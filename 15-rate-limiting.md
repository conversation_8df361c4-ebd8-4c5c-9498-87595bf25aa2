# Module 15: Rate Limiting

## Learning Objectives
- Implement comprehensive rate limiting and traffic shaping for DoS protection
- Design token bucket, sliding window, and fixed window algorithms
- Build distributed rate limiting with Redis and consistent enforcement
- Create adaptive rate limiting based on server load and performance
- Understand rate limiting patterns and anti-abuse strategies

## Why Rate Limiting is Critical

Rate limiting protects services from abuse and ensures fair resource allocation:

1. **DoS Protection**: Prevent denial-of-service attacks and traffic spikes
2. **Resource Management**: Ensure fair access to limited server resources
3. **Cost Control**: Manage API usage and prevent unexpected costs
4. **Quality of Service**: Maintain performance for legitimate users
5. **Compliance**: Meet SLA requirements and regulatory constraints

### Rate Limiting Architecture

```mermaid
graph TD
    Client[Client Requests] --> LB[Load Balancer]
    LB --> RL[Rate Limiter]
    
    RL --> TB[Token Bucket]
    RL --> SW[Sliding Window]
    RL --> FW[Fixed Window]
    RL --> AL[Adaptive Limiter]
    
    subgraph "Rate Limiting Storage"
        Memory[In-Memory Store]
        Redis[Redis Cluster]
        Database[Rate Limit DB]
    end
    
    TB --> Memory
    SW --> Redis
    FW --> Database
    AL --> Memory
    
    subgraph "Rate Limit Policies"
        Global[Global Limits]
        PerIP[Per-IP Limits]
        PerUser[Per-User Limits]
        PerEndpoint[Per-Endpoint Limits]
    end
    
    RL --> Global
    RL --> PerIP
    RL --> PerUser
    RL --> PerEndpoint
    
    subgraph "Enforcement Actions"
        Allow[Allow Request]
        Throttle[Throttle Request]
        Block[Block Request]
        Queue[Queue Request]
    end
    
    RL --> Allow
    RL --> Throttle
    RL --> Block
    RL --> Queue
    
    subgraph "Monitoring & Analytics"
        Metrics[Rate Limit Metrics]
        Alerts[Abuse Alerts]
        Reports[Usage Reports]
    end
    
    RL --> Metrics
    Metrics --> Alerts
    Metrics --> Reports
    
    Allow --> Backend[Backend Servers]
    Throttle --> Backend
    Block --> ErrorResponse[Error Response]
    Queue --> DelayedBackend[Delayed Backend]
```

## Rate Limiting Implementation

### Design Decisions

**Why multiple rate limiting algorithms?**
- **Flexibility**: Different algorithms suit different use cases and traffic patterns
- **Accuracy**: Choose optimal algorithm based on precision requirements
- **Performance**: Balance between accuracy and computational overhead
- **Burst Handling**: Different approaches to handling traffic bursts

**Why distributed rate limiting?**
- **Consistency**: Ensure limits are enforced across multiple load balancer instances
- **Scalability**: Handle high-volume traffic with distributed enforcement
- **Accuracy**: Prevent limit bypass through multiple entry points
- **Coordination**: Synchronize rate limit state across the cluster

Create `src/ratelimit/mod.rs`:

```rust
//! Comprehensive rate limiting and traffic shaping system
//!
//! This module provides advanced rate limiting capabilities including:
//! - Multiple algorithms (token bucket, sliding window, fixed window)
//! - Distributed rate limiting with Redis backend
//! - Adaptive rate limiting based on server load
//! - Per-IP, per-user, and per-endpoint rate limiting
//! - Traffic shaping and request queuing

pub mod algorithms;
pub mod storage;
pub mod policies;
pub mod adaptive;

use crate::error::{LoadBalancerError, Result};
use crate::metrics::LoadBalancerMetrics;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::RwLock;
use tracing::{info, warn, debug, trace};

/// Rate limiter with multiple algorithms and policies
pub struct RateLimiter {
    /// Rate limiting policies
    policies: Arc<RwLock<Vec<RateLimitPolicy>>>,
    
    /// Storage backend for rate limit state
    storage: Arc<dyn RateLimitStorage + Send + Sync>,
    
    /// Adaptive rate limiter
    adaptive_limiter: Option<Arc<AdaptiveRateLimiter>>,
    
    /// Metrics collector
    metrics: Option<Arc<LoadBalancerMetrics>>,
    
    /// Configuration
    config: RateLimitConfig,
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Enable rate limiting
    pub enabled: bool,
    
    /// Default algorithm
    pub default_algorithm: RateLimitAlgorithm,
    
    /// Enable distributed rate limiting
    pub distributed: bool,
    
    /// Redis connection string (for distributed limiting)
    pub redis_url: Option<String>,
    
    /// Enable adaptive rate limiting
    pub enable_adaptive: bool,
    
    /// Adaptive adjustment interval
    pub adaptive_interval: Duration,
    
    /// Enable request queuing for throttled requests
    pub enable_queuing: bool,
    
    /// Maximum queue size
    pub max_queue_size: usize,
    
    /// Queue timeout
    pub queue_timeout: Duration,
    
    /// Enable rate limit headers in responses
    pub include_headers: bool,
    
    /// Cleanup interval for expired entries
    pub cleanup_interval: Duration,
}

/// Rate limiting algorithms
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum RateLimitAlgorithm {
    TokenBucket,
    SlidingWindow,
    FixedWindow,
    SlidingWindowLog,
}

/// Rate limiting policy
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitPolicy {
    /// Policy name
    pub name: String,
    
    /// Policy scope
    pub scope: RateLimitScope,
    
    /// Rate limit (requests per time window)
    pub limit: u64,
    
    /// Time window
    pub window: Duration,
    
    /// Burst capacity (for token bucket)
    pub burst: Option<u64>,
    
    /// Algorithm to use
    pub algorithm: RateLimitAlgorithm,
    
    /// Action to take when limit exceeded
    pub action: RateLimitAction,
    
    /// Policy priority (higher number = higher priority)
    pub priority: u32,
    
    /// Policy conditions
    pub conditions: Vec<RateLimitCondition>,
}

/// Rate limiting scope
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RateLimitScope {
    Global,
    PerIP,
    PerUser { user_header: String },
    PerEndpoint { path_pattern: String },
    PerHeader { header_name: String },
    Custom { key_extractor: String },
}

/// Actions to take when rate limit is exceeded
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RateLimitAction {
    /// Block the request immediately
    Block,
    
    /// Throttle the request (add delay)
    Throttle { delay: Duration },
    
    /// Queue the request for later processing
    Queue,
    
    /// Allow but log the violation
    LogOnly,
}

/// Conditions for applying rate limit policies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RateLimitCondition {
    /// Apply to specific HTTP methods
    Method { methods: Vec<String> },
    
    /// Apply to specific paths
    Path { patterns: Vec<String> },
    
    /// Apply to specific headers
    Header { name: String, values: Vec<String> },
    
    /// Apply during specific time ranges
    TimeRange { start: String, end: String },
    
    /// Apply based on server load
    ServerLoad { threshold: f64 },
}

/// Rate limit check result
#[derive(Debug, Clone)]
pub struct RateLimitResult {
    /// Whether the request is allowed
    pub allowed: bool,
    
    /// Action to take
    pub action: RateLimitAction,
    
    /// Remaining requests in current window
    pub remaining: u64,
    
    /// Time until window resets
    pub reset_time: Duration,
    
    /// Retry after duration (for blocked requests)
    pub retry_after: Option<Duration>,
    
    /// Policy that was applied
    pub policy_name: String,
    
    /// Rate limit headers to include in response
    pub headers: HashMap<String, String>,
}

/// Request context for rate limiting
#[derive(Debug, Clone)]
pub struct RateLimitContext {
    /// Client IP address
    pub client_ip: IpAddr,
    
    /// User identifier (if available)
    pub user_id: Option<String>,
    
    /// HTTP method
    pub method: String,
    
    /// Request path
    pub path: String,
    
    /// Request headers
    pub headers: HashMap<String, String>,
    
    /// Request timestamp
    pub timestamp: SystemTime,
}

impl RateLimiter {
    /// Create a new rate limiter
    pub async fn new(config: RateLimitConfig) -> Result<Self> {
        // Create storage backend
        let storage: Arc<dyn RateLimitStorage + Send + Sync> = if config.distributed {
            if let Some(ref redis_url) = config.redis_url {
                Arc::new(RedisRateLimitStorage::new(redis_url).await?)
            } else {
                return Err(LoadBalancerError::Configuration {
                    message: "Redis URL required for distributed rate limiting".to_string(),
                    field: Some("redis_url".to_string()),
                    value: None,
                });
            }
        } else {
            Arc::new(MemoryRateLimitStorage::new())
        };
        
        // Create adaptive limiter if enabled
        let adaptive_limiter = if config.enable_adaptive {
            Some(Arc::new(AdaptiveRateLimiter::new(config.adaptive_interval)))
        } else {
            None
        };
        
        info!("🚦 Rate limiter initialized (distributed: {}, adaptive: {})", 
              config.distributed, config.enable_adaptive);
        
        Ok(Self {
            policies: Arc::new(RwLock::new(Vec::new())),
            storage,
            adaptive_limiter,
            metrics: None,
            config,
        })
    }
    
    /// Set metrics collector
    pub fn with_metrics(mut self, metrics: Arc<LoadBalancerMetrics>) -> Self {
        self.metrics = Some(metrics);
        self
    }
    
    /// Add a rate limiting policy
    pub async fn add_policy(&self, policy: RateLimitPolicy) -> Result<()> {
        let mut policies = self.policies.write().await;
        
        // Insert policy in priority order (higher priority first)
        let insert_pos = policies.iter()
            .position(|p| p.priority < policy.priority)
            .unwrap_or(policies.len());
        
        policies.insert(insert_pos, policy.clone());
        
        info!("📋 Added rate limit policy: {} (priority: {})", policy.name, policy.priority);
        Ok(())
    }
    
    /// Remove a rate limiting policy
    pub async fn remove_policy(&self, policy_name: &str) -> Result<()> {
        let mut policies = self.policies.write().await;
        
        if let Some(pos) = policies.iter().position(|p| p.name == policy_name) {
            policies.remove(pos);
            info!("🗑️ Removed rate limit policy: {}", policy_name);
            Ok(())
        } else {
            Err(LoadBalancerError::Configuration {
                message: format!("Rate limit policy '{}' not found", policy_name),
                field: Some("policy_name".to_string()),
                value: Some(policy_name.to_string()),
            })
        }
    }
    
    /// Check if request should be rate limited
    pub async fn check_rate_limit(&self, context: &RateLimitContext) -> Result<RateLimitResult> {
        if !self.config.enabled {
            return Ok(RateLimitResult {
                allowed: true,
                action: RateLimitAction::LogOnly,
                remaining: u64::MAX,
                reset_time: Duration::from_secs(0),
                retry_after: None,
                policy_name: "disabled".to_string(),
                headers: HashMap::new(),
            });
        }
        
        let policies = self.policies.read().await;
        
        // Check each policy in priority order
        for policy in policies.iter() {
            if self.policy_matches(policy, context).await? {
                let result = self.check_policy_limit(policy, context).await?;
                
                if !result.allowed {
                    // Record rate limit violation
                    if let Some(ref metrics) = self.metrics {
                        metrics.record_rate_limit_hit(
                            &context.client_ip.to_string(),
                            &policy.name,
                        );
                    }
                    
                    warn!("🚫 Rate limit exceeded for {} (policy: {})", 
                          context.client_ip, policy.name);
                    
                    return Ok(result);
                }
            }
        }
        
        // No policies matched or all policies allowed the request
        Ok(RateLimitResult {
            allowed: true,
            action: RateLimitAction::LogOnly,
            remaining: u64::MAX,
            reset_time: Duration::from_secs(0),
            retry_after: None,
            policy_name: "none".to_string(),
            headers: HashMap::new(),
        })
    }
    
    /// Check if a policy matches the request context
    async fn policy_matches(&self, policy: &RateLimitPolicy, context: &RateLimitContext) -> Result<bool> {
        // Check all conditions
        for condition in &policy.conditions {
            if !self.condition_matches(condition, context).await? {
                return Ok(false);
            }
        }
        
        Ok(true)
    }
    
    /// Check if a condition matches the request context
    async fn condition_matches(&self, condition: &RateLimitCondition, context: &RateLimitContext) -> Result<bool> {
        match condition {
            RateLimitCondition::Method { methods } => {
                Ok(methods.contains(&context.method))
            }
            RateLimitCondition::Path { patterns } => {
                // Simple pattern matching (in production, use regex or glob)
                Ok(patterns.iter().any(|pattern| context.path.contains(pattern)))
            }
            RateLimitCondition::Header { name, values } => {
                if let Some(header_value) = context.headers.get(name) {
                    Ok(values.contains(header_value))
                } else {
                    Ok(false)
                }
            }
            RateLimitCondition::TimeRange { start: _, end: _ } => {
                // Time range checking would be implemented here
                Ok(true)
            }
            RateLimitCondition::ServerLoad { threshold } => {
                // Check current server load
                if let Some(ref adaptive_limiter) = self.adaptive_limiter {
                    let current_load = adaptive_limiter.get_current_load().await;
                    Ok(current_load > *threshold)
                } else {
                    Ok(false)
                }
            }
        }
    }
    
    /// Check rate limit for a specific policy
    async fn check_policy_limit(&self, policy: &RateLimitPolicy, context: &RateLimitContext) -> Result<RateLimitResult> {
        // Generate rate limit key based on policy scope
        let key = self.generate_rate_limit_key(policy, context);
        
        // Check rate limit using the specified algorithm
        let (allowed, remaining, reset_time) = match policy.algorithm {
            RateLimitAlgorithm::TokenBucket => {
                self.check_token_bucket(&key, policy).await?
            }
            RateLimitAlgorithm::SlidingWindow => {
                self.check_sliding_window(&key, policy).await?
            }
            RateLimitAlgorithm::FixedWindow => {
                self.check_fixed_window(&key, policy).await?
            }
            RateLimitAlgorithm::SlidingWindowLog => {
                self.check_sliding_window_log(&key, policy).await?
            }
        };
        
        // Generate response headers if enabled
        let headers = if self.config.include_headers {
            let mut headers = HashMap::new();
            headers.insert("X-RateLimit-Limit".to_string(), policy.limit.to_string());
            headers.insert("X-RateLimit-Remaining".to_string(), remaining.to_string());
            headers.insert("X-RateLimit-Reset".to_string(), 
                          (SystemTime::now() + reset_time).duration_since(SystemTime::UNIX_EPOCH)
                          .unwrap_or_default().as_secs().to_string());
            
            if !allowed {
                if let RateLimitAction::Throttle { delay } = &policy.action {
                    headers.insert("Retry-After".to_string(), delay.as_secs().to_string());
                }
            }
            
            headers
        } else {
            HashMap::new()
        };
        
        let retry_after = if !allowed {
            match &policy.action {
                RateLimitAction::Throttle { delay } => Some(*delay),
                RateLimitAction::Block => Some(reset_time),
                _ => None,
            }
        } else {
            None
        };
        
        Ok(RateLimitResult {
            allowed,
            action: policy.action.clone(),
            remaining,
            reset_time,
            retry_after,
            policy_name: policy.name.clone(),
            headers,
        })
    }
    
    /// Generate rate limit key based on policy scope
    fn generate_rate_limit_key(&self, policy: &RateLimitPolicy, context: &RateLimitContext) -> String {
        match &policy.scope {
            RateLimitScope::Global => {
                format!("global:{}", policy.name)
            }
            RateLimitScope::PerIP => {
                format!("ip:{}:{}", context.client_ip, policy.name)
            }
            RateLimitScope::PerUser { user_header } => {
                let user_id = context.headers.get(user_header)
                    .or(context.user_id.as_ref())
                    .unwrap_or(&"anonymous".to_string());
                format!("user:{}:{}", user_id, policy.name)
            }
            RateLimitScope::PerEndpoint { path_pattern } => {
                format!("endpoint:{}:{}", path_pattern, policy.name)
            }
            RateLimitScope::PerHeader { header_name } => {
                let header_value = context.headers.get(header_name)
                    .unwrap_or(&"unknown".to_string());
                format!("header:{}:{}:{}", header_name, header_value, policy.name)
            }
            RateLimitScope::Custom { key_extractor } => {
                // Custom key extraction logic would be implemented here
                format!("custom:{}:{}", key_extractor, policy.name)
            }
        }
    }
    
    /// Check token bucket rate limit
    async fn check_token_bucket(&self, key: &str, policy: &RateLimitPolicy) -> Result<(bool, u64, Duration)> {
        let bucket_capacity = policy.burst.unwrap_or(policy.limit);
        let refill_rate = policy.limit as f64 / policy.window.as_secs_f64();
        
        let (tokens, last_refill) = self.storage.get_token_bucket(key).await?
            .unwrap_or((bucket_capacity, Instant::now()));
        
        // Calculate tokens to add based on time elapsed
        let now = Instant::now();
        let elapsed = now.duration_since(last_refill).as_secs_f64();
        let tokens_to_add = (elapsed * refill_rate) as u64;
        let current_tokens = (tokens + tokens_to_add).min(bucket_capacity);
        
        if current_tokens > 0 {
            // Consume one token
            let new_tokens = current_tokens - 1;
            self.storage.set_token_bucket(key, new_tokens, now).await?;
            
            let reset_time = if new_tokens == 0 {
                Duration::from_secs_f64(1.0 / refill_rate)
            } else {
                Duration::from_secs(0)
            };
            
            Ok((true, new_tokens, reset_time))
        } else {
            // No tokens available
            let reset_time = Duration::from_secs_f64(1.0 / refill_rate);
            Ok((false, 0, reset_time))
        }
    }
    
    /// Check sliding window rate limit
    async fn check_sliding_window(&self, key: &str, policy: &RateLimitPolicy) -> Result<(bool, u64, Duration)> {
        let window_size = policy.window;
        let now = Instant::now();
        let window_start = now - window_size;
        
        // Get current count in the sliding window
        let current_count = self.storage.get_sliding_window_count(key, window_start, now).await?;
        
        if current_count < policy.limit {
            // Add current request to the window
            self.storage.add_sliding_window_entry(key, now).await?;
            
            let remaining = policy.limit - current_count - 1;
            let reset_time = window_size;
            
            Ok((true, remaining, reset_time))
        } else {
            // Rate limit exceeded
            let reset_time = window_size;
            Ok((false, 0, reset_time))
        }
    }
    
    /// Check fixed window rate limit
    async fn check_fixed_window(&self, key: &str, policy: &RateLimitPolicy) -> Result<(bool, u64, Duration)> {
        let window_size = policy.window.as_secs();
        let now = SystemTime::now().duration_since(SystemTime::UNIX_EPOCH)
            .unwrap_or_default().as_secs();
        let window_start = (now / window_size) * window_size;
        let window_key = format!("{}:{}", key, window_start);
        
        let current_count = self.storage.get_fixed_window_count(&window_key).await?;
        
        if current_count < policy.limit {
            // Increment counter for this window
            self.storage.increment_fixed_window(&window_key, policy.window).await?;
            
            let remaining = policy.limit - current_count - 1;
            let reset_time = Duration::from_secs((window_start + window_size) - now);
            
            Ok((true, remaining, reset_time))
        } else {
            // Rate limit exceeded
            let reset_time = Duration::from_secs((window_start + window_size) - now);
            Ok((false, 0, reset_time))
        }
    }
    
    /// Check sliding window log rate limit
    async fn check_sliding_window_log(&self, key: &str, policy: &RateLimitPolicy) -> Result<(bool, u64, Duration)> {
        // Similar to sliding window but with more precise tracking
        // This would maintain a log of all requests in the window
        self.check_sliding_window(key, policy).await
    }
    
    /// Start background tasks
    pub async fn start(&self) -> Result<()> {
        // Start cleanup task
        self.start_cleanup_task().await;
        
        // Start adaptive adjustment task if enabled
        if let Some(ref adaptive_limiter) = self.adaptive_limiter {
            adaptive_limiter.start().await?;
        }
        
        info!("✅ Rate limiter background tasks started");
        Ok(())
    }
    
    /// Start cleanup task for expired entries
    async fn start_cleanup_task(&self) {
        let storage = self.storage.clone();
        let cleanup_interval = self.config.cleanup_interval;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(cleanup_interval);
            
            loop {
                interval.tick().await;
                
                if let Err(e) = storage.cleanup_expired().await {
                    warn!("Failed to cleanup expired rate limit entries: {}", e);
                }
            }
        });
    }
    
    /// Get rate limiting statistics
    pub async fn get_stats(&self) -> RateLimitStats {
        let policies = self.policies.read().await;
        
        RateLimitStats {
            total_policies: policies.len(),
            enabled: self.config.enabled,
            distributed: self.config.distributed,
            adaptive_enabled: self.adaptive_limiter.is_some(),
        }
    }
}

/// Rate limiting statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitStats {
    pub total_policies: usize,
    pub enabled: bool,
    pub distributed: bool,
    pub adaptive_enabled: bool,
}

/// Trait for rate limit storage backends
#[async_trait::async_trait]
pub trait RateLimitStorage {
    /// Get token bucket state
    async fn get_token_bucket(&self, key: &str) -> Result<Option<(u64, Instant)>>;
    
    /// Set token bucket state
    async fn set_token_bucket(&self, key: &str, tokens: u64, last_refill: Instant) -> Result<()>;
    
    /// Get sliding window count
    async fn get_sliding_window_count(&self, key: &str, start: Instant, end: Instant) -> Result<u64>;
    
    /// Add sliding window entry
    async fn add_sliding_window_entry(&self, key: &str, timestamp: Instant) -> Result<()>;
    
    /// Get fixed window count
    async fn get_fixed_window_count(&self, key: &str) -> Result<u64>;
    
    /// Increment fixed window counter
    async fn increment_fixed_window(&self, key: &str, ttl: Duration) -> Result<()>;
    
    /// Cleanup expired entries
    async fn cleanup_expired(&self) -> Result<()>;
}

/// In-memory rate limit storage
pub struct MemoryRateLimitStorage {
    token_buckets: Arc<RwLock<HashMap<String, (u64, Instant)>>>,
    sliding_windows: Arc<RwLock<HashMap<String, Vec<Instant>>>>,
    fixed_windows: Arc<RwLock<HashMap<String, (u64, SystemTime)>>>,
}

impl MemoryRateLimitStorage {
    pub fn new() -> Self {
        Self {
            token_buckets: Arc::new(RwLock::new(HashMap::new())),
            sliding_windows: Arc::new(RwLock::new(HashMap::new())),
            fixed_windows: Arc::new(RwLock::new(HashMap::new())),
        }
    }
}

#[async_trait::async_trait]
impl RateLimitStorage for MemoryRateLimitStorage {
    async fn get_token_bucket(&self, key: &str) -> Result<Option<(u64, Instant)>> {
        let buckets = self.token_buckets.read().await;
        Ok(buckets.get(key).copied())
    }
    
    async fn set_token_bucket(&self, key: &str, tokens: u64, last_refill: Instant) -> Result<()> {
        let mut buckets = self.token_buckets.write().await;
        buckets.insert(key.to_string(), (tokens, last_refill));
        Ok(())
    }
    
    async fn get_sliding_window_count(&self, key: &str, start: Instant, _end: Instant) -> Result<u64> {
        let windows = self.sliding_windows.read().await;
        if let Some(entries) = windows.get(key) {
            let count = entries.iter().filter(|&&timestamp| timestamp >= start).count();
            Ok(count as u64)
        } else {
            Ok(0)
        }
    }
    
    async fn add_sliding_window_entry(&self, key: &str, timestamp: Instant) -> Result<()> {
        let mut windows = self.sliding_windows.write().await;
        windows.entry(key.to_string()).or_insert_with(Vec::new).push(timestamp);
        Ok(())
    }
    
    async fn get_fixed_window_count(&self, key: &str) -> Result<u64> {
        let windows = self.fixed_windows.read().await;
        Ok(windows.get(key).map(|(count, _)| *count).unwrap_or(0))
    }
    
    async fn increment_fixed_window(&self, key: &str, _ttl: Duration) -> Result<()> {
        let mut windows = self.fixed_windows.write().await;
        let entry = windows.entry(key.to_string()).or_insert((0, SystemTime::now()));
        entry.0 += 1;
        Ok(())
    }
    
    async fn cleanup_expired(&self) -> Result<()> {
        // Cleanup logic would be implemented here
        Ok(())
    }
}

/// Redis-based distributed rate limit storage
pub struct RedisRateLimitStorage {
    // Redis client would be stored here
    _redis_url: String,
}

impl RedisRateLimitStorage {
    pub async fn new(redis_url: &str) -> Result<Self> {
        // Initialize Redis connection
        info!("🔗 Connecting to Redis for distributed rate limiting: {}", redis_url);
        
        Ok(Self {
            _redis_url: redis_url.to_string(),
        })
    }
}

#[async_trait::async_trait]
impl RateLimitStorage for RedisRateLimitStorage {
    async fn get_token_bucket(&self, _key: &str) -> Result<Option<(u64, Instant)>> {
        // Redis implementation would go here
        Ok(None)
    }
    
    async fn set_token_bucket(&self, _key: &str, _tokens: u64, _last_refill: Instant) -> Result<()> {
        // Redis implementation would go here
        Ok(())
    }
    
    async fn get_sliding_window_count(&self, _key: &str, _start: Instant, _end: Instant) -> Result<u64> {
        // Redis implementation would go here
        Ok(0)
    }
    
    async fn add_sliding_window_entry(&self, _key: &str, _timestamp: Instant) -> Result<()> {
        // Redis implementation would go here
        Ok(())
    }
    
    async fn get_fixed_window_count(&self, _key: &str) -> Result<u64> {
        // Redis implementation would go here
        Ok(0)
    }
    
    async fn increment_fixed_window(&self, _key: &str, _ttl: Duration) -> Result<()> {
        // Redis implementation would go here
        Ok(())
    }
    
    async fn cleanup_expired(&self) -> Result<()> {
        // Redis implementation would go here
        Ok(())
    }
}

/// Adaptive rate limiter that adjusts limits based on server load
pub struct AdaptiveRateLimiter {
    /// Current server load (0.0 - 1.0)
    current_load: Arc<RwLock<f64>>,
    
    /// Adjustment interval
    adjustment_interval: Duration,
}

impl AdaptiveRateLimiter {
    pub fn new(adjustment_interval: Duration) -> Self {
        Self {
            current_load: Arc::new(RwLock::new(0.0)),
            adjustment_interval,
        }
    }
    
    pub async fn start(&self) -> Result<()> {
        let current_load = self.current_load.clone();
        let adjustment_interval = self.adjustment_interval;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(adjustment_interval);
            
            loop {
                interval.tick().await;
                
                // Monitor server load and adjust rate limits
                let load = Self::measure_server_load().await;
                {
                    let mut current = current_load.write().await;
                    *current = load;
                }
                
                trace!("Server load: {:.2}", load);
            }
        });
        
        Ok(())
    }
    
    pub async fn get_current_load(&self) -> f64 {
        *self.current_load.read().await
    }
    
    async fn measure_server_load() -> f64 {
        // In a real implementation, this would measure actual server metrics
        // For now, simulate varying load
        use rand::Rng;
        let mut rng = rand::thread_rng();
        rng.gen_range(0.0..1.0)
    }
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            default_algorithm: RateLimitAlgorithm::TokenBucket,
            distributed: false,
            redis_url: None,
            enable_adaptive: false,
            adaptive_interval: Duration::from_secs(30),
            enable_queuing: false,
            max_queue_size: 1000,
            queue_timeout: Duration::from_secs(30),
            include_headers: true,
            cleanup_interval: Duration::from_secs(300),
        }
    }
}
```

## Integration Example

Create `examples/rate_limiting_example.rs`:

```rust
//! Example demonstrating comprehensive rate limiting with multiple policies

use rusty_balancer::ratelimit::{
    RateLimiter, RateLimitConfig, RateLimitPolicy, RateLimitScope,
    RateLimitAlgorithm, RateLimitAction, RateLimitCondition, RateLimitContext
};
use std::collections::HashMap;
use std::net::IpAddr;
use std::time::{Duration, SystemTime};
use tracing::{info, warn};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("🚦 Starting Rate Limiting Example");

    // Create rate limiting configuration
    let config = RateLimitConfig {
        enabled: true,
        default_algorithm: RateLimitAlgorithm::TokenBucket,
        distributed: false, // Use in-memory for this example
        enable_adaptive: true,
        adaptive_interval: Duration::from_secs(10),
        include_headers: true,
        cleanup_interval: Duration::from_secs(60),
        ..Default::default()
    };

    // Create rate limiter
    let rate_limiter = RateLimiter::new(config).await?;
    rate_limiter.start().await?;

    // Define rate limiting policies
    let policies = vec![
        // Global rate limit - 1000 requests per minute
        RateLimitPolicy {
            name: "global_limit".to_string(),
            scope: RateLimitScope::Global,
            limit: 1000,
            window: Duration::from_secs(60),
            burst: Some(1200),
            algorithm: RateLimitAlgorithm::TokenBucket,
            action: RateLimitAction::Block,
            priority: 1,
            conditions: vec![],
        },

        // Per-IP rate limit - 100 requests per minute
        RateLimitPolicy {
            name: "per_ip_limit".to_string(),
            scope: RateLimitScope::PerIP,
            limit: 100,
            window: Duration::from_secs(60),
            burst: Some(120),
            algorithm: RateLimitAlgorithm::TokenBucket,
            action: RateLimitAction::Throttle { delay: Duration::from_millis(500) },
            priority: 2,
            conditions: vec![],
        },

        // API endpoint rate limit - 50 requests per minute for /api/upload
        RateLimitPolicy {
            name: "upload_endpoint_limit".to_string(),
            scope: RateLimitScope::PerEndpoint { path_pattern: "/api/upload".to_string() },
            limit: 50,
            window: Duration::from_secs(60),
            burst: None,
            algorithm: RateLimitAlgorithm::SlidingWindow,
            action: RateLimitAction::Block,
            priority: 3,
            conditions: vec![
                RateLimitCondition::Method { methods: vec!["POST".to_string()] },
            ],
        },

        // User-based rate limit - 200 requests per hour
        RateLimitPolicy {
            name: "per_user_limit".to_string(),
            scope: RateLimitScope::PerUser { user_header: "X-User-ID".to_string() },
            limit: 200,
            window: Duration::from_secs(3600),
            burst: None,
            algorithm: RateLimitAlgorithm::FixedWindow,
            action: RateLimitAction::Queue,
            priority: 4,
            conditions: vec![],
        },

        // Adaptive rate limit based on server load
        RateLimitPolicy {
            name: "adaptive_limit".to_string(),
            scope: RateLimitScope::Global,
            limit: 500,
            window: Duration::from_secs(60),
            burst: None,
            algorithm: RateLimitAlgorithm::TokenBucket,
            action: RateLimitAction::Throttle { delay: Duration::from_secs(1) },
            priority: 5,
            conditions: vec![
                RateLimitCondition::ServerLoad { threshold: 0.8 },
            ],
        },
    ];

    // Add policies to rate limiter
    for policy in policies {
        rate_limiter.add_policy(policy).await?;
    }

    info!("📋 Added {} rate limiting policies", 5);

    // Simulate different types of requests
    let test_scenarios = vec![
        // Normal user requests
        ("***********00", "user123", "GET", "/api/data", false),
        ("***********00", "user123", "GET", "/api/data", false),
        ("***********00", "user123", "GET", "/api/data", false),

        // Upload requests (more restrictive)
        ("***********01", "user456", "POST", "/api/upload", false),
        ("***********01", "user456", "POST", "/api/upload", false),
        ("***********01", "user456", "POST", "/api/upload", false),

        // Burst of requests from same IP
        ("***********02", "user789", "GET", "/api/search", true),
        ("***********02", "user789", "GET", "/api/search", true),
        ("***********02", "user789", "GET", "/api/search", true),
        ("***********02", "user789", "GET", "/api/search", true),
        ("***********02", "user789", "GET", "/api/search", true),
    ];

    for (i, (ip, user_id, method, path, is_burst)) in test_scenarios.iter().enumerate() {
        let mut headers = HashMap::new();
        headers.insert("X-User-ID".to_string(), user_id.to_string());
        headers.insert("User-Agent".to_string(), "Test Client/1.0".to_string());

        let context = RateLimitContext {
            client_ip: ip.parse::<IpAddr>()?,
            user_id: Some(user_id.to_string()),
            method: method.to_string(),
            path: path.to_string(),
            headers,
            timestamp: SystemTime::now(),
        };

        let result = rate_limiter.check_rate_limit(&context).await?;

        let status = if result.allowed { "✅ ALLOWED" } else { "🚫 BLOCKED" };
        let action_desc = match result.action {
            RateLimitAction::Block => "Block",
            RateLimitAction::Throttle { delay } => &format!("Throttle ({}ms)", delay.as_millis()),
            RateLimitAction::Queue => "Queue",
            RateLimitAction::LogOnly => "Log Only",
        };

        info!(
            "Request {}: {} {} {} -> {} (Policy: {}, Remaining: {}, Action: {})",
            i + 1, method, path, ip, status, result.policy_name, result.remaining, action_desc
        );

        // Show rate limit headers
        if !result.headers.is_empty() {
            info!("  Headers: {:?}", result.headers);
        }

        if *is_burst {
            // Small delay for burst requests to show rate limiting in action
            tokio::time::sleep(Duration::from_millis(100)).await;
        } else {
            // Longer delay for normal requests
            tokio::time::sleep(Duration::from_millis(500)).await;
        }
    }

    // Test policy management
    info!("\n🔧 Testing policy management:");

    // Remove a policy
    rate_limiter.remove_policy("upload_endpoint_limit").await?;
    info!("Removed upload endpoint limit policy");

    // Add a new policy
    let new_policy = RateLimitPolicy {
        name: "strict_upload_limit".to_string(),
        scope: RateLimitScope::PerEndpoint { path_pattern: "/api/upload".to_string() },
        limit: 10, // Much more restrictive
        window: Duration::from_secs(60),
        burst: None,
        algorithm: RateLimitAlgorithm::FixedWindow,
        action: RateLimitAction::Block,
        priority: 10, // Higher priority
        conditions: vec![
            RateLimitCondition::Method { methods: vec!["POST".to_string()] },
        ],
    };

    rate_limiter.add_policy(new_policy).await?;
    info!("Added strict upload limit policy");

    // Test the new policy
    let upload_context = RateLimitContext {
        client_ip: "***********00".parse()?,
        user_id: Some("test_user".to_string()),
        method: "POST".to_string(),
        path: "/api/upload".to_string(),
        headers: HashMap::new(),
        timestamp: SystemTime::now(),
    };

    let result = rate_limiter.check_rate_limit(&upload_context).await?;
    let status = if result.allowed { "✅ ALLOWED" } else { "🚫 BLOCKED" };
    info!("Upload request with new policy: {} (Policy: {})", status, result.policy_name);

    // Show rate limiter statistics
    let stats = rate_limiter.get_stats().await;
    info!("\n📊 Rate Limiter Statistics:");
    info!("  Total policies: {}", stats.total_policies);
    info!("  Enabled: {}", stats.enabled);
    info!("  Distributed: {}", stats.distributed);
    info!("  Adaptive enabled: {}", stats.adaptive_enabled);

    info!("✅ Rate limiting example completed");

    Ok(())
}
```

## Testing Rate Limiting

Create `tests/rate_limiting_tests.rs`:

```rust
use rusty_balancer::ratelimit::{
    RateLimiter, RateLimitConfig, RateLimitPolicy, RateLimitScope,
    RateLimitAlgorithm, RateLimitAction, RateLimitContext
};
use std::collections::HashMap;
use std::net::IpAddr;
use std::time::{Duration, SystemTime};

#[tokio::test]
async fn test_rate_limiter_creation() {
    let config = RateLimitConfig::default();
    let rate_limiter = RateLimiter::new(config).await.unwrap();

    let stats = rate_limiter.get_stats().await;
    assert_eq!(stats.total_policies, 0);
    assert!(stats.enabled);
}

#[tokio::test]
async fn test_token_bucket_algorithm() {
    let config = RateLimitConfig::default();
    let rate_limiter = RateLimiter::new(config).await.unwrap();

    let policy = RateLimitPolicy {
        name: "test_policy".to_string(),
        scope: RateLimitScope::PerIP,
        limit: 5,
        window: Duration::from_secs(60),
        burst: Some(10),
        algorithm: RateLimitAlgorithm::TokenBucket,
        action: RateLimitAction::Block,
        priority: 1,
        conditions: vec![],
    };

    rate_limiter.add_policy(policy).await.unwrap();

    let context = RateLimitContext {
        client_ip: "***********".parse().unwrap(),
        user_id: None,
        method: "GET".to_string(),
        path: "/test".to_string(),
        headers: HashMap::new(),
        timestamp: SystemTime::now(),
    };

    // First few requests should be allowed
    for i in 0..5 {
        let result = rate_limiter.check_rate_limit(&context).await.unwrap();
        assert!(result.allowed, "Request {} should be allowed", i);
    }
}

#[tokio::test]
async fn test_per_ip_rate_limiting() {
    let config = RateLimitConfig::default();
    let rate_limiter = RateLimiter::new(config).await.unwrap();

    let policy = RateLimitPolicy {
        name: "per_ip_test".to_string(),
        scope: RateLimitScope::PerIP,
        limit: 3,
        window: Duration::from_secs(60),
        burst: Some(3),
        algorithm: RateLimitAlgorithm::TokenBucket,
        action: RateLimitAction::Block,
        priority: 1,
        conditions: vec![],
    };

    rate_limiter.add_policy(policy).await.unwrap();

    let ip1_context = RateLimitContext {
        client_ip: "***********".parse().unwrap(),
        user_id: None,
        method: "GET".to_string(),
        path: "/test".to_string(),
        headers: HashMap::new(),
        timestamp: SystemTime::now(),
    };

    let ip2_context = RateLimitContext {
        client_ip: "***********".parse().unwrap(),
        user_id: None,
        method: "GET".to_string(),
        path: "/test".to_string(),
        headers: HashMap::new(),
        timestamp: SystemTime::now(),
    };

    // Both IPs should have independent rate limits
    for _ in 0..3 {
        let result1 = rate_limiter.check_rate_limit(&ip1_context).await.unwrap();
        let result2 = rate_limiter.check_rate_limit(&ip2_context).await.unwrap();

        assert!(result1.allowed);
        assert!(result2.allowed);
    }

    // Fourth request from each IP should be blocked
    let result1 = rate_limiter.check_rate_limit(&ip1_context).await.unwrap();
    let result2 = rate_limiter.check_rate_limit(&ip2_context).await.unwrap();

    assert!(!result1.allowed);
    assert!(!result2.allowed);
}

#[tokio::test]
async fn test_policy_priority() {
    let config = RateLimitConfig::default();
    let rate_limiter = RateLimiter::new(config).await.unwrap();

    // Add two policies with different priorities
    let low_priority_policy = RateLimitPolicy {
        name: "low_priority".to_string(),
        scope: RateLimitScope::Global,
        limit: 100,
        window: Duration::from_secs(60),
        burst: Some(100),
        algorithm: RateLimitAlgorithm::TokenBucket,
        action: RateLimitAction::LogOnly,
        priority: 1,
        conditions: vec![],
    };

    let high_priority_policy = RateLimitPolicy {
        name: "high_priority".to_string(),
        scope: RateLimitScope::Global,
        limit: 5,
        window: Duration::from_secs(60),
        burst: Some(5),
        algorithm: RateLimitAlgorithm::TokenBucket,
        action: RateLimitAction::Block,
        priority: 10,
        conditions: vec![],
    };

    rate_limiter.add_policy(low_priority_policy).await.unwrap();
    rate_limiter.add_policy(high_priority_policy).await.unwrap();

    let context = RateLimitContext {
        client_ip: "***********".parse().unwrap(),
        user_id: None,
        method: "GET".to_string(),
        path: "/test".to_string(),
        headers: HashMap::new(),
        timestamp: SystemTime::now(),
    };

    // High priority policy should be applied first
    for _ in 0..5 {
        let result = rate_limiter.check_rate_limit(&context).await.unwrap();
        assert!(result.allowed);
        assert_eq!(result.policy_name, "high_priority");
    }

    // Sixth request should be blocked by high priority policy
    let result = rate_limiter.check_rate_limit(&context).await.unwrap();
    assert!(!result.allowed);
    assert_eq!(result.policy_name, "high_priority");
}

#[tokio::test]
async fn test_rate_limit_headers() {
    let config = RateLimitConfig {
        include_headers: true,
        ..Default::default()
    };
    let rate_limiter = RateLimiter::new(config).await.unwrap();

    let policy = RateLimitPolicy {
        name: "header_test".to_string(),
        scope: RateLimitScope::PerIP,
        limit: 10,
        window: Duration::from_secs(60),
        burst: Some(10),
        algorithm: RateLimitAlgorithm::TokenBucket,
        action: RateLimitAction::Block,
        priority: 1,
        conditions: vec![],
    };

    rate_limiter.add_policy(policy).await.unwrap();

    let context = RateLimitContext {
        client_ip: "***********".parse().unwrap(),
        user_id: None,
        method: "GET".to_string(),
        path: "/test".to_string(),
        headers: HashMap::new(),
        timestamp: SystemTime::now(),
    };

    let result = rate_limiter.check_rate_limit(&context).await.unwrap();

    assert!(result.allowed);
    assert!(result.headers.contains_key("X-RateLimit-Limit"));
    assert!(result.headers.contains_key("X-RateLimit-Remaining"));
    assert!(result.headers.contains_key("X-RateLimit-Reset"));

    assert_eq!(result.headers.get("X-RateLimit-Limit").unwrap(), "10");
}

#[tokio::test]
async fn test_policy_management() {
    let config = RateLimitConfig::default();
    let rate_limiter = RateLimiter::new(config).await.unwrap();

    let policy = RateLimitPolicy {
        name: "test_policy".to_string(),
        scope: RateLimitScope::Global,
        limit: 100,
        window: Duration::from_secs(60),
        burst: Some(100),
        algorithm: RateLimitAlgorithm::TokenBucket,
        action: RateLimitAction::Block,
        priority: 1,
        conditions: vec![],
    };

    // Add policy
    rate_limiter.add_policy(policy).await.unwrap();
    let stats = rate_limiter.get_stats().await;
    assert_eq!(stats.total_policies, 1);

    // Remove policy
    rate_limiter.remove_policy("test_policy").await.unwrap();
    let stats = rate_limiter.get_stats().await;
    assert_eq!(stats.total_policies, 0);

    // Try to remove non-existent policy
    let result = rate_limiter.remove_policy("non_existent").await;
    assert!(result.is_err());
}
```

## Key Design Decisions Explained

### 1. Multiple Rate Limiting Algorithms
**Decision**: Support token bucket, sliding window, fixed window, and sliding window log algorithms

**Rationale**:
- **Flexibility**: Different algorithms suit different use cases and traffic patterns
- **Accuracy**: Choose optimal algorithm based on precision requirements
- **Performance**: Balance between accuracy and computational overhead
- **Burst Handling**: Different approaches to handling traffic bursts

### 2. Hierarchical Policy System
**Decision**: Priority-based policy evaluation with multiple scopes

**Rationale**:
- **Granular Control**: Apply different limits to different types of requests
- **Flexibility**: Combine global, per-IP, per-user, and per-endpoint limits
- **Override Capability**: Higher priority policies can override lower priority ones
- **Conditional Application**: Apply policies based on request characteristics

### 3. Distributed Rate Limiting
**Decision**: Support both in-memory and Redis-based distributed storage

**Rationale**:
- **Scalability**: Handle high-volume traffic across multiple instances
- **Consistency**: Ensure limits are enforced accurately across the cluster
- **Performance**: Choose storage backend based on requirements
- **Reliability**: Prevent limit bypass through multiple entry points

## Performance Considerations

1. **Algorithm Efficiency**: O(1) operations for most rate limiting checks
2. **Memory Usage**: Efficient storage of rate limit state and history
3. **Network Overhead**: Minimal Redis operations for distributed limiting
4. **Cleanup Performance**: Background cleanup of expired entries

## Security Considerations

1. **DoS Protection**: Prevent abuse through comprehensive rate limiting
2. **Resource Exhaustion**: Protect against memory and storage exhaustion
3. **Bypass Prevention**: Secure key generation and validation
4. **Audit Trail**: Log rate limit violations for security analysis

## Comprehensive Testing

Create `tests/rate_limiting_tests.rs`:

```rust
use rusty_balancer::rate_limit::{
    RateLimitManager, RateLimitConfig, RateLimitAlgorithm, RateLimitScope,
    RateLimitPolicy, RateLimitRequest, RateLimitResult
};
use std::collections::HashMap;
use std::net::IpAddr;
use std::time::{Duration, Instant};

#[tokio::test]
async fn test_rate_limit_manager_creation() {
    let config = RateLimitConfig::default();
    let rate_limiter = RateLimitManager::new(config).await.unwrap();

    let stats = rate_limiter.get_rate_limit_stats().await;
    assert_eq!(stats.total_requests, 0);
    assert_eq!(stats.allowed_requests, 0);
    assert_eq!(stats.blocked_requests, 0);
}

#[tokio::test]
async fn test_token_bucket_algorithm() {
    let policy = RateLimitPolicy {
        name: "test-policy".to_string(),
        algorithm: RateLimitAlgorithm::TokenBucket {
            capacity: 10,
            refill_rate: 5, // 5 tokens per second
            refill_interval: Duration::from_secs(1),
        },
        scope: RateLimitScope::Global,
        window_size: Duration::from_secs(60),
        max_requests: 10,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        ..Default::default()
    };

    let rate_limiter = RateLimitManager::new(config).await.unwrap();
    rate_limiter.start().await.unwrap();

    let request = RateLimitRequest {
        client_ip: "***********00".parse().unwrap(),
        user_id: None,
        api_key: None,
        endpoint: "/api/test".to_string(),
        method: "GET".to_string(),
        headers: HashMap::new(),
        timestamp: Instant::now(),
        request_size: 1024,
        metadata: HashMap::new(),
    };

    // First 10 requests should be allowed (bucket capacity)
    for i in 0..10 {
        let result = rate_limiter.check_rate_limit(&request).await.unwrap();
        assert!(result.allowed, "Request {} should be allowed", i + 1);
        assert_eq!(result.policy_name, "test-policy");
    }

    // 11th request should be blocked (bucket empty)
    let result = rate_limiter.check_rate_limit(&request).await.unwrap();
    assert!(!result.allowed, "Request 11 should be blocked");

    // Wait for refill and try again
    tokio::time::sleep(Duration::from_secs(1)).await;

    // Should have 5 new tokens
    for i in 0..5 {
        let result = rate_limiter.check_rate_limit(&request).await.unwrap();
        assert!(result.allowed, "Refilled request {} should be allowed", i + 1);
    }

    // Next request should be blocked again
    let result = rate_limiter.check_rate_limit(&request).await.unwrap();
    assert!(!result.allowed, "Request after refill should be blocked");
}

#[tokio::test]
async fn test_sliding_window_algorithm() {
    let policy = RateLimitPolicy {
        name: "sliding-window-policy".to_string(),
        algorithm: RateLimitAlgorithm::SlidingWindow {
            window_size: Duration::from_secs(10),
            sub_windows: 10, // 1-second sub-windows
        },
        scope: RateLimitScope::Global,
        window_size: Duration::from_secs(10),
        max_requests: 5,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        ..Default::default()
    };

    let rate_limiter = RateLimitManager::new(config).await.unwrap();
    rate_limiter.start().await.unwrap();

    let request = RateLimitRequest {
        client_ip: "***********00".parse().unwrap(),
        user_id: None,
        api_key: None,
        endpoint: "/api/test".to_string(),
        method: "GET".to_string(),
        headers: HashMap::new(),
        timestamp: Instant::now(),
        request_size: 1024,
        metadata: HashMap::new(),
    };

    // First 5 requests should be allowed
    for i in 0..5 {
        let result = rate_limiter.check_rate_limit(&request).await.unwrap();
        assert!(result.allowed, "Request {} should be allowed", i + 1);
    }

    // 6th request should be blocked
    let result = rate_limiter.check_rate_limit(&request).await.unwrap();
    assert!(!result.allowed, "Request 6 should be blocked");

    // Wait for window to slide
    tokio::time::sleep(Duration::from_secs(2)).await;

    // Should still be blocked (window hasn't fully slid)
    let result = rate_limiter.check_rate_limit(&request).await.unwrap();
    assert!(!result.allowed, "Request should still be blocked");

    // Wait for full window to slide
    tokio::time::sleep(Duration::from_secs(9)).await;

    // Should be allowed again
    let result = rate_limiter.check_rate_limit(&request).await.unwrap();
    assert!(result.allowed, "Request should be allowed after window slide");
}

#[tokio::test]
async fn test_fixed_window_algorithm() {
    let policy = RateLimitPolicy {
        name: "fixed-window-policy".to_string(),
        algorithm: RateLimitAlgorithm::FixedWindow {
            window_size: Duration::from_secs(5),
        },
        scope: RateLimitScope::Global,
        window_size: Duration::from_secs(5),
        max_requests: 3,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        ..Default::default()
    };

    let rate_limiter = RateLimitManager::new(config).await.unwrap();
    rate_limiter.start().await.unwrap();

    let request = RateLimitRequest {
        client_ip: "***********00".parse().unwrap(),
        user_id: None,
        api_key: None,
        endpoint: "/api/test".to_string(),
        method: "GET".to_string(),
        headers: HashMap::new(),
        timestamp: Instant::now(),
        request_size: 1024,
        metadata: HashMap::new(),
    };

    // First 3 requests should be allowed
    for i in 0..3 {
        let result = rate_limiter.check_rate_limit(&request).await.unwrap();
        assert!(result.allowed, "Request {} should be allowed", i + 1);
    }

    // 4th request should be blocked
    let result = rate_limiter.check_rate_limit(&request).await.unwrap();
    assert!(!result.allowed, "Request 4 should be blocked");

    // Wait for window to reset
    tokio::time::sleep(Duration::from_secs(6)).await;

    // Should be allowed again
    let result = rate_limiter.check_rate_limit(&request).await.unwrap();
    assert!(result.allowed, "Request should be allowed after window reset");
}

#[tokio::test]
async fn test_per_ip_rate_limiting() {
    let policy = RateLimitPolicy {
        name: "per-ip-policy".to_string(),
        algorithm: RateLimitAlgorithm::TokenBucket {
            capacity: 5,
            refill_rate: 1,
            refill_interval: Duration::from_secs(1),
        },
        scope: RateLimitScope::PerIP,
        window_size: Duration::from_secs(60),
        max_requests: 5,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        ..Default::default()
    };

    let rate_limiter = RateLimitManager::new(config).await.unwrap();
    rate_limiter.start().await.unwrap();

    let request1 = RateLimitRequest {
        client_ip: "***********00".parse().unwrap(),
        user_id: None,
        api_key: None,
        endpoint: "/api/test".to_string(),
        method: "GET".to_string(),
        headers: HashMap::new(),
        timestamp: Instant::now(),
        request_size: 1024,
        metadata: HashMap::new(),
    };

    let request2 = RateLimitRequest {
        client_ip: "***********01".parse().unwrap(),
        user_id: None,
        api_key: None,
        endpoint: "/api/test".to_string(),
        method: "GET".to_string(),
        headers: HashMap::new(),
        timestamp: Instant::now(),
        request_size: 1024,
        metadata: HashMap::new(),
    };

    // Each IP should have its own bucket
    for i in 0..5 {
        let result1 = rate_limiter.check_rate_limit(&request1).await.unwrap();
        let result2 = rate_limiter.check_rate_limit(&request2).await.unwrap();

        assert!(result1.allowed, "IP1 request {} should be allowed", i + 1);
        assert!(result2.allowed, "IP2 request {} should be allowed", i + 1);
    }

    // Both IPs should now be rate limited
    let result1 = rate_limiter.check_rate_limit(&request1).await.unwrap();
    let result2 = rate_limiter.check_rate_limit(&request2).await.unwrap();

    assert!(!result1.allowed, "IP1 should be rate limited");
    assert!(!result2.allowed, "IP2 should be rate limited");
}

#[tokio::test]
async fn test_multiple_policies() {
    let global_policy = RateLimitPolicy {
        name: "global-policy".to_string(),
        algorithm: RateLimitAlgorithm::TokenBucket {
            capacity: 100,
            refill_rate: 10,
            refill_interval: Duration::from_secs(1),
        },
        scope: RateLimitScope::Global,
        window_size: Duration::from_secs(60),
        max_requests: 100,
        enabled: true,
        priority: 50, // Lower priority
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let per_ip_policy = RateLimitPolicy {
        name: "per-ip-policy".to_string(),
        algorithm: RateLimitAlgorithm::TokenBucket {
            capacity: 5,
            refill_rate: 1,
            refill_interval: Duration::from_secs(1),
        },
        scope: RateLimitScope::PerIP,
        window_size: Duration::from_secs(60),
        max_requests: 5,
        enabled: true,
        priority: 100, // Higher priority
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![global_policy, per_ip_policy],
        ..Default::default()
    };

    let rate_limiter = RateLimitManager::new(config).await.unwrap();
    rate_limiter.start().await.unwrap();

    let request = RateLimitRequest {
        client_ip: "***********00".parse().unwrap(),
        user_id: None,
        api_key: None,
        endpoint: "/api/test".to_string(),
        method: "GET".to_string(),
        headers: HashMap::new(),
        timestamp: Instant::now(),
        request_size: 1024,
        metadata: HashMap::new(),
    };

    // First 5 requests should be allowed (per-IP limit)
    for i in 0..5 {
        let result = rate_limiter.check_rate_limit(&request).await.unwrap();
        assert!(result.allowed, "Request {} should be allowed", i + 1);
        assert_eq!(result.policy_name, "per-ip-policy"); // Should hit per-IP policy first
    }

    // 6th request should be blocked by per-IP policy
    let result = rate_limiter.check_rate_limit(&request).await.unwrap();
    assert!(!result.allowed, "Request should be blocked by per-IP policy");
    assert_eq!(result.policy_name, "per-ip-policy");
}

#[tokio::test]
async fn test_adaptive_rate_limiting() {
    let policy = RateLimitPolicy {
        name: "adaptive-policy".to_string(),
        algorithm: RateLimitAlgorithm::Adaptive {
            base_limit: 10,
            min_limit: 5,
            max_limit: 20,
            adjustment_factor: 0.1,
            measurement_window: Duration::from_secs(10),
        },
        scope: RateLimitScope::Global,
        window_size: Duration::from_secs(60),
        max_requests: 10,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        enable_adaptive: true,
        ..Default::default()
    };

    let rate_limiter = RateLimitManager::new(config).await.unwrap();
    rate_limiter.start().await.unwrap();

    let request = RateLimitRequest {
        client_ip: "***********00".parse().unwrap(),
        user_id: None,
        api_key: None,
        endpoint: "/api/test".to_string(),
        method: "GET".to_string(),
        headers: HashMap::new(),
        timestamp: Instant::now(),
        request_size: 1024,
        metadata: HashMap::new(),
    };

    // Test that adaptive limiting adjusts based on load
    // This would require more complex setup to simulate varying load conditions
    let result = rate_limiter.check_rate_limit(&request).await.unwrap();
    assert!(result.allowed, "First request should be allowed");

    // In a real test, we would simulate high error rates or slow responses
    // to trigger adaptive adjustment
}

#[tokio::test]
async fn test_distributed_rate_limiting() {
    let policy = RateLimitPolicy {
        name: "distributed-policy".to_string(),
        algorithm: RateLimitAlgorithm::TokenBucket {
            capacity: 10,
            refill_rate: 5,
            refill_interval: Duration::from_secs(1),
        },
        scope: RateLimitScope::Global,
        window_size: Duration::from_secs(60),
        max_requests: 10,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        enable_distributed: true,
        redis_config: Some(rusty_balancer::rate_limit::RedisConfig {
            url: "redis://localhost:6379".to_string(),
            pool_size: 10,
            timeout: Duration::from_secs(1),
            key_prefix: "test_rate_limit".to_string(),
        }),
        ..Default::default()
    };

    // Note: This test would require a Redis instance to be running
    // For now, we'll test the configuration setup
    let rate_limiter = RateLimitManager::new(config).await;

    // In a real environment with Redis, this would succeed
    // For testing without Redis, we expect it to fall back to local limiting
    match rate_limiter {
        Ok(limiter) => {
            // Redis is available, test distributed limiting
            let request = RateLimitRequest {
                client_ip: "***********00".parse().unwrap(),
                user_id: None,
                api_key: None,
                endpoint: "/api/test".to_string(),
                method: "GET".to_string(),
                headers: HashMap::new(),
                timestamp: Instant::now(),
                request_size: 1024,
                metadata: HashMap::new(),
            };

            let result = limiter.check_rate_limit(&request).await.unwrap();
            assert!(result.allowed || !result.allowed); // Either outcome is valid
        }
        Err(_) => {
            // Redis not available, which is expected in test environment
            println!("Distributed rate limiting test skipped (Redis not available)");
        }
    }
}

#[tokio::test]
async fn test_rate_limit_headers() {
    let policy = RateLimitPolicy {
        name: "header-test-policy".to_string(),
        algorithm: RateLimitAlgorithm::TokenBucket {
            capacity: 10,
            refill_rate: 5,
            refill_interval: Duration::from_secs(1),
        },
        scope: RateLimitScope::Global,
        window_size: Duration::from_secs(60),
        max_requests: 10,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        ..Default::default()
    };

    let rate_limiter = RateLimitManager::new(config).await.unwrap();
    rate_limiter.start().await.unwrap();

    let request = RateLimitRequest {
        client_ip: "***********00".parse().unwrap(),
        user_id: None,
        api_key: None,
        endpoint: "/api/test".to_string(),
        method: "GET".to_string(),
        headers: HashMap::new(),
        timestamp: Instant::now(),
        request_size: 1024,
        metadata: HashMap::new(),
    };

    let result = rate_limiter.check_rate_limit(&request).await.unwrap();

    // Check that rate limit headers are provided
    assert!(result.headers.contains_key("X-RateLimit-Limit"));
    assert!(result.headers.contains_key("X-RateLimit-Remaining"));
    assert!(result.headers.contains_key("X-RateLimit-Reset"));

    if !result.allowed {
        assert!(result.headers.contains_key("Retry-After"));
    }

    // Verify header values are reasonable
    let limit: u32 = result.headers.get("X-RateLimit-Limit").unwrap().parse().unwrap();
    let remaining: u32 = result.headers.get("X-RateLimit-Remaining").unwrap().parse().unwrap();

    assert!(limit > 0);
    assert!(remaining <= limit);
}

#[tokio::test]
async fn test_concurrent_rate_limiting() {
    let policy = RateLimitPolicy {
        name: "concurrent-policy".to_string(),
        algorithm: RateLimitAlgorithm::TokenBucket {
            capacity: 100,
            refill_rate: 50,
            refill_interval: Duration::from_secs(1),
        },
        scope: RateLimitScope::Global,
        window_size: Duration::from_secs(60),
        max_requests: 100,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        ..Default::default()
    };

    let rate_limiter = RateLimitManager::new(config).await.unwrap();
    rate_limiter.start().await.unwrap();

    let num_requests = 200;
    let mut handles = Vec::new();

    for i in 0..num_requests {
        let rate_limiter_clone = rate_limiter.clone();
        let handle = tokio::spawn(async move {
            let request = RateLimitRequest {
                client_ip: format!("192.168.1.{}", 100 + (i % 155)).parse().unwrap(),
                user_id: None,
                api_key: None,
                endpoint: "/api/test".to_string(),
                method: "GET".to_string(),
                headers: HashMap::new(),
                timestamp: Instant::now(),
                request_size: 1024,
                metadata: HashMap::new(),
            };

            rate_limiter_clone.check_rate_limit(&request).await
        });

        handles.push(handle);
    }

    // Wait for all requests to complete
    let mut allowed_count = 0;
    let mut blocked_count = 0;

    for handle in handles {
        match handle.await.unwrap() {
            Ok(result) => {
                if result.allowed {
                    allowed_count += 1;
                } else {
                    blocked_count += 1;
                }
            }
            Err(_) => {
                // Count errors as blocked
                blocked_count += 1;
            }
        }
    }

    println!("Concurrent test results: {} allowed, {} blocked", allowed_count, blocked_count);

    // Should have some allowed and some blocked requests
    assert!(allowed_count > 0, "Some requests should be allowed");
    assert!(blocked_count > 0, "Some requests should be blocked");
    assert_eq!(allowed_count + blocked_count, num_requests);

    // Check final statistics
    let stats = rate_limiter.get_rate_limit_stats().await;
    assert_eq!(stats.total_requests, num_requests as u64);
    assert_eq!(stats.allowed_requests, allowed_count as u64);
    assert_eq!(stats.blocked_requests, blocked_count as u64);
}
```

## Performance Benchmarks

Create `benches/rate_limiting_bench.rs`:

```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use rusty_balancer::rate_limit::{
    RateLimitManager, RateLimitConfig, RateLimitAlgorithm, RateLimitScope,
    RateLimitPolicy, RateLimitRequest
};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::runtime::Runtime;

fn bench_token_bucket_algorithm(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let policy = RateLimitPolicy {
        name: "bench-policy".to_string(),
        algorithm: RateLimitAlgorithm::TokenBucket {
            capacity: 1000,
            refill_rate: 500,
            refill_interval: Duration::from_secs(1),
        },
        scope: RateLimitScope::Global,
        window_size: Duration::from_secs(60),
        max_requests: 1000,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        ..Default::default()
    };

    let rate_limiter = rt.block_on(async {
        let rl = RateLimitManager::new(config).await.unwrap();
        rl.start().await.unwrap();
        rl
    });

    c.bench_function("token_bucket_check", |b| {
        b.to_async(&rt).iter(|| async {
            let request = RateLimitRequest {
                client_ip: "***********00".parse().unwrap(),
                user_id: None,
                api_key: None,
                endpoint: "/api/test".to_string(),
                method: "GET".to_string(),
                headers: HashMap::new(),
                timestamp: Instant::now(),
                request_size: 1024,
                metadata: HashMap::new(),
            };

            black_box(rate_limiter.check_rate_limit(&request).await.unwrap())
        })
    });
}

fn bench_sliding_window_algorithm(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let policy = RateLimitPolicy {
        name: "sliding-bench-policy".to_string(),
        algorithm: RateLimitAlgorithm::SlidingWindow {
            window_size: Duration::from_secs(60),
            sub_windows: 60,
        },
        scope: RateLimitScope::Global,
        window_size: Duration::from_secs(60),
        max_requests: 1000,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        ..Default::default()
    };

    let rate_limiter = rt.block_on(async {
        let rl = RateLimitManager::new(config).await.unwrap();
        rl.start().await.unwrap();
        rl
    });

    c.bench_function("sliding_window_check", |b| {
        b.to_async(&rt).iter(|| async {
            let request = RateLimitRequest {
                client_ip: "***********00".parse().unwrap(),
                user_id: None,
                api_key: None,
                endpoint: "/api/test".to_string(),
                method: "GET".to_string(),
                headers: HashMap::new(),
                timestamp: Instant::now(),
                request_size: 1024,
                metadata: HashMap::new(),
            };

            black_box(rate_limiter.check_rate_limit(&request).await.unwrap())
        })
    });
}

fn bench_different_scopes(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let scopes = vec![
        ("global", RateLimitScope::Global),
        ("per_ip", RateLimitScope::PerIP),
        ("per_user", RateLimitScope::PerUser),
        ("per_endpoint", RateLimitScope::PerEndpoint),
    ];

    let mut group = c.benchmark_group("rate_limit_scopes");

    for (scope_name, scope) in scopes {
        let policy = RateLimitPolicy {
            name: format!("{}-policy", scope_name),
            algorithm: RateLimitAlgorithm::TokenBucket {
                capacity: 1000,
                refill_rate: 500,
                refill_interval: Duration::from_secs(1),
            },
            scope: scope.clone(),
            window_size: Duration::from_secs(60),
            max_requests: 1000,
            enabled: true,
            priority: 100,
            actions: Vec::new(),
            metadata: HashMap::new(),
        };

        let config = RateLimitConfig {
            enabled: true,
            policies: vec![policy],
            ..Default::default()
        };

        let rate_limiter = rt.block_on(async {
            let rl = RateLimitManager::new(config).await.unwrap();
            rl.start().await.unwrap();
            rl
        });

        group.bench_with_input(
            BenchmarkId::new("rate_limit_check", scope_name),
            &scope_name,
            |b, _| {
                b.to_async(&rt).iter(|| async {
                    let request = RateLimitRequest {
                        client_ip: "***********00".parse().unwrap(),
                        user_id: Some("user123".to_string()),
                        api_key: Some("key123".to_string()),
                        endpoint: "/api/test".to_string(),
                        method: "GET".to_string(),
                        headers: HashMap::new(),
                        timestamp: Instant::now(),
                        request_size: 1024,
                        metadata: HashMap::new(),
                    };

                    black_box(rate_limiter.check_rate_limit(&request).await.unwrap())
                })
            },
        );
    }

    group.finish();
}

fn bench_concurrent_rate_limiting(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let policy = RateLimitPolicy {
        name: "concurrent-bench-policy".to_string(),
        algorithm: RateLimitAlgorithm::TokenBucket {
            capacity: 10000,
            refill_rate: 5000,
            refill_interval: Duration::from_secs(1),
        },
        scope: RateLimitScope::PerIP,
        window_size: Duration::from_secs(60),
        max_requests: 10000,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        ..Default::default()
    };

    let rate_limiter = rt.block_on(async {
        let rl = RateLimitManager::new(config).await.unwrap();
        rl.start().await.unwrap();
        rl
    });

    let concurrency_levels = vec![1, 10, 50, 100];
    let mut group = c.benchmark_group("concurrent_rate_limiting");

    for &concurrency in &concurrency_levels {
        group.bench_with_input(
            BenchmarkId::new("concurrent_checks", concurrency),
            &concurrency,
            |b, &concurrency| {
                b.to_async(&rt).iter(|| async {
                    let mut handles = Vec::new();

                    for i in 0..concurrency {
                        let rate_limiter_clone = rate_limiter.clone();
                        let handle = tokio::spawn(async move {
                            let request = RateLimitRequest {
                                client_ip: format!("192.168.1.{}", 100 + (i % 155)).parse().unwrap(),
                                user_id: None,
                                api_key: None,
                                endpoint: "/api/test".to_string(),
                                method: "GET".to_string(),
                                headers: HashMap::new(),
                                timestamp: Instant::now(),
                                request_size: 1024,
                                metadata: HashMap::new(),
                            };

                            rate_limiter_clone.check_rate_limit(&request).await.unwrap()
                        });

                        handles.push(handle);
                    }

                    // Wait for all checks to complete
                    for handle in handles {
                        black_box(handle.await.unwrap());
                    }
                })
            },
        );
    }

    group.finish();
}

fn bench_multiple_policies(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let policy_counts = vec![1, 5, 10, 20];
    let mut group = c.benchmark_group("multiple_policies");

    for &policy_count in &policy_counts {
        let mut policies = Vec::new();

        for i in 0..policy_count {
            let policy = RateLimitPolicy {
                name: format!("policy-{}", i),
                algorithm: RateLimitAlgorithm::TokenBucket {
                    capacity: 1000,
                    refill_rate: 500,
                    refill_interval: Duration::from_secs(1),
                },
                scope: if i % 2 == 0 { RateLimitScope::Global } else { RateLimitScope::PerIP },
                window_size: Duration::from_secs(60),
                max_requests: 1000,
                enabled: true,
                priority: 100 - i as u32, // Different priorities
                actions: Vec::new(),
                metadata: HashMap::new(),
            };

            policies.push(policy);
        }

        let config = RateLimitConfig {
            enabled: true,
            policies,
            ..Default::default()
        };

        let rate_limiter = rt.block_on(async {
            let rl = RateLimitManager::new(config).await.unwrap();
            rl.start().await.unwrap();
            rl
        });

        group.bench_with_input(
            BenchmarkId::new("policy_evaluation", policy_count),
            &policy_count,
            |b, _| {
                b.to_async(&rt).iter(|| async {
                    let request = RateLimitRequest {
                        client_ip: "***********00".parse().unwrap(),
                        user_id: None,
                        api_key: None,
                        endpoint: "/api/test".to_string(),
                        method: "GET".to_string(),
                        headers: HashMap::new(),
                        timestamp: Instant::now(),
                        request_size: 1024,
                        metadata: HashMap::new(),
                    };

                    black_box(rate_limiter.check_rate_limit(&request).await.unwrap())
                })
            },
        );
    }

    group.finish();
}

fn bench_memory_usage(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let policy = RateLimitPolicy {
        name: "memory-bench-policy".to_string(),
        algorithm: RateLimitAlgorithm::SlidingWindow {
            window_size: Duration::from_secs(60),
            sub_windows: 60,
        },
        scope: RateLimitScope::PerIP,
        window_size: Duration::from_secs(60),
        max_requests: 1000,
        enabled: true,
        priority: 100,
        actions: Vec::new(),
        metadata: HashMap::new(),
    };

    let config = RateLimitConfig {
        enabled: true,
        policies: vec![policy],
        cleanup_interval: Duration::from_secs(1),
        ..Default::default()
    };

    c.bench_function("memory_usage_many_ips", |b| {
        b.to_async(&rt).iter(|| async {
            let rate_limiter = RateLimitManager::new(config.clone()).await.unwrap();
            rate_limiter.start().await.unwrap();

            // Create requests from many different IPs
            for i in 0..1000 {
                let request = RateLimitRequest {
                    client_ip: format!("10.{}.{}.{}",
                        (i / 65536) % 256,
                        (i / 256) % 256,
                        i % 256
                    ).parse().unwrap(),
                    user_id: None,
                    api_key: None,
                    endpoint: "/api/test".to_string(),
                    method: "GET".to_string(),
                    headers: HashMap::new(),
                    timestamp: Instant::now(),
                    request_size: 1024,
                    metadata: HashMap::new(),
                };

                let _ = rate_limiter.check_rate_limit(&request).await.unwrap();
            }

            black_box(rate_limiter.get_rate_limit_stats().await)
        })
    });
}

criterion_group!(
    benches,
    bench_token_bucket_algorithm,
    bench_sliding_window_algorithm,
    bench_different_scopes,
    bench_concurrent_rate_limiting,
    bench_multiple_policies,
    bench_memory_usage
);
criterion_main!(benches);
```

## Navigation
- [Previous: Sticky Sessions](14-sticky-sessions.md)
- [Next: Connection Pooling](16-connection-pooling.md)
