# Module 06: HTTP Protocol Support

## Learning Objectives
- Master HTTP protocol structure, parsing, and advanced features
- Implement comprehensive HTTP request and response handling using Hyper
- Learn HTTP connection management, keep-alive, and connection pooling
- Build an intelligent HTTP-aware proxy with advanced header manipulation
- Understand HTTP/1.1, HTTP/2, and HTTP/3 differences and optimizations
- Implement proper HTTP error handling, status codes, and edge cases
- Add support for WebSockets and Server-Sent Events
- Implement HTTP caching, compression, and content negotiation

## Prerequisites
- Completion of Module 05: Async and Concurrency
- Deep understanding of HTTP protocol specifications (RFC 7230-7235)
- Familiarity with <PERSON><PERSON>'s trait system and advanced generic programming
- Knowledge of async/await patterns, futures, and tokio runtime
- Understanding of network protocols and TCP/IP fundamentals

## Navigation
- [Previous: Async and Concurrency](05-async-and-concurrency.md)
- [Next: Health Checking System](07-health-checking.md)
- [Table of Contents](01-introduction.md#table-of-contents)

## HTTP Protocol Fundamentals

HTTP (HyperText Transfer Protocol) is the application layer protocol that powers the web. Unlike our TCP proxy that blindly forwards bytes, an HTTP-aware load balancer understands the structure and semantics of HTTP messages, enabling intelligent routing decisions and advanced features.

### HTTP Message Structure Deep Dive

```
HTTP Request Anatomy:
┌─────────────────────────────────────────────────────────────┐
│ GET /api/users?page=1&limit=10 HTTP/1.1                    │ ← Request Line
├─────────────────────────────────────────────────────────────┤
│ Host: api.example.com                                       │ ← Headers
│ User-Agent: Mozilla/5.0 (compatible; LoadBalancer/1.0)     │
│ Accept: application/json                                    │
│ Accept-Encoding: gzip, deflate, br                         │
│ Connection: keep-alive                                      │
│ Content-Length: 45                                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │ ← Empty line
├─────────────────────────────────────────────────────────────┤
│ {"filter": {"status": "active"}}                           │ ← Body (optional)
└─────────────────────────────────────────────────────────────┘

HTTP Response Anatomy:
┌─────────────────────────────────────────────────────────────┐
│ HTTP/1.1 200 OK                                             │ ← Status Line
├─────────────────────────────────────────────────────────────┤
│ Content-Type: application/json; charset=utf-8              │ ← Headers
│ Content-Length: 156                                         │
│ Content-Encoding: gzip                                      │
│ Cache-Control: public, max-age=300                         │
│ ETag: "abc123def456"                                        │
│ Server: nginx/1.20.1                                       │
│ X-Response-Time: 23ms                                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │ ← Empty line
├─────────────────────────────────────────────────────────────┤
│ {"users":[{"id":1,"name":"Alice"},{"id":2,"name":"Bob"}]}  │ ← Body
└─────────────────────────────────────────────────────────────┘
```

### Why HTTP-Aware Load Balancing? (Layer 7 vs Layer 4)

**Layer 4 (TCP) Load Balancing**:
- Operates at transport layer
- Routes based on IP addresses and ports only
- Fast but limited intelligence
- Cannot inspect application data

**Layer 7 (HTTP) Load Balancing**:
- Operates at application layer
- Full HTTP message inspection and manipulation
- Intelligent routing based on content
- Advanced features like caching and compression

```mermaid
graph TD
    Client[Client Request] --> LB[HTTP Load Balancer]

    subgraph "Layer 7 Intelligence"
        LB --> Parse[Parse HTTP Message]
        Parse --> Route{Routing Decision}
        Route -->|Path: /api/*| API[API Servers]
        Route -->|Path: /static/*| Static[Static Servers]
        Route -->|Header: mobile| Mobile[Mobile Servers]
        Route -->|Cookie: session| Sticky[Sticky Session]
    end

    API --> Response[HTTP Response]
    Static --> Response
    Mobile --> Response
    Sticky --> Response
    Response --> Client
```

**Advanced Layer 7 Capabilities**:

1. **Content-Based Routing**:
   - Route `/api/*` to API servers
   - Route `/static/*` to CDN servers
   - Route based on User-Agent headers

2. **Request/Response Transformation**:
   - Add security headers
   - Rewrite URLs and paths
   - Modify request/response bodies

3. **Session Management**:
   - Sticky sessions based on cookies
   - Session affinity for stateful applications
   - Load balancing with session awareness

4. **Performance Optimization**:
   - HTTP response caching
   - Content compression (gzip, brotli)
   - Connection keep-alive management

5. **Security Features**:
   - SSL/TLS termination
   - Request validation and sanitization
   - Rate limiting per user/IP
   - DDoS protection

### HTTP Version Comparison and Evolution

| Feature | HTTP/1.0 | HTTP/1.1 | HTTP/2 | HTTP/3 |
|---------|----------|----------|---------|---------|
| **Connection** | New per request | Keep-alive | Multiplexing | QUIC (UDP) |
| **Header Compression** | None | None | HPACK | QPACK |
| **Prioritization** | None | None | Stream priority | Stream priority |
| **Server Push** | No | No | Yes | Yes |
| **Head-of-line Blocking** | Yes | Yes | Partial | No |
| **Binary Protocol** | No | No | Yes | Yes |
| **Performance** | Poor | Good | Better | Best |

**HTTP/2 Key Improvements**:
- **Multiplexing**: Multiple requests over single connection
- **Header Compression**: HPACK reduces overhead
- **Server Push**: Proactively send resources
- **Stream Prioritization**: Important requests first

**HTTP/3 Revolutionary Changes**:
- **QUIC Protocol**: Built on UDP instead of TCP
- **No Head-of-line Blocking**: True multiplexing
- **Built-in Encryption**: Always encrypted
- **Connection Migration**: Survives network changes

### Performance Impact Analysis

```rust
// Performance comparison example
use std::time::Instant;

// HTTP/1.1: Sequential requests (head-of-line blocking)
async fn http1_requests() -> Duration {
    let start = Instant::now();

    // Each request waits for previous to complete
    let _response1 = make_http1_request("/api/users").await;
    let _response2 = make_http1_request("/api/posts").await;
    let _response3 = make_http1_request("/api/comments").await;

    start.elapsed() // ~300ms (100ms each)
}

// HTTP/2: Concurrent requests (multiplexing)
async fn http2_requests() -> Duration {
    let start = Instant::now();

    // All requests sent simultaneously over single connection
    let (response1, response2, response3) = tokio::join!(
        make_http2_request("/api/users"),
        make_http2_request("/api/posts"),
        make_http2_request("/api/comments")
    );

    start.elapsed() // ~100ms (parallel execution)
}
```

**Real-World Performance Metrics**:
- HTTP/1.1: ~50 requests/second per connection
- HTTP/2: ~500+ requests/second per connection
- HTTP/3: ~1000+ requests/second per connection (with QUIC)

## Rust HTTP Ecosystem and Architecture

### Hyper: The High-Performance Foundation

Hyper is Rust's premier HTTP library, designed for maximum performance and safety:

**Core Features**:
- **Zero-copy parsing**: Minimal memory allocations during parsing
- **HTTP/1.1 and HTTP/2**: Full protocol support with automatic negotiation
- **Async-first design**: Built for tokio runtime from the ground up
- **Type safety**: Compile-time guarantees for HTTP correctness
- **Streaming support**: Handle large requests/responses efficiently

**Architecture Overview**:
```mermaid
graph TD
    subgraph "Hyper Architecture"
        Client[hyper::Client] --> Connector[HttpConnector]
        Server[hyper::Server] --> Service[Service Trait]

        subgraph "Core Types"
            Request[Request<Body>]
            Response[Response<Body>]
            Body[Body - Streaming]
            Headers[HeaderMap]
        end

        subgraph "Protocol Support"
            HTTP1[HTTP/1.1 Codec]
            HTTP2[HTTP/2 Codec]
            Auto[Auto-negotiation]
        end
    end

    Connector --> HTTP1
    Connector --> HTTP2
    Service --> Request
    Service --> Response
```

### Essential HTTP Types and Patterns

```rust
use hyper::{Request, Response, Body, Method, Uri, StatusCode, Version};
use hyper::header::{HeaderMap, HeaderName, HeaderValue, HOST, USER_AGENT, CONTENT_TYPE};
use hyper::service::{make_service_fn, service_fn};
use std::convert::Infallible;

/// Core HTTP types used throughout our load balancer
pub type HttpRequest = Request<Body>;
pub type HttpResponse = Response<Body>;
pub type HttpResult<T> = Result<T, Box<dyn std::error::Error + Send + Sync>>;

/// HTTP method analysis for routing decisions
#[derive(Debug, Clone, PartialEq)]
pub enum HttpMethodClass {
    Safe,      // GET, HEAD, OPTIONS - safe to retry
    Idempotent, // PUT, DELETE - idempotent operations
    Unsafe,    // POST, PATCH - potentially unsafe to retry
}

impl HttpMethodClass {
    pub fn from_method(method: &Method) -> Self {
        match *method {
            Method::GET | Method::HEAD | Method::OPTIONS => HttpMethodClass::Safe,
            Method::PUT | Method::DELETE => HttpMethodClass::Idempotent,
            Method::POST | Method::PATCH => HttpMethodClass::Unsafe,
            _ => HttpMethodClass::Unsafe, // Conservative default
        }
    }

    /// Determine if method is safe to retry on failure
    pub fn is_retryable(&self) -> bool {
        matches!(self, HttpMethodClass::Safe | HttpMethodClass::Idempotent)
    }
}

/// HTTP version capabilities for optimization
#[derive(Debug, Clone)]
pub struct HttpCapabilities {
    pub version: Version,
    pub supports_multiplexing: bool,
    pub supports_server_push: bool,
    pub supports_header_compression: bool,
}

impl HttpCapabilities {
    pub fn from_version(version: Version) -> Self {
        match version {
            Version::HTTP_09 | Version::HTTP_10 => Self {
                version,
                supports_multiplexing: false,
                supports_server_push: false,
                supports_header_compression: false,
            },
            Version::HTTP_11 => Self {
                version,
                supports_multiplexing: false,
                supports_server_push: false,
                supports_header_compression: false,
            },
            Version::HTTP_2 => Self {
                version,
                supports_multiplexing: true,
                supports_server_push: true,
                supports_header_compression: true,
            },
            Version::HTTP_3 => Self {
                version,
                supports_multiplexing: true,
                supports_server_push: true,
                supports_header_compression: true,
            },
            _ => Self::from_version(Version::HTTP_11), // Default fallback
        }
    }
}

/// Request analysis for intelligent routing
#[derive(Debug, Clone)]
pub struct RequestAnalysis {
    pub method_class: HttpMethodClass,
    pub capabilities: HttpCapabilities,
    pub content_length: Option<u64>,
    pub is_websocket_upgrade: bool,
    pub has_expect_continue: bool,
    pub cache_control: Option<String>,
    pub routing_hints: RoutingHints,
}

#[derive(Debug, Clone)]
pub struct RoutingHints {
    pub path_prefix: String,
    pub host: Option<String>,
    pub user_agent_class: UserAgentClass,
    pub accepts_compression: Vec<String>,
    pub preferred_language: Option<String>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum UserAgentClass {
    Browser,
    MobileApp,
    ApiClient,
    Bot,
    Unknown,
}

impl RequestAnalysis {
    /// Analyze HTTP request for routing and optimization decisions
    pub fn analyze(request: &HttpRequest) -> Self {
        let method_class = HttpMethodClass::from_method(request.method());
        let capabilities = HttpCapabilities::from_version(request.version());

        // Extract content length
        let content_length = request.headers()
            .get("content-length")
            .and_then(|v| v.to_str().ok())
            .and_then(|s| s.parse().ok());

        // Check for WebSocket upgrade
        let is_websocket_upgrade = request.headers()
            .get("upgrade")
            .map(|v| v.to_str().unwrap_or("").to_lowercase() == "websocket")
            .unwrap_or(false);

        // Check for Expect: 100-continue
        let has_expect_continue = request.headers()
            .get("expect")
            .map(|v| v.to_str().unwrap_or("").to_lowercase() == "100-continue")
            .unwrap_or(false);

        // Extract cache control
        let cache_control = request.headers()
            .get("cache-control")
            .and_then(|v| v.to_str().ok())
            .map(|s| s.to_string());

        // Analyze routing hints
        let routing_hints = Self::extract_routing_hints(request);

        Self {
            method_class,
            capabilities,
            content_length,
            is_websocket_upgrade,
            has_expect_continue,
            cache_control,
            routing_hints,
        }
    }

    fn extract_routing_hints(request: &HttpRequest) -> RoutingHints {
        let path_prefix = request.uri().path()
            .split('/')
            .nth(1)
            .unwrap_or("")
            .to_string();

        let host = request.headers()
            .get(HOST)
            .and_then(|v| v.to_str().ok())
            .map(|s| s.to_string());

        let user_agent_class = request.headers()
            .get(USER_AGENT)
            .and_then(|v| v.to_str().ok())
            .map(Self::classify_user_agent)
            .unwrap_or(UserAgentClass::Unknown);

        let accepts_compression = request.headers()
            .get("accept-encoding")
            .and_then(|v| v.to_str().ok())
            .map(|s| s.split(',').map(|enc| enc.trim().to_string()).collect())
            .unwrap_or_default();

        let preferred_language = request.headers()
            .get("accept-language")
            .and_then(|v| v.to_str().ok())
            .and_then(|s| s.split(',').next())
            .map(|lang| lang.trim().to_string());

        RoutingHints {
            path_prefix,
            host,
            user_agent_class,
            accepts_compression,
            preferred_language,
        }
    }

    fn classify_user_agent(user_agent: &str) -> UserAgentClass {
        let ua_lower = user_agent.to_lowercase();

        if ua_lower.contains("mozilla") && (ua_lower.contains("chrome") || ua_lower.contains("firefox") || ua_lower.contains("safari")) {
            UserAgentClass::Browser
        } else if ua_lower.contains("mobile") || ua_lower.contains("android") || ua_lower.contains("iphone") {
            UserAgentClass::MobileApp
        } else if ua_lower.contains("bot") || ua_lower.contains("crawler") || ua_lower.contains("spider") {
            UserAgentClass::Bot
        } else if ua_lower.contains("curl") || ua_lower.contains("wget") || ua_lower.contains("postman") {
            UserAgentClass::ApiClient
        } else {
            UserAgentClass::Unknown
        }
    }
}

## Implementation: HTTP Proxy

Let's implement an HTTP-aware proxy that can parse requests and make routing decisions.

### HTTP Proxy Structure

Create `src/proxy/http_proxy.rs`:

```rust
use hyper::{Body, Request, Response, StatusCode, Uri};
use hyper::client::HttpConnector;
use hyper::service::{make_service_fn, service_fn};
use hyper::{Client, Server};
use std::convert::Infallible;
use std::net::SocketAddr;
use tracing::{info, warn, error, debug};
use crate::Result;

/// HTTP-aware proxy that can parse and modify requests
pub struct HttpProxy {
    backend_addr: SocketAddr,
    client: Client<HttpConnector>,
}

impl HttpProxy {
    /// Create a new HTTP proxy
    pub fn new(backend_addr: SocketAddr) -> Self {
        let client = Client::new();
        Self {
            backend_addr,
            client,
        }
    }
    
    /// Start the HTTP proxy server
    pub async fn start(&self, bind_addr: SocketAddr) -> Result<()> {
        // Clone for move into service
        let backend_addr = self.backend_addr;
        let client = self.client.clone();
        
        // Create service factory
        let make_svc = make_service_fn(move |_conn| {
            let client = client.clone();
            let backend_addr = backend_addr;
            
            async move {
                Ok::<_, Infallible>(service_fn(move |req| {
                    Self::handle_request(req, client.clone(), backend_addr)
                }))
            }
        });
        
        // Start server
        let server = Server::bind(&bind_addr).serve(make_svc);
        info!("HTTP Proxy listening on {}, forwarding to {}", bind_addr, backend_addr);
        
        if let Err(e) = server.await {
            error!("HTTP proxy server error: {}", e);
        }
        
        Ok(())
    }
    
    /// Handle a single HTTP request
    async fn handle_request(
        mut req: Request<Body>,
        client: Client<HttpConnector>,
        backend_addr: SocketAddr,
    ) -> Result<Response<Body>, Infallible> {
        debug!("Handling request: {} {}", req.method(), req.uri());
        
        // Modify request for backend
        match Self::prepare_backend_request(&mut req, backend_addr) {
            Ok(_) => {}
            Err(e) => {
                error!("Failed to prepare backend request: {}", e);
                return Ok(Self::error_response(StatusCode::BAD_GATEWAY));
            }
        }
        
        // Forward request to backend
        match client.request(req).await {
            Ok(mut response) => {
                debug!("Backend response: {}", response.status());
                
                // Modify response headers if needed
                Self::prepare_client_response(&mut response);
                
                Ok(response)
            }
            Err(e) => {
                error!("Backend request failed: {}", e);
                Ok(Self::error_response(StatusCode::BAD_GATEWAY))
            }
        }
    }
    
    /// Prepare request for forwarding to backend
    fn prepare_backend_request(req: &mut Request<Body>, backend_addr: SocketAddr) -> Result<()> {
        // Build new URI with backend address
        let original_uri = req.uri();
        let new_uri = format!(
            "http://{}{}{}",
            backend_addr,
            original_uri.path(),
            original_uri.query().map(|q| format!("?{}", q)).unwrap_or_default()
        );
        
        *req.uri_mut() = new_uri.parse().map_err(|e| {
            error!("Invalid URI: {}", e);
            e
        })?;
        
        // Add/modify headers
        let headers = req.headers_mut();
        
        // Update Host header
        headers.insert("host", format!("{}", backend_addr).parse().unwrap());
        
        // Add proxy headers
        headers.insert("x-forwarded-for", "127.0.0.1".parse().unwrap());
        headers.insert("x-forwarded-proto", "http".parse().unwrap());
        
        debug!("Modified request URI: {}", req.uri());
        Ok(())
    }
    
    /// Prepare response for client
    fn prepare_client_response(response: &mut Response<Body>) {
        let headers = response.headers_mut();
        
        // Add proxy identification
        headers.insert("x-proxy", "rusty-balancer".parse().unwrap());
        
        // Remove hop-by-hop headers
        headers.remove("connection");
        headers.remove("upgrade");
        headers.remove("proxy-authenticate");
        headers.remove("proxy-authorization");
        headers.remove("te");
        headers.remove("trailers");
        headers.remove("transfer-encoding");
    }
    
    /// Create error response
    fn error_response(status: StatusCode) -> Response<Body> {
        Response::builder()
            .status(status)
            .header("content-type", "text/plain")
            .body(Body::from(format!("Proxy Error: {}", status)))
            .unwrap()
    }
}
```

### Understanding HTTP Proxy Components

#### 1. Service Architecture

The proxy uses Hyper's `make_service_fn` and `service_fn` to create a service for each incoming connection. This allows us to handle requests asynchronously and efficiently.

#### 2. Request Preparation

The `prepare_backend_request` function modifies the incoming request to route it to the backend server. This includes updating the URI and adding proxy headers like `x-forwarded-for`.

#### 3. Response Preparation

The `prepare_client_response` function modifies the backend's response before sending it to the client. This includes adding proxy identification headers and removing hop-by-hop headers.

#### 4. Error Handling

The `error_response` function generates appropriate HTTP error responses for the client, ensuring a consistent user experience.

### Performance Considerations

- **Connection Pooling**: Use connection pooling to reduce the overhead of creating new connections.
- **Header Compression**: Leverage HTTP/2's header compression to reduce bandwidth usage.
- **Load Testing**: Perform load testing to identify bottlenecks and optimize performance.
- **Tracing**: Use tracing tools to monitor and debug the proxy's performance.

By understanding and implementing these components, you can build a robust and efficient HTTP-aware proxy.

## Advanced HTTP Features

### Connection Pooling

HTTP/1.1 supports connection reuse through keep-alive:

```rust
use hyper::client::HttpConnector;
use hyper::Client;

// Client automatically pools connections
let client = Client::builder()
    .pool_idle_timeout(Duration::from_secs(30))
    .pool_max_idle_per_host(10)
    .build::<_, hyper::Body>(HttpConnector::new());
```

### Request/Response Body Handling

```rust
use hyper::Body;
use bytes::Bytes;

async fn read_body(body: Body) -> Result<Bytes> {
    hyper::body::to_bytes(body).await.map_err(Into::into)
}

async fn modify_request_body(req: &mut Request<Body>) -> Result<()> {
    let body_bytes = hyper::body::to_bytes(req.body_mut()).await?;
    
    // Modify body content
    let modified = modify_content(body_bytes);
    
    // Replace body
    *req.body_mut() = Body::from(modified);
    Ok(())
}
```

### Content-Based Routing

```rust
impl HttpProxy {
    async fn route_request(&self, req: &Request<Body>) -> SocketAddr {
        match req.uri().path() {
            path if path.starts_with("/api/") => self.api_backend,
            path if path.starts_with("/static/") => self.static_backend,
            _ => self.default_backend,
        }
    }
}
```

## Testing the HTTP Proxy

Update `src/main.rs` to test our HTTP proxy:

```rust
use rusty_balancer::proxy::HttpProxy;
use std::net::SocketAddr;
use tracing::info;

#[tokio::main]
async fn main() -> rusty_balancer::Result<()> {
    tracing_subscriber::fmt::init();
    
    info!("Starting Rusty Balancer HTTP Proxy");
    
    let bind_addr: SocketAddr = "127.0.0.1:8080".parse()?;
    let backend_addr: SocketAddr = "127.0.0.1:8081".parse()?;
    
    let proxy = HttpProxy::new(backend_addr);
    proxy.start(bind_addr).await?;
    
    Ok(())
}
```

### Testing Setup

1. **Start a backend HTTP server**:
```bash
# Simple Python HTTP server
python3 -m http.server 8081
```

2. **Start the proxy**:
```bash
RUST_LOG=debug cargo run
```

3. **Test with curl**:
```bash
curl -v http://127.0.0.1:8080/
curl -H "Custom-Header: test" http://127.0.0.1:8080/
```

## HTTP/2 Considerations

HTTP/2 introduces additional complexity:
- **Binary Protocol**: Not human-readable like HTTP/1.1
- **Multiplexing**: Multiple requests per connection
- **Server Push**: Server can send resources proactively
- **Header Compression**: HPACK compression algorithm

Hyper supports HTTP/2, but it requires additional configuration:

```rust
use hyper::server::conn::Http;

let http = Http::new().http2_only(true);
```

## Error Handling Patterns

HTTP proxies must handle various error scenarios:

```rust
impl HttpProxy {
    fn handle_backend_error(error: hyper::Error) -> Response<Body> {
        match error {
            e if e.is_connect() => {
                Self::error_response(StatusCode::BAD_GATEWAY)
            }
            e if e.is_timeout() => {
                Self::error_response(StatusCode::GATEWAY_TIMEOUT)
            }
            _ => {
                Self::error_response(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }
}
```

## Performance Optimization

### Zero-Copy Operations
```rust
// Avoid copying body data when possible
let body = req.into_body();
let response = Response::new(body);
```

### Connection Reuse
```rust
// Configure client for optimal connection pooling
let client = Client::builder()
    .pool_idle_timeout(Duration::from_secs(30))
    .pool_max_idle_per_host(20)
    .build::<_, Body>(HttpConnector::new());
```

## Next Steps Preview

In Module 04, we'll add load balancing logic:
- Multiple backend servers
- Round-robin algorithm implementation
- Backend server health tracking
- Request distribution strategies

## Key Takeaways

- **HTTP Parsing**: Hyper provides efficient HTTP parsing and generation
- **Service Architecture**: Hyper's service model enables clean request handling
- **Header Management**: Proper header handling is crucial for HTTP proxies
- **Error Handling**: HTTP proxies must handle various network and protocol errors
- **Performance**: Connection pooling and zero-copy operations improve efficiency

Our HTTP proxy now understands the application layer, enabling intelligent routing decisions based on request content.

## Navigation
- [Previous: Basic TCP Proxy](02-basic-tcp-proxy.md)
- [Next: Round-Robin Load Balancing](04-round-robin-balancing.md)
