# Module 18: Graceful Shutdown

## Learning Objectives
- Implement graceful shutdown patterns for zero-downtime deployments
- Design connection draining and request completion strategies
- Build shutdown coordination across multiple system components
- Create health check integration during shutdown process
- Understand graceful shutdown patterns and operational best practices

## Why Graceful Shutdown is Critical

Graceful shutdown ensures service reliability during maintenance and deployments:

1. **Zero Downtime**: Complete in-flight requests before shutting down
2. **Data Integrity**: Ensure all transactions are properly completed or rolled back
3. **User Experience**: Prevent connection drops and request failures
4. **Resource Cleanup**: Properly release resources and close connections
5. **Operational Safety**: Enable safe deployments and maintenance windows

### Graceful Shutdown Architecture

```mermaid
graph TD
    Signal[Shutdown Signal<br/>SIGTERM/SIGINT] --> SM[Shutdown Manager]
    
    SM --> Phase1[Phase 1: Stop Accepting New Requests]
    Phase1 --> HC[Update Health Checks]
    HC --> LB[Load Balancer Removal]
    
    Phase1 --> Phase2[Phase 2: Drain Existing Connections]
    Phase2 --> Monitor[Monitor Active Requests]
    Monitor --> Wait[Wait for Completion]
    
    Phase2 --> Phase3[Phase 3: Graceful Resource Cleanup]
    Phase3 --> CloseConn[Close Connections]
    Phase3 --> StopServices[Stop Background Services]
    Phase3 --> FlushLogs[Flush Logs & Metrics]
    
    Phase3 --> Phase4[Phase 4: Final Cleanup]
    Phase4 --> ReleaseRes[Release Resources]
    Phase4 --> Exit[Exit Process]
    
    subgraph "Shutdown Components"
        Server[HTTP Server]
        ConnPool[Connection Pools]
        Metrics[Metrics System]
        Logging[Logging System]
        Background[Background Tasks]
    end
    
    SM --> Server
    SM --> ConnPool
    SM --> Metrics
    SM --> Logging
    SM --> Background
    
    subgraph "Monitoring & Coordination"
        Timeout[Shutdown Timeout]
        Progress[Progress Tracking]
        Alerts[Shutdown Alerts]
        Status[Status Reporting]
    end
    
    SM --> Timeout
    SM --> Progress
    SM --> Alerts
    SM --> Status
    
    subgraph "External Integration"
        HealthEndpoint[Health Endpoint]
        ServiceMesh[Service Mesh]
        LoadBalancer[Load Balancer]
        Monitoring[Monitoring Systems]
    end
    
    HC --> HealthEndpoint
    HC --> ServiceMesh
    HC --> LoadBalancer
    Progress --> Monitoring
```

## Graceful Shutdown Implementation

### Design Decisions

**Why phased shutdown approach?**
- **Coordination**: Ensure proper order of shutdown operations
- **Safety**: Prevent data loss and connection drops
- **Observability**: Clear visibility into shutdown progress
- **Timeout Management**: Handle stuck operations gracefully

**Why connection draining?**
- **User Experience**: Complete active requests before shutdown
- **Data Integrity**: Ensure transactions are properly finished
- **Resource Management**: Clean up connections in controlled manner
- **Load Balancer Integration**: Coordinate with upstream load balancers

Create `src/shutdown/mod.rs`:

```rust
//! Graceful shutdown management for zero-downtime deployments
//!
//! This module provides comprehensive graceful shutdown capabilities including:
//! - Phased shutdown with configurable timeouts
//! - Connection draining and request completion
//! - Health check integration during shutdown
//! - Resource cleanup and background task termination
//! - Shutdown progress monitoring and alerting

pub mod manager;
pub mod phases;
pub mod draining;
pub mod coordination;

use crate::error::{LoadBalancerError, Result};
use crate::metrics::LoadBalancerMetrics;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, Mutex, broadcast, watch};
use tokio::signal;
use tracing::{info, warn, error, debug};

/// Graceful shutdown manager
pub struct ShutdownManager {
    /// Shutdown configuration
    config: ShutdownConfig,
    
    /// Shutdown state
    state: Arc<RwLock<ShutdownState>>,
    
    /// Shutdown progress tracker
    progress: Arc<RwLock<ShutdownProgress>>,
    
    /// Shutdown signal broadcaster
    shutdown_tx: broadcast::Sender<ShutdownPhase>,
    
    /// Health status during shutdown
    health_status: Arc<RwLock<HealthStatus>>,
    
    /// Active connections tracker
    active_connections: Arc<RwLock<HashMap<String, ConnectionInfo>>>,
    
    /// Background tasks tracker
    background_tasks: Arc<RwLock<Vec<TaskHandle>>>,
    
    /// Metrics collector
    metrics: Option<Arc<LoadBalancerMetrics>>,
}

/// Shutdown configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShutdownConfig {
    /// Enable graceful shutdown
    pub enabled: bool,
    
    /// Total shutdown timeout
    pub total_timeout: Duration,
    
    /// Phase 1 timeout (stop accepting new requests)
    pub stop_accepting_timeout: Duration,
    
    /// Phase 2 timeout (drain existing connections)
    pub connection_drain_timeout: Duration,
    
    /// Phase 3 timeout (cleanup resources)
    pub cleanup_timeout: Duration,
    
    /// Force shutdown after total timeout
    pub force_shutdown_after_timeout: bool,
    
    /// Health check grace period
    pub health_check_grace_period: Duration,
    
    /// Connection drain check interval
    pub drain_check_interval: Duration,
    
    /// Enable shutdown progress reporting
    pub enable_progress_reporting: bool,
    
    /// Progress reporting interval
    pub progress_reporting_interval: Duration,
    
    /// Enable shutdown alerts
    pub enable_alerts: bool,
    
    /// Alert on long-running connections
    pub alert_on_long_connections: bool,
    
    /// Long connection threshold
    pub long_connection_threshold: Duration,
}

/// Shutdown phases
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum ShutdownPhase {
    /// Normal operation
    Running,
    
    /// Phase 1: Stop accepting new requests
    StopAccepting,
    
    /// Phase 2: Drain existing connections
    Draining,
    
    /// Phase 3: Cleanup resources
    Cleanup,
    
    /// Phase 4: Final shutdown
    Shutdown,
    
    /// Emergency shutdown (timeout exceeded)
    Emergency,
}

/// Shutdown state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShutdownState {
    /// Current shutdown phase
    pub current_phase: ShutdownPhase,
    
    /// Shutdown start time
    pub shutdown_started_at: Option<Instant>,
    
    /// Phase start times
    pub phase_start_times: HashMap<ShutdownPhase, Instant>,
    
    /// Shutdown reason
    pub shutdown_reason: Option<String>,
    
    /// Whether shutdown was initiated
    pub shutdown_initiated: bool,
    
    /// Whether shutdown completed successfully
    pub shutdown_completed: bool,
    
    /// Shutdown errors
    pub shutdown_errors: Vec<String>,
}

/// Shutdown progress tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShutdownProgress {
    /// Active connections count
    pub active_connections: usize,
    
    /// Background tasks count
    pub background_tasks: usize,
    
    /// Completed phases
    pub completed_phases: Vec<ShutdownPhase>,
    
    /// Current phase progress (0.0 - 1.0)
    pub current_phase_progress: f64,
    
    /// Overall progress (0.0 - 1.0)
    pub overall_progress: f64,
    
    /// Estimated time remaining
    pub estimated_time_remaining: Option<Duration>,
    
    /// Last updated
    pub last_updated: Instant,
}

/// Health status during shutdown
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum HealthStatus {
    /// Service is healthy and accepting requests
    Healthy,
    
    /// Service is shutting down but still processing requests
    Draining,
    
    /// Service is unhealthy and not accepting requests
    Unhealthy,
}

/// Connection information for tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionInfo {
    /// Connection ID
    pub id: String,
    
    /// Connection start time
    pub started_at: Instant,
    
    /// Connection type
    pub connection_type: ConnectionType,
    
    /// Remote address
    pub remote_addr: String,
    
    /// Current request count
    pub active_requests: usize,
    
    /// Last activity time
    pub last_activity: Instant,
}

/// Types of connections being tracked
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum ConnectionType {
    /// HTTP client connection
    Http,
    
    /// WebSocket connection
    WebSocket,
    
    /// Backend connection
    Backend,
    
    /// Management connection
    Management,
}

/// Background task handle
#[derive(Debug)]
pub struct TaskHandle {
    /// Task name
    pub name: String,
    
    /// Task handle
    pub handle: tokio::task::JoinHandle<()>,
    
    /// Task shutdown signal
    pub shutdown_tx: Option<watch::Sender<bool>>,
    
    /// Task start time
    pub started_at: Instant,
}

impl ShutdownManager {
    /// Create a new shutdown manager
    pub fn new(config: ShutdownConfig) -> Self {
        let (shutdown_tx, _) = broadcast::channel(16);
        
        info!("🛑 Shutdown manager initialized (timeout: {:?})", config.total_timeout);
        
        Self {
            config,
            state: Arc::new(RwLock::new(ShutdownState::new())),
            progress: Arc::new(RwLock::new(ShutdownProgress::new())),
            shutdown_tx,
            health_status: Arc::new(RwLock::new(HealthStatus::Healthy)),
            active_connections: Arc::new(RwLock::new(HashMap::new())),
            background_tasks: Arc::new(RwLock::new(Vec::new())),
            metrics: None,
        }
    }
    
    /// Set metrics collector
    pub fn with_metrics(mut self, metrics: Arc<LoadBalancerMetrics>) -> Self {
        self.metrics = Some(metrics);
        self
    }
    
    /// Start shutdown manager and signal handlers
    pub async fn start(&self) -> Result<()> {
        if !self.config.enabled {
            info!("Graceful shutdown is disabled");
            return Ok(());
        }
        
        // Start signal handlers
        self.start_signal_handlers().await;
        
        // Start progress reporting if enabled
        if self.config.enable_progress_reporting {
            self.start_progress_reporting().await;
        }
        
        info!("✅ Shutdown manager started");
        Ok(())
    }
    
    /// Start signal handlers for graceful shutdown
    async fn start_signal_handlers(&self) {
        let shutdown_manager = self.clone();
        
        tokio::spawn(async move {
            let mut sigterm = signal::unix::signal(signal::unix::SignalKind::terminate())
                .expect("Failed to install SIGTERM handler");
            let mut sigint = signal::unix::signal(signal::unix::SignalKind::interrupt())
                .expect("Failed to install SIGINT handler");
            
            tokio::select! {
                _ = sigterm.recv() => {
                    info!("📡 Received SIGTERM, initiating graceful shutdown");
                    if let Err(e) = shutdown_manager.initiate_shutdown("SIGTERM received".to_string()).await {
                        error!("Failed to initiate shutdown: {}", e);
                    }
                }
                _ = sigint.recv() => {
                    info!("📡 Received SIGINT, initiating graceful shutdown");
                    if let Err(e) = shutdown_manager.initiate_shutdown("SIGINT received".to_string()).await {
                        error!("Failed to initiate shutdown: {}", e);
                    }
                }
            }
        });
    }
    
    /// Initiate graceful shutdown
    pub async fn initiate_shutdown(&self, reason: String) -> Result<()> {
        let mut state = self.state.write().await;
        
        if state.shutdown_initiated {
            warn!("Shutdown already initiated");
            return Ok(());
        }
        
        state.shutdown_initiated = true;
        state.shutdown_started_at = Some(Instant::now());
        state.shutdown_reason = Some(reason.clone());
        state.current_phase = ShutdownPhase::StopAccepting;
        state.phase_start_times.insert(ShutdownPhase::StopAccepting, Instant::now());
        
        drop(state);
        
        info!("🛑 Initiating graceful shutdown: {}", reason);
        
        // Start shutdown process
        self.execute_shutdown().await
    }
    
    /// Execute the shutdown process
    async fn execute_shutdown(&self) -> Result<()> {
        let start_time = Instant::now();
        
        // Phase 1: Stop accepting new requests
        if let Err(e) = self.execute_phase_1().await {
            error!("Phase 1 failed: {}", e);
            self.record_shutdown_error(format!("Phase 1 error: {}", e)).await;
        }
        
        // Phase 2: Drain existing connections
        if let Err(e) = self.execute_phase_2().await {
            error!("Phase 2 failed: {}", e);
            self.record_shutdown_error(format!("Phase 2 error: {}", e)).await;
        }
        
        // Phase 3: Cleanup resources
        if let Err(e) = self.execute_phase_3().await {
            error!("Phase 3 failed: {}", e);
            self.record_shutdown_error(format!("Phase 3 error: {}", e)).await;
        }
        
        // Phase 4: Final shutdown
        self.execute_phase_4().await?;
        
        let total_time = start_time.elapsed();
        info!("✅ Graceful shutdown completed in {:?}", total_time);
        
        // Mark shutdown as completed
        {
            let mut state = self.state.write().await;
            state.shutdown_completed = true;
            state.current_phase = ShutdownPhase::Shutdown;
        }
        
        Ok(())
    }
    
    /// Phase 1: Stop accepting new requests
    async fn execute_phase_1(&self) -> Result<()> {
        info!("🔄 Phase 1: Stop accepting new requests");
        
        // Update health status to draining
        {
            let mut health_status = self.health_status.write().await;
            *health_status = HealthStatus::Draining;
        }
        
        // Broadcast shutdown signal to stop accepting new requests
        if let Err(e) = self.shutdown_tx.send(ShutdownPhase::StopAccepting) {
            warn!("Failed to broadcast stop accepting signal: {}", e);
        }
        
        // Wait for health check grace period
        tokio::time::sleep(self.config.health_check_grace_period).await;
        
        // Update health status to unhealthy
        {
            let mut health_status = self.health_status.write().await;
            *health_status = HealthStatus::Unhealthy;
        }
        
        info!("✅ Phase 1 completed: No longer accepting new requests");
        Ok(())
    }
    
    /// Phase 2: Drain existing connections
    async fn execute_phase_2(&self) -> Result<()> {
        info!("🔄 Phase 2: Draining existing connections");
        
        {
            let mut state = self.state.write().await;
            state.current_phase = ShutdownPhase::Draining;
            state.phase_start_times.insert(ShutdownPhase::Draining, Instant::now());
        }
        
        // Broadcast draining signal
        if let Err(e) = self.shutdown_tx.send(ShutdownPhase::Draining) {
            warn!("Failed to broadcast draining signal: {}", e);
        }
        
        let drain_start = Instant::now();
        let mut check_interval = tokio::time::interval(self.config.drain_check_interval);
        
        loop {
            check_interval.tick().await;
            
            let active_count = self.get_active_connection_count().await;
            
            if active_count == 0 {
                info!("✅ All connections drained");
                break;
            }
            
            if drain_start.elapsed() >= self.config.connection_drain_timeout {
                warn!("⏰ Connection drain timeout reached, {} connections still active", active_count);
                
                if self.config.alert_on_long_connections {
                    self.alert_long_running_connections().await;
                }
                
                break;
            }
            
            debug!("🔄 Draining connections: {} active", active_count);
            self.update_progress().await;
        }
        
        info!("✅ Phase 2 completed: Connection draining finished");
        Ok(())
    }
    
    /// Phase 3: Cleanup resources
    async fn execute_phase_3(&self) -> Result<()> {
        info!("🔄 Phase 3: Cleaning up resources");
        
        {
            let mut state = self.state.write().await;
            state.current_phase = ShutdownPhase::Cleanup;
            state.phase_start_times.insert(ShutdownPhase::Cleanup, Instant::now());
        }
        
        // Broadcast cleanup signal
        if let Err(e) = self.shutdown_tx.send(ShutdownPhase::Cleanup) {
            warn!("Failed to broadcast cleanup signal: {}", e);
        }
        
        // Stop background tasks
        self.stop_background_tasks().await?;
        
        // Close remaining connections
        self.close_remaining_connections().await?;
        
        // Flush metrics and logs
        self.flush_metrics_and_logs().await?;
        
        info!("✅ Phase 3 completed: Resource cleanup finished");
        Ok(())
    }
    
    /// Phase 4: Final shutdown
    async fn execute_phase_4(&self) -> Result<()> {
        info!("🔄 Phase 4: Final shutdown");
        
        {
            let mut state = self.state.write().await;
            state.current_phase = ShutdownPhase::Shutdown;
            state.phase_start_times.insert(ShutdownPhase::Shutdown, Instant::now());
        }
        
        // Broadcast final shutdown signal
        if let Err(e) = self.shutdown_tx.send(ShutdownPhase::Shutdown) {
            warn!("Failed to broadcast shutdown signal: {}", e);
        }
        
        // Final progress update
        self.update_final_progress().await;
        
        info!("✅ Phase 4 completed: Final shutdown");
        Ok(())
    }
    
    /// Register a connection for tracking
    pub async fn register_connection(&self, connection_info: ConnectionInfo) {
        let mut connections = self.active_connections.write().await;
        connections.insert(connection_info.id.clone(), connection_info);
        
        self.update_progress().await;
    }
    
    /// Unregister a connection
    pub async fn unregister_connection(&self, connection_id: &str) {
        let mut connections = self.active_connections.write().await;
        connections.remove(connection_id);
        
        self.update_progress().await;
    }
    
    /// Register a background task
    pub async fn register_background_task(&self, task_handle: TaskHandle) {
        let mut tasks = self.background_tasks.write().await;
        tasks.push(task_handle);
    }
    
    /// Get shutdown signal receiver
    pub fn get_shutdown_receiver(&self) -> broadcast::Receiver<ShutdownPhase> {
        self.shutdown_tx.subscribe()
    }
    
    /// Get current health status
    pub async fn get_health_status(&self) -> HealthStatus {
        *self.health_status.read().await
    }
    
    /// Get shutdown state
    pub async fn get_shutdown_state(&self) -> ShutdownState {
        self.state.read().await.clone()
    }
    
    /// Get shutdown progress
    pub async fn get_shutdown_progress(&self) -> ShutdownProgress {
        self.progress.read().await.clone()
    }
    
    /// Get active connection count
    async fn get_active_connection_count(&self) -> usize {
        self.active_connections.read().await.len()
    }
    
    /// Stop background tasks
    async fn stop_background_tasks(&self) -> Result<()> {
        let mut tasks = self.background_tasks.write().await;
        
        info!("🛑 Stopping {} background tasks", tasks.len());
        
        // Send shutdown signals to tasks
        for task in tasks.iter() {
            if let Some(ref shutdown_tx) = task.shutdown_tx {
                if let Err(e) = shutdown_tx.send(true) {
                    warn!("Failed to send shutdown signal to task {}: {}", task.name, e);
                }
            }
        }
        
        // Wait for tasks to complete with timeout
        let timeout = tokio::time::timeout(
            self.config.cleanup_timeout,
            futures::future::join_all(tasks.drain(..).map(|task| task.handle))
        );
        
        match timeout.await {
            Ok(results) => {
                let failed_tasks = results.iter().filter(|r| r.is_err()).count();
                if failed_tasks > 0 {
                    warn!("⚠️ {} background tasks failed to stop cleanly", failed_tasks);
                }
            }
            Err(_) => {
                warn!("⏰ Background task shutdown timeout reached");
            }
        }
        
        Ok(())
    }
    
    /// Close remaining connections
    async fn close_remaining_connections(&self) -> Result<()> {
        let connections = self.active_connections.read().await;
        let connection_count = connections.len();
        
        if connection_count > 0 {
            warn!("🔌 Force closing {} remaining connections", connection_count);
            
            // In a real implementation, you would close the actual connections
            // For now, we'll just clear the tracking
            drop(connections);
            
            let mut connections = self.active_connections.write().await;
            connections.clear();
        }
        
        Ok(())
    }
    
    /// Flush metrics and logs
    async fn flush_metrics_and_logs(&self) -> Result<()> {
        info!("💾 Flushing metrics and logs");
        
        // Flush metrics if available
        if let Some(ref metrics) = self.metrics {
            // In a real implementation, you would flush metrics
            debug!("Flushing metrics");
        }
        
        // Flush logs
        // In a real implementation, you would flush log buffers
        debug!("Flushing logs");
        
        Ok(())
    }
    
    /// Update shutdown progress
    async fn update_progress(&self) {
        let active_connections = self.get_active_connection_count().await;
        let background_tasks = self.background_tasks.read().await.len();
        
        let state = self.state.read().await;
        let current_phase = state.current_phase;
        
        let overall_progress = match current_phase {
            ShutdownPhase::Running => 0.0,
            ShutdownPhase::StopAccepting => 0.25,
            ShutdownPhase::Draining => 0.5,
            ShutdownPhase::Cleanup => 0.75,
            ShutdownPhase::Shutdown => 1.0,
            ShutdownPhase::Emergency => 1.0,
        };
        
        let mut progress = self.progress.write().await;
        progress.active_connections = active_connections;
        progress.background_tasks = background_tasks;
        progress.overall_progress = overall_progress;
        progress.last_updated = Instant::now();
        
        // Estimate time remaining based on current phase
        if let Some(shutdown_start) = state.shutdown_started_at {
            let elapsed = shutdown_start.elapsed();
            let estimated_total = self.config.total_timeout;
            
            if elapsed < estimated_total {
                progress.estimated_time_remaining = Some(estimated_total - elapsed);
            }
        }
    }
    
    /// Update final progress
    async fn update_final_progress(&self) {
        let mut progress = self.progress.write().await;
        progress.overall_progress = 1.0;
        progress.current_phase_progress = 1.0;
        progress.estimated_time_remaining = Some(Duration::from_secs(0));
        progress.last_updated = Instant::now();
    }
    
    /// Start progress reporting
    async fn start_progress_reporting(&self) {
        let progress = self.progress.clone();
        let state = self.state.clone();
        let interval = self.config.progress_reporting_interval;
        
        tokio::spawn(async move {
            let mut reporting_interval = tokio::time::interval(interval);
            
            loop {
                reporting_interval.tick().await;
                
                let current_state = state.read().await;
                if !current_state.shutdown_initiated {
                    continue;
                }
                
                let current_progress = progress.read().await;
                
                info!("📊 Shutdown Progress: {:.1}% complete, {} active connections, {} background tasks",
                      current_progress.overall_progress * 100.0,
                      current_progress.active_connections,
                      current_progress.background_tasks);
                
                if current_progress.overall_progress >= 1.0 {
                    break;
                }
            }
        });
    }
    
    /// Alert on long-running connections
    async fn alert_long_running_connections(&self) {
        let connections = self.active_connections.read().await;
        let threshold = self.config.long_connection_threshold;
        let now = Instant::now();
        
        for (id, conn) in connections.iter() {
            if now.duration_since(conn.started_at) > threshold {
                warn!("⚠️ Long-running connection detected: {} ({}s old, {} active requests)",
                      id, now.duration_since(conn.started_at).as_secs(), conn.active_requests);
            }
        }
    }
    
    /// Record shutdown error
    async fn record_shutdown_error(&self, error: String) {
        let mut state = self.state.write().await;
        state.shutdown_errors.push(error);
    }
}

impl Clone for ShutdownManager {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            state: self.state.clone(),
            progress: self.progress.clone(),
            shutdown_tx: self.shutdown_tx.clone(),
            health_status: self.health_status.clone(),
            active_connections: self.active_connections.clone(),
            background_tasks: self.background_tasks.clone(),
            metrics: self.metrics.clone(),
        }
    }
}

impl ShutdownState {
    fn new() -> Self {
        Self {
            current_phase: ShutdownPhase::Running,
            shutdown_started_at: None,
            phase_start_times: HashMap::new(),
            shutdown_reason: None,
            shutdown_initiated: false,
            shutdown_completed: false,
            shutdown_errors: Vec::new(),
        }
    }
}

impl ShutdownProgress {
    fn new() -> Self {
        Self {
            active_connections: 0,
            background_tasks: 0,
            completed_phases: Vec::new(),
            current_phase_progress: 0.0,
            overall_progress: 0.0,
            estimated_time_remaining: None,
            last_updated: Instant::now(),
        }
    }
}

impl Default for ShutdownConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            total_timeout: Duration::from_secs(300), // 5 minutes
            stop_accepting_timeout: Duration::from_secs(30),
            connection_drain_timeout: Duration::from_secs(120), // 2 minutes
            cleanup_timeout: Duration::from_secs(60),
            force_shutdown_after_timeout: true,
            health_check_grace_period: Duration::from_secs(10),
            drain_check_interval: Duration::from_secs(5),
            enable_progress_reporting: true,
            progress_reporting_interval: Duration::from_secs(10),
            enable_alerts: true,
            alert_on_long_connections: true,
            long_connection_threshold: Duration::from_secs(60),
        }
    }
}
```

## Integration Example

Create `examples/graceful_shutdown_example.rs`:

```rust
//! Example demonstrating graceful shutdown with connection draining

use rusty_balancer::shutdown::{
    ShutdownManager, ShutdownConfig, ConnectionInfo, ConnectionType, TaskHandle
};
use std::time::{Duration, Instant};
use tokio::sync::watch;
use tracing::{info, warn};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("🛑 Starting Graceful Shutdown Example");

    // Create shutdown configuration
    let config = ShutdownConfig {
        enabled: true,
        total_timeout: Duration::from_secs(60),
        stop_accepting_timeout: Duration::from_secs(10),
        connection_drain_timeout: Duration::from_secs(30),
        cleanup_timeout: Duration::from_secs(15),
        health_check_grace_period: Duration::from_secs(5),
        drain_check_interval: Duration::from_secs(2),
        enable_progress_reporting: true,
        progress_reporting_interval: Duration::from_secs(5),
        ..Default::default()
    };

    // Create shutdown manager
    let shutdown_manager = ShutdownManager::new(config);
    shutdown_manager.start().await?;

    // Simulate running services
    info!("🚀 Starting simulated services");

    // Start HTTP server simulation
    let http_server = start_http_server_simulation(shutdown_manager.clone()).await;

    // Start background tasks
    start_background_tasks(shutdown_manager.clone()).await;

    // Simulate active connections
    simulate_active_connections(shutdown_manager.clone()).await;

    // Wait for shutdown signal (in real scenario, this would be your main application loop)
    info!("✅ Services started. Send SIGTERM or SIGINT to test graceful shutdown");
    info!("💡 Or wait 10 seconds for automatic shutdown demonstration");

    // Simulate automatic shutdown after 10 seconds for demonstration
    tokio::time::sleep(Duration::from_secs(10)).await;

    info!("🔔 Initiating shutdown for demonstration");
    shutdown_manager.initiate_shutdown("Demonstration shutdown".to_string()).await?;

    // Wait for shutdown to complete
    let mut check_interval = tokio::time::interval(Duration::from_secs(1));
    let shutdown_start = Instant::now();

    loop {
        check_interval.tick().await;

        let state = shutdown_manager.get_shutdown_state().await;
        let progress = shutdown_manager.get_shutdown_progress().await;

        info!("📊 Shutdown Status: Phase: {:?}, Progress: {:.1}%",
              state.current_phase, progress.overall_progress * 100.0);

        if state.shutdown_completed {
            info!("✅ Graceful shutdown completed successfully in {:?}",
                  shutdown_start.elapsed());
            break;
        }

        if shutdown_start.elapsed() > Duration::from_secs(70) {
            warn!("⏰ Shutdown taking longer than expected");
            break;
        }
    }

    // Wait for HTTP server to finish
    if let Err(e) = http_server.await {
        warn!("HTTP server task error: {:?}", e);
    }

    info!("🏁 Graceful shutdown example completed");
    Ok(())
}

/// Simulate HTTP server with graceful shutdown
async fn start_http_server_simulation(shutdown_manager: ShutdownManager) -> tokio::task::JoinHandle<()> {
    tokio::spawn(async move {
        let mut shutdown_rx = shutdown_manager.get_shutdown_receiver();
        let mut accepting_requests = true;
        let mut server_running = true;

        info!("🌐 HTTP server simulation started");

        // Simulate request handling loop
        let mut request_interval = tokio::time::interval(Duration::from_millis(500));

        while server_running {
            tokio::select! {
                _ = request_interval.tick() => {
                    if accepting_requests {
                        // Simulate incoming request
                        let connection_id = format!("http-{}", uuid::Uuid::new_v4());
                        let connection_info = ConnectionInfo {
                            id: connection_id.clone(),
                            started_at: Instant::now(),
                            connection_type: ConnectionType::Http,
                            remote_addr: "192.168.1.100:12345".to_string(),
                            active_requests: 1,
                            last_activity: Instant::now(),
                        };

                        shutdown_manager.register_connection(connection_info).await;

                        // Simulate request processing
                        let shutdown_manager_clone = shutdown_manager.clone();
                        tokio::spawn(async move {
                            // Simulate request processing time
                            tokio::time::sleep(Duration::from_millis(200 + rand::random::<u64>() % 800)).await;

                            // Unregister connection when request completes
                            shutdown_manager_clone.unregister_connection(&connection_id).await;
                        });
                    }
                }

                phase = shutdown_rx.recv() => {
                    match phase {
                        Ok(phase) => {
                            match phase {
                                rusty_balancer::shutdown::ShutdownPhase::StopAccepting => {
                                    info!("🛑 HTTP server stopped accepting new requests");
                                    accepting_requests = false;
                                }
                                rusty_balancer::shutdown::ShutdownPhase::Draining => {
                                    info!("🔄 HTTP server draining existing connections");
                                }
                                rusty_balancer::shutdown::ShutdownPhase::Cleanup => {
                                    info!("🧹 HTTP server cleaning up resources");
                                }
                                rusty_balancer::shutdown::ShutdownPhase::Shutdown => {
                                    info!("🏁 HTTP server shutting down");
                                    server_running = false;
                                }
                                _ => {}
                            }
                        }
                        Err(_) => {
                            info!("📡 Shutdown signal channel closed");
                            break;
                        }
                    }
                }
            }
        }

        info!("✅ HTTP server simulation completed");
    })
}

/// Start background tasks with shutdown handling
async fn start_background_tasks(shutdown_manager: ShutdownManager) {
    // Metrics collection task
    let (metrics_shutdown_tx, mut metrics_shutdown_rx) = watch::channel(false);
    let metrics_task = tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(5));

        info!("📊 Metrics collection task started");

        loop {
            tokio::select! {
                _ = interval.tick() => {
                    // Simulate metrics collection
                    info!("📊 Collecting metrics...");
                }
                _ = metrics_shutdown_rx.changed() => {
                    if *metrics_shutdown_rx.borrow() {
                        info!("🛑 Metrics collection task shutting down");
                        break;
                    }
                }
            }
        }

        info!("✅ Metrics collection task completed");
    });

    let metrics_task_handle = TaskHandle {
        name: "metrics_collector".to_string(),
        handle: metrics_task,
        shutdown_tx: Some(metrics_shutdown_tx),
        started_at: Instant::now(),
    };

    shutdown_manager.register_background_task(metrics_task_handle).await;

    // Health check task
    let (health_shutdown_tx, mut health_shutdown_rx) = watch::channel(false);
    let health_task = tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(10));

        info!("🏥 Health check task started");

        loop {
            tokio::select! {
                _ = interval.tick() => {
                    // Simulate health checks
                    info!("🏥 Performing health checks...");
                }
                _ = health_shutdown_rx.changed() => {
                    if *health_shutdown_rx.borrow() {
                        info!("🛑 Health check task shutting down");
                        break;
                    }
                }
            }
        }

        info!("✅ Health check task completed");
    });

    let health_task_handle = TaskHandle {
        name: "health_checker".to_string(),
        handle: health_task,
        shutdown_tx: Some(health_shutdown_tx),
        started_at: Instant::now(),
    };

    shutdown_manager.register_background_task(health_task_handle).await;

    // Log rotation task
    let (log_shutdown_tx, mut log_shutdown_rx) = watch::channel(false);
    let log_task = tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(30));

        info!("📝 Log rotation task started");

        loop {
            tokio::select! {
                _ = interval.tick() => {
                    // Simulate log rotation
                    info!("📝 Rotating logs...");
                }
                _ = log_shutdown_rx.changed() => {
                    if *log_shutdown_rx.borrow() {
                        info!("🛑 Log rotation task shutting down");
                        break;
                    }
                }
            }
        }

        info!("✅ Log rotation task completed");
    });

    let log_task_handle = TaskHandle {
        name: "log_rotator".to_string(),
        handle: log_task,
        shutdown_tx: Some(log_shutdown_tx),
        started_at: Instant::now(),
    };

    shutdown_manager.register_background_task(log_task_handle).await;
}

/// Simulate some long-running connections
async fn simulate_active_connections(shutdown_manager: ShutdownManager) {
    // WebSocket connection
    let ws_connection = ConnectionInfo {
        id: "websocket-long-lived".to_string(),
        started_at: Instant::now(),
        connection_type: ConnectionType::WebSocket,
        remote_addr: "192.168.1.200:54321".to_string(),
        active_requests: 0,
        last_activity: Instant::now(),
    };

    shutdown_manager.register_connection(ws_connection).await;

    // Backend connection
    let backend_connection = ConnectionInfo {
        id: "backend-pool-conn-1".to_string(),
        started_at: Instant::now(),
        connection_type: ConnectionType::Backend,
        remote_addr: "10.0.1.100:8080".to_string(),
        active_requests: 2,
        last_activity: Instant::now(),
    };

    shutdown_manager.register_connection(backend_connection).await;

    // Simulate connection cleanup during shutdown
    let shutdown_manager_clone = shutdown_manager.clone();
    tokio::spawn(async move {
        let mut shutdown_rx = shutdown_manager_clone.get_shutdown_receiver();

        while let Ok(phase) = shutdown_rx.recv().await {
            match phase {
                rusty_balancer::shutdown::ShutdownPhase::Draining => {
                    // Simulate WebSocket connection closing during drain
                    tokio::time::sleep(Duration::from_secs(8)).await;
                    shutdown_manager_clone.unregister_connection("websocket-long-lived").await;
                    info!("🔌 WebSocket connection closed during drain");
                }
                rusty_balancer::shutdown::ShutdownPhase::Cleanup => {
                    // Simulate backend connection closing during cleanup
                    tokio::time::sleep(Duration::from_secs(3)).await;
                    shutdown_manager_clone.unregister_connection("backend-pool-conn-1").await;
                    info!("🔌 Backend connection closed during cleanup");
                }
                _ => {}
            }
        }
    });
}
```

## Key Design Decisions Explained

### 1. Phased Shutdown Approach
**Decision**: Implement shutdown in distinct phases with clear transitions

**Rationale**:
- **Coordination**: Ensure proper order of shutdown operations
- **Safety**: Prevent data loss and connection drops
- **Observability**: Clear visibility into shutdown progress
- **Timeout Management**: Handle stuck operations gracefully

### 2. Connection Draining Strategy
**Decision**: Track active connections and wait for completion before shutdown

**Rationale**:
- **User Experience**: Complete active requests before shutdown
- **Data Integrity**: Ensure transactions are properly finished
- **Resource Management**: Clean up connections in controlled manner
- **Load Balancer Integration**: Coordinate with upstream load balancers

### 3. Health Check Integration
**Decision**: Update health status during shutdown to coordinate with load balancers

**Rationale**:
- **Traffic Redirection**: Signal load balancers to stop sending traffic
- **Graceful Transition**: Allow time for load balancer configuration updates
- **Service Discovery**: Update service registry during shutdown
- **Monitoring Integration**: Provide clear health status to monitoring systems

## Performance Considerations

1. **Shutdown Speed**: Balance between graceful shutdown and total time
2. **Resource Usage**: Minimal overhead during normal operation
3. **Connection Tracking**: Efficient tracking without impacting request performance
4. **Signal Handling**: Fast response to shutdown signals

## Security Considerations

1. **Signal Handling**: Secure handling of shutdown signals
2. **Resource Cleanup**: Proper cleanup to prevent resource leaks
3. **Connection Security**: Secure closure of encrypted connections
4. **Audit Trail**: Log shutdown events for security analysis

## Navigation
- [Previous: Circuit Breaking](17-circuit-breaking.md)
- [Next: Caching and Storage](19-caching-storage.md)
