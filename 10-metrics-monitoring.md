# Module 10: Metrics and Monitoring

## Learning Objectives
- Design a comprehensive metrics collection system for production monitoring
- Implement Prometheus-compatible metrics with proper labeling and cardinality
- Create real-time dashboards and alerting for operational visibility
- Build performance monitoring and capacity planning capabilities
- Understand observability best practices for distributed systems

## Why Metrics and Monitoring Matter

Comprehensive monitoring is essential for production load balancers because:

1. **Operational Visibility**: Real-time insight into system health and performance
2. **Capacity Planning**: Understanding usage patterns and growth trends
3. **Incident Response**: Quick detection and diagnosis of issues
4. **Performance Optimization**: Identifying bottlenecks and optimization opportunities
5. **SLA Compliance**: Tracking service level objectives and availability

### Monitoring Architecture

```mermaid
graph TD
    LB[Load Balancer] --> Collector[Metrics Collector]
    Collector --> Registry[Metrics Registry]
    Registry --> Exporter[Prometheus Exporter]
    
    Exporter --> Prometheus[Prometheus Server]
    Prometheus --> Grafana[Grafana Dashboard]
    Prometheus --> AlertManager[Alert Manager]
    
    AlertManager --> Slack[Slack Notifications]
    AlertManager --> PagerDuty[PagerDuty]
    AlertManager --> Email[Email <PERSON>s]
    
    Grafana --> Dashboard1[Performance Dashboard]
    Grafana --> Dashboard2[Health Dashboard]
    Grafana --> Dashboard3[Security Dashboard]
    
    subgraph "Metrics Types"
        Counter[Counters]
        Gauge[Gauges]
        Histogram[Histograms]
        Summary[Summaries]
    end
    
    Registry --> Counter
    Registry --> Gauge
    Registry --> Histogram
    Registry --> Summary
```

## Metrics Design and Implementation

### Design Decisions

**Why Prometheus metrics format?**
- **Industry Standard**: Widely adopted in cloud-native environments
- **Rich Ecosystem**: Extensive tooling and integrations available
- **Powerful Querying**: PromQL enables complex analysis and alerting
- **Scalability**: Designed for high-cardinality, high-volume metrics

**Why different metric types?**
- **Counters**: Track events that only increase (requests, errors)
- **Gauges**: Track values that can go up and down (connections, memory)
- **Histograms**: Track distributions of values (response times, request sizes)
- **Summaries**: Track quantiles and totals (percentiles, averages)

Create `src/metrics/mod.rs`:

```rust
//! Comprehensive metrics collection and monitoring system
//!
//! This module provides a production-ready metrics system with:
//! - Prometheus-compatible metrics export
//! - Real-time performance monitoring
//! - Health and availability tracking
//! - Security and operational metrics
//! - Custom business metrics support

pub mod collector;
pub mod registry;
pub mod exporter;
pub mod dashboard;

use prometheus::{
    Counter, CounterVec, Gauge, GaugeVec, Histogram, HistogramVec,
    Registry, Opts, HistogramOpts, register_counter_vec_with_registry,
    register_gauge_vec_with_registry, register_histogram_vec_with_registry,
};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug};

/// Comprehensive metrics collector for the load balancer
pub struct LoadBalancerMetrics {
    /// Prometheus metrics registry
    registry: Registry,
    
    /// Request metrics
    pub requests_total: CounterVec,
    pub request_duration: HistogramVec,
    pub request_size_bytes: HistogramVec,
    pub response_size_bytes: HistogramVec,
    
    /// Backend metrics
    pub backend_requests_total: CounterVec,
    pub backend_request_duration: HistogramVec,
    pub backend_connections_active: GaugeVec,
    pub backend_health_status: GaugeVec,
    
    /// Load balancer metrics
    pub active_connections: Gauge,
    pub connection_pool_size: GaugeVec,
    pub load_balancer_uptime: Counter,
    
    /// Error metrics
    pub errors_total: CounterVec,
    pub circuit_breaker_state: GaugeVec,
    pub rate_limit_hits: CounterVec,
    
    /// Performance metrics
    pub memory_usage_bytes: Gauge,
    pub cpu_usage_percent: Gauge,
    pub file_descriptors_used: Gauge,
    pub goroutines_count: Gauge,
    
    /// Security metrics
    pub security_events_total: CounterVec,
    pub tls_handshakes_total: CounterVec,
    pub auth_attempts_total: CounterVec,
    
    /// Business metrics
    pub custom_metrics: Arc<RwLock<HashMap<String, Box<dyn CustomMetric + Send + Sync>>>>,
}

impl LoadBalancerMetrics {
    /// Create a new metrics collector
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let registry = Registry::new();
        
        // Request metrics
        let requests_total = register_counter_vec_with_registry!(
            Opts::new("rusty_lb_requests_total", "Total number of HTTP requests"),
            &["method", "status_code", "backend"],
            registry
        )?;
        
        let request_duration = register_histogram_vec_with_registry!(
            HistogramOpts::new("rusty_lb_request_duration_seconds", "Request duration in seconds")
                .buckets(vec![0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]),
            &["method", "status_code", "backend"],
            registry
        )?;
        
        let request_size_bytes = register_histogram_vec_with_registry!(
            HistogramOpts::new("rusty_lb_request_size_bytes", "Request size in bytes")
                .buckets(vec![64.0, 256.0, 1024.0, 4096.0, 16384.0, 65536.0, 262144.0, 1048576.0]),
            &["method"],
            registry
        )?;
        
        let response_size_bytes = register_histogram_vec_with_registry!(
            HistogramOpts::new("rusty_lb_response_size_bytes", "Response size in bytes")
                .buckets(vec![64.0, 256.0, 1024.0, 4096.0, 16384.0, 65536.0, 262144.0, 1048576.0]),
            &["status_code", "backend"],
            registry
        )?;
        
        // Backend metrics
        let backend_requests_total = register_counter_vec_with_registry!(
            Opts::new("rusty_lb_backend_requests_total", "Total requests to backend servers"),
            &["backend", "status_code"],
            registry
        )?;
        
        let backend_request_duration = register_histogram_vec_with_registry!(
            HistogramOpts::new("rusty_lb_backend_request_duration_seconds", "Backend request duration")
                .buckets(vec![0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]),
            &["backend"],
            registry
        )?;
        
        let backend_connections_active = register_gauge_vec_with_registry!(
            Opts::new("rusty_lb_backend_connections_active", "Active connections to backends"),
            &["backend"],
            registry
        )?;
        
        let backend_health_status = register_gauge_vec_with_registry!(
            Opts::new("rusty_lb_backend_health_status", "Backend health status (1=healthy, 0=unhealthy)"),
            &["backend"],
            registry
        )?;
        
        // Load balancer metrics
        let active_connections = prometheus::register_gauge_with_registry!(
            Opts::new("rusty_lb_active_connections", "Current active connections"),
            registry
        )?;
        
        let connection_pool_size = register_gauge_vec_with_registry!(
            Opts::new("rusty_lb_connection_pool_size", "Connection pool size"),
            &["backend", "state"],
            registry
        )?;
        
        let load_balancer_uptime = prometheus::register_counter_with_registry!(
            Opts::new("rusty_lb_uptime_seconds_total", "Load balancer uptime in seconds"),
            registry
        )?;
        
        // Error metrics
        let errors_total = register_counter_vec_with_registry!(
            Opts::new("rusty_lb_errors_total", "Total number of errors"),
            &["error_type", "component"],
            registry
        )?;
        
        let circuit_breaker_state = register_gauge_vec_with_registry!(
            Opts::new("rusty_lb_circuit_breaker_state", "Circuit breaker state (0=closed, 1=open, 2=half-open)"),
            &["backend"],
            registry
        )?;
        
        let rate_limit_hits = register_counter_vec_with_registry!(
            Opts::new("rusty_lb_rate_limit_hits_total", "Rate limit hits"),
            &["client_ip", "limit_type"],
            registry
        )?;
        
        // Performance metrics
        let memory_usage_bytes = prometheus::register_gauge_with_registry!(
            Opts::new("rusty_lb_memory_usage_bytes", "Memory usage in bytes"),
            registry
        )?;
        
        let cpu_usage_percent = prometheus::register_gauge_with_registry!(
            Opts::new("rusty_lb_cpu_usage_percent", "CPU usage percentage"),
            registry
        )?;
        
        let file_descriptors_used = prometheus::register_gauge_with_registry!(
            Opts::new("rusty_lb_file_descriptors_used", "Number of file descriptors in use"),
            registry
        )?;
        
        let goroutines_count = prometheus::register_gauge_with_registry!(
            Opts::new("rusty_lb_goroutines_count", "Number of goroutines"),
            registry
        )?;
        
        // Security metrics
        let security_events_total = register_counter_vec_with_registry!(
            Opts::new("rusty_lb_security_events_total", "Security events"),
            &["event_type", "severity", "client_ip"],
            registry
        )?;
        
        let tls_handshakes_total = register_counter_vec_with_registry!(
            Opts::new("rusty_lb_tls_handshakes_total", "TLS handshakes"),
            &["status", "version"],
            registry
        )?;
        
        let auth_attempts_total = register_counter_vec_with_registry!(
            Opts::new("rusty_lb_auth_attempts_total", "Authentication attempts"),
            &["status", "method"],
            registry
        )?;
        
        info!("📊 Metrics system initialized with {} collectors", 18);
        
        Ok(Self {
            registry,
            requests_total,
            request_duration,
            request_size_bytes,
            response_size_bytes,
            backend_requests_total,
            backend_request_duration,
            backend_connections_active,
            backend_health_status,
            active_connections,
            connection_pool_size,
            load_balancer_uptime,
            errors_total,
            circuit_breaker_state,
            rate_limit_hits,
            memory_usage_bytes,
            cpu_usage_percent,
            file_descriptors_used,
            goroutines_count,
            security_events_total,
            tls_handshakes_total,
            auth_attempts_total,
            custom_metrics: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    /// Get the Prometheus registry for exporting metrics
    pub fn registry(&self) -> &Registry {
        &self.registry
    }
    
    /// Record a completed HTTP request
    pub fn record_request(
        &self,
        method: &str,
        status_code: u16,
        backend: &str,
        duration: Duration,
        request_size: Option<u64>,
        response_size: Option<u64>,
    ) {
        let status_str = status_code.to_string();
        
        // Record request count
        self.requests_total
            .with_label_values(&[method, &status_str, backend])
            .inc();
        
        // Record request duration
        self.request_duration
            .with_label_values(&[method, &status_str, backend])
            .observe(duration.as_secs_f64());
        
        // Record request size
        if let Some(size) = request_size {
            self.request_size_bytes
                .with_label_values(&[method])
                .observe(size as f64);
        }
        
        // Record response size
        if let Some(size) = response_size {
            self.response_size_bytes
                .with_label_values(&[&status_str, backend])
                .observe(size as f64);
        }
        
        debug!(
            method = method,
            status_code = status_code,
            backend = backend,
            duration_ms = duration.as_millis(),
            "Recorded request metrics"
        );
    }
    
    /// Record backend request metrics
    pub fn record_backend_request(
        &self,
        backend: &str,
        status_code: u16,
        duration: Duration,
    ) {
        let status_str = status_code.to_string();
        
        self.backend_requests_total
            .with_label_values(&[backend, &status_str])
            .inc();
        
        self.backend_request_duration
            .with_label_values(&[backend])
            .observe(duration.as_secs_f64());
    }
    
    /// Update backend health status
    pub fn update_backend_health(&self, backend: &str, is_healthy: bool) {
        let health_value = if is_healthy { 1.0 } else { 0.0 };
        self.backend_health_status
            .with_label_values(&[backend])
            .set(health_value);
    }
    
    /// Update active connections count
    pub fn update_active_connections(&self, count: i64) {
        self.active_connections.set(count as f64);
    }
    
    /// Update backend connection pool metrics
    pub fn update_connection_pool(&self, backend: &str, active: usize, idle: usize) {
        self.connection_pool_size
            .with_label_values(&[backend, "active"])
            .set(active as f64);
        
        self.connection_pool_size
            .with_label_values(&[backend, "idle"])
            .set(idle as f64);
    }
    
    /// Record an error
    pub fn record_error(&self, error_type: &str, component: &str) {
        self.errors_total
            .with_label_values(&[error_type, component])
            .inc();
    }
    
    /// Update circuit breaker state
    pub fn update_circuit_breaker_state(&self, backend: &str, state: CircuitBreakerState) {
        let state_value = match state {
            CircuitBreakerState::Closed => 0.0,
            CircuitBreakerState::Open => 1.0,
            CircuitBreakerState::HalfOpen => 2.0,
        };
        
        self.circuit_breaker_state
            .with_label_values(&[backend])
            .set(state_value);
    }
    
    /// Record rate limit hit
    pub fn record_rate_limit_hit(&self, client_ip: &str, limit_type: &str) {
        self.rate_limit_hits
            .with_label_values(&[client_ip, limit_type])
            .inc();
    }
    
    /// Update system performance metrics
    pub fn update_system_metrics(&self, metrics: SystemMetrics) {
        self.memory_usage_bytes.set(metrics.memory_usage_bytes as f64);
        self.cpu_usage_percent.set(metrics.cpu_usage_percent);
        self.file_descriptors_used.set(metrics.file_descriptors_used as f64);
        self.goroutines_count.set(metrics.goroutines_count as f64);
    }
    
    /// Record security event
    pub fn record_security_event(
        &self,
        event_type: &str,
        severity: &str,
        client_ip: &str,
    ) {
        self.security_events_total
            .with_label_values(&[event_type, severity, client_ip])
            .inc();
    }
    
    /// Record TLS handshake
    pub fn record_tls_handshake(&self, success: bool, version: &str) {
        let status = if success { "success" } else { "failure" };
        self.tls_handshakes_total
            .with_label_values(&[status, version])
            .inc();
    }
    
    /// Record authentication attempt
    pub fn record_auth_attempt(&self, success: bool, method: &str) {
        let status = if success { "success" } else { "failure" };
        self.auth_attempts_total
            .with_label_values(&[status, method])
            .inc();
    }
    
    /// Add a custom metric
    pub async fn add_custom_metric(&self, name: String, metric: Box<dyn CustomMetric + Send + Sync>) {
        let mut custom_metrics = self.custom_metrics.write().await;
        custom_metrics.insert(name, metric);
    }
    
    /// Update uptime counter
    pub fn update_uptime(&self, uptime_seconds: f64) {
        // Reset and set the counter to current uptime
        // Note: In a real implementation, you might want to use a gauge instead
        self.load_balancer_uptime.inc_by(uptime_seconds);
    }
}

/// Circuit breaker states for metrics
#[derive(Debug, Clone, Copy)]
pub enum CircuitBreakerState {
    Closed,
    Open,
    HalfOpen,
}

/// System performance metrics
#[derive(Debug, Clone)]
pub struct SystemMetrics {
    pub memory_usage_bytes: u64,
    pub cpu_usage_percent: f64,
    pub file_descriptors_used: u64,
    pub goroutines_count: u64,
}

/// Trait for custom metrics
pub trait CustomMetric {
    fn name(&self) -> &str;
    fn help(&self) -> &str;
    fn metric_type(&self) -> &str;
    fn value(&self) -> f64;
    fn labels(&self) -> Vec<(&str, &str)>;
}

/// Request metrics tracker for individual requests
pub struct RequestMetricsTracker {
    start_time: Instant,
    method: String,
    uri: String,
    backend: Option<String>,
    request_size: Option<u64>,
}

impl RequestMetricsTracker {
    /// Create a new request tracker
    pub fn new(method: String, uri: String) -> Self {
        Self {
            start_time: Instant::now(),
            method,
            uri,
            backend: None,
            request_size: None,
        }
    }
    
    /// Set the backend that handled the request
    pub fn set_backend(&mut self, backend: String) {
        self.backend = Some(backend);
    }
    
    /// Set the request size
    pub fn set_request_size(&mut self, size: u64) {
        self.request_size = Some(size);
    }
    
    /// Finish the request and record metrics
    pub fn finish(
        &self,
        metrics: &LoadBalancerMetrics,
        status_code: u16,
        response_size: Option<u64>,
    ) {
        let duration = self.start_time.elapsed();
        let backend = self.backend.as_deref().unwrap_or("unknown");
        
        metrics.record_request(
            &self.method,
            status_code,
            backend,
            duration,
            self.request_size,
            response_size,
        );
    }
}
```

## Metrics Exporter Implementation

Create `src/metrics/exporter.rs`:

```rust
//! Prometheus metrics exporter with HTTP endpoint
//!
//! This module provides an HTTP server that exposes metrics in Prometheus format
//! for scraping by monitoring systems.

use crate::metrics::LoadBalancerMetrics;
use hyper::{Body, Request, Response, Server, StatusCode};
use hyper::service::{make_service_fn, service_fn};
use prometheus::{Encoder, TextEncoder};
use std::convert::Infallible;
use std::net::SocketAddr;
use std::sync::Arc;
use tracing::{info, warn, error, debug};

/// Prometheus metrics exporter
pub struct MetricsExporter {
    /// Metrics collector
    metrics: Arc<LoadBalancerMetrics>,

    /// Bind address for metrics endpoint
    bind_addr: SocketAddr,

    /// Metrics endpoint path
    endpoint_path: String,
}

impl MetricsExporter {
    /// Create a new metrics exporter
    pub fn new(
        metrics: Arc<LoadBalancerMetrics>,
        bind_addr: SocketAddr,
    ) -> Self {
        Self {
            metrics,
            bind_addr,
            endpoint_path: "/metrics".to_string(),
        }
    }

    /// Set custom endpoint path
    pub fn with_endpoint_path(mut self, path: String) -> Self {
        self.endpoint_path = path;
        self
    }

    /// Start the metrics HTTP server
    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error>> {
        let metrics = self.metrics.clone();
        let endpoint_path = self.endpoint_path.clone();

        let make_svc = make_service_fn(move |_conn| {
            let metrics = metrics.clone();
            let endpoint_path = endpoint_path.clone();

            async move {
                Ok::<_, Infallible>(service_fn(move |req| {
                    Self::handle_request(req, metrics.clone(), endpoint_path.clone())
                }))
            }
        });

        let server = Server::bind(&self.bind_addr).serve(make_svc);

        info!("📊 Metrics server listening on {} at {}",
              self.bind_addr, self.endpoint_path);

        if let Err(e) = server.await {
            error!("Metrics server error: {}", e);
        }

        Ok(())
    }

    /// Handle HTTP requests to the metrics endpoint
    async fn handle_request(
        req: Request<Body>,
        metrics: Arc<LoadBalancerMetrics>,
        endpoint_path: String,
    ) -> Result<Response<Body>, Infallible> {
        let path = req.uri().path();

        match (req.method(), path) {
            (&hyper::Method::GET, path) if path == endpoint_path => {
                debug!("Serving metrics at {}", path);

                match Self::export_metrics(&metrics).await {
                    Ok(metrics_output) => {
                        Ok(Response::builder()
                            .status(StatusCode::OK)
                            .header("content-type", "text/plain; version=0.0.4; charset=utf-8")
                            .header("cache-control", "no-cache")
                            .body(Body::from(metrics_output))
                            .unwrap())
                    }
                    Err(e) => {
                        error!("Failed to export metrics: {}", e);
                        Ok(Response::builder()
                            .status(StatusCode::INTERNAL_SERVER_ERROR)
                            .body(Body::from("Failed to export metrics"))
                            .unwrap())
                    }
                }
            }
            (&hyper::Method::GET, "/health") => {
                // Health check endpoint for the metrics server itself
                Ok(Response::builder()
                    .status(StatusCode::OK)
                    .header("content-type", "application/json")
                    .body(Body::from(r#"{"status": "healthy", "service": "metrics-exporter"}"#))
                    .unwrap())
            }
            _ => {
                warn!("Invalid request to metrics server: {} {}", req.method(), path);
                Ok(Response::builder()
                    .status(StatusCode::NOT_FOUND)
                    .body(Body::from("Not Found"))
                    .unwrap())
            }
        }
    }

    /// Export metrics in Prometheus format
    async fn export_metrics(
        metrics: &LoadBalancerMetrics,
    ) -> Result<String, Box<dyn std::error::Error>> {
        let encoder = TextEncoder::new();
        let metric_families = metrics.registry().gather();

        let mut buffer = Vec::new();
        encoder.encode(&metric_families, &mut buffer)?;

        // Add custom metrics
        let custom_metrics = metrics.custom_metrics.read().await;
        for (name, metric) in custom_metrics.iter() {
            let labels = metric.labels()
                .iter()
                .map(|(k, v)| format!("{}=\"{}\"", k, v))
                .collect::<Vec<_>>()
                .join(",");

            let labels_str = if labels.is_empty() {
                String::new()
            } else {
                format!("{{{}}}", labels)
            };

            buffer.extend_from_slice(
                format!(
                    "# HELP {} {}\n# TYPE {} {}\n{}{} {}\n",
                    name,
                    metric.help(),
                    name,
                    metric.metric_type(),
                    name,
                    labels_str,
                    metric.value()
                ).as_bytes()
            );
        }

        Ok(String::from_utf8(buffer)?)
    }
}

/// System metrics collector for performance monitoring
pub struct SystemMetricsCollector {
    /// Metrics instance
    metrics: Arc<LoadBalancerMetrics>,

    /// Collection interval
    interval: std::time::Duration,
}

impl SystemMetricsCollector {
    /// Create a new system metrics collector
    pub fn new(
        metrics: Arc<LoadBalancerMetrics>,
        interval: std::time::Duration,
    ) -> Self {
        Self {
            metrics,
            interval,
        }
    }

    /// Start collecting system metrics
    pub async fn start(&self) {
        let mut interval = tokio::time::interval(self.interval);

        info!("🔍 System metrics collection started (interval: {:?})", self.interval);

        loop {
            interval.tick().await;

            if let Err(e) = self.collect_metrics().await {
                warn!("Failed to collect system metrics: {}", e);
            }
        }
    }

    /// Collect current system metrics
    async fn collect_metrics(&self) -> Result<(), Box<dyn std::error::Error>> {
        let system_metrics = self.get_system_metrics().await?;
        self.metrics.update_system_metrics(system_metrics);

        debug!("System metrics updated");
        Ok(())
    }

    /// Get current system metrics
    async fn get_system_metrics(&self) -> Result<crate::metrics::SystemMetrics, Box<dyn std::error::Error>> {
        // In a real implementation, you'd use system APIs or libraries like `sysinfo`
        // For now, we'll simulate the metrics

        use rand::Rng;
        let mut rng = rand::thread_rng();

        Ok(crate::metrics::SystemMetrics {
            memory_usage_bytes: rng.gen_range(100_000_000..500_000_000), // 100MB - 500MB
            cpu_usage_percent: rng.gen_range(5.0..25.0), // 5% - 25%
            file_descriptors_used: rng.gen_range(100..1000),
            goroutines_count: rng.gen_range(50..200),
        })
    }
}
```

## Dashboard Configuration

Create `src/metrics/dashboard.rs`:

```rust
//! Grafana dashboard configuration and templates
//!
//! This module provides pre-configured Grafana dashboards for monitoring
//! the load balancer's performance, health, and security metrics.

use serde_json::{json, Value};
use std::collections::HashMap;

/// Grafana dashboard generator
pub struct DashboardGenerator {
    /// Dashboard templates
    templates: HashMap<String, Value>,
}

impl DashboardGenerator {
    /// Create a new dashboard generator
    pub fn new() -> Self {
        let mut generator = Self {
            templates: HashMap::new(),
        };

        generator.load_templates();
        generator
    }

    /// Load dashboard templates
    fn load_templates(&mut self) {
        self.templates.insert(
            "performance".to_string(),
            self.create_performance_dashboard(),
        );

        self.templates.insert(
            "health".to_string(),
            self.create_health_dashboard(),
        );

        self.templates.insert(
            "security".to_string(),
            self.create_security_dashboard(),
        );
    }

    /// Get a dashboard template by name
    pub fn get_dashboard(&self, name: &str) -> Option<&Value> {
        self.templates.get(name)
    }

    /// Create performance monitoring dashboard
    fn create_performance_dashboard(&self) -> Value {
        json!({
            "dashboard": {
                "id": null,
                "title": "Rusty Load Balancer - Performance",
                "tags": ["rusty-lb", "performance"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "Request Rate",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "rate(rusty_lb_requests_total[5m])",
                                "legendFormat": "{{method}} {{status_code}}"
                            }
                        ],
                        "yAxes": [
                            {
                                "label": "Requests/sec"
                            }
                        ],
                        "gridPos": {
                            "h": 8,
                            "w": 12,
                            "x": 0,
                            "y": 0
                        }
                    },
                    {
                        "id": 2,
                        "title": "Response Time",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "histogram_quantile(0.95, rate(rusty_lb_request_duration_seconds_bucket[5m]))",
                                "legendFormat": "95th percentile"
                            },
                            {
                                "expr": "histogram_quantile(0.50, rate(rusty_lb_request_duration_seconds_bucket[5m]))",
                                "legendFormat": "50th percentile"
                            }
                        ],
                        "yAxes": [
                            {
                                "label": "Seconds"
                            }
                        ],
                        "gridPos": {
                            "h": 8,
                            "w": 12,
                            "x": 12,
                            "y": 0
                        }
                    },
                    {
                        "id": 3,
                        "title": "Active Connections",
                        "type": "singlestat",
                        "targets": [
                            {
                                "expr": "rusty_lb_active_connections",
                                "legendFormat": "Active Connections"
                            }
                        ],
                        "gridPos": {
                            "h": 4,
                            "w": 6,
                            "x": 0,
                            "y": 8
                        }
                    },
                    {
                        "id": 4,
                        "title": "Memory Usage",
                        "type": "singlestat",
                        "targets": [
                            {
                                "expr": "rusty_lb_memory_usage_bytes / 1024 / 1024",
                                "legendFormat": "Memory (MB)"
                            }
                        ],
                        "gridPos": {
                            "h": 4,
                            "w": 6,
                            "x": 6,
                            "y": 8
                        }
                    },
                    {
                        "id": 5,
                        "title": "CPU Usage",
                        "type": "singlestat",
                        "targets": [
                            {
                                "expr": "rusty_lb_cpu_usage_percent",
                                "legendFormat": "CPU %"
                            }
                        ],
                        "gridPos": {
                            "h": 4,
                            "w": 6,
                            "x": 12,
                            "y": 8
                        }
                    },
                    {
                        "id": 6,
                        "title": "Error Rate",
                        "type": "singlestat",
                        "targets": [
                            {
                                "expr": "rate(rusty_lb_errors_total[5m])",
                                "legendFormat": "Errors/sec"
                            }
                        ],
                        "gridPos": {
                            "h": 4,
                            "w": 6,
                            "x": 18,
                            "y": 8
                        }
                    }
                ],
                "time": {
                    "from": "now-1h",
                    "to": "now"
                },
                "refresh": "5s"
            }
        })
    }

    /// Create health monitoring dashboard
    fn create_health_dashboard(&self) -> Value {
        json!({
            "dashboard": {
                "id": null,
                "title": "Rusty Load Balancer - Health",
                "tags": ["rusty-lb", "health"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "Backend Health Status",
                        "type": "table",
                        "targets": [
                            {
                                "expr": "rusty_lb_backend_health_status",
                                "legendFormat": "{{backend}}"
                            }
                        ],
                        "gridPos": {
                            "h": 8,
                            "w": 12,
                            "x": 0,
                            "y": 0
                        }
                    },
                    {
                        "id": 2,
                        "title": "Circuit Breaker Status",
                        "type": "table",
                        "targets": [
                            {
                                "expr": "rusty_lb_circuit_breaker_state",
                                "legendFormat": "{{backend}}"
                            }
                        ],
                        "gridPos": {
                            "h": 8,
                            "w": 12,
                            "x": 12,
                            "y": 0
                        }
                    },
                    {
                        "id": 3,
                        "title": "Backend Response Time",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "histogram_quantile(0.95, rate(rusty_lb_backend_request_duration_seconds_bucket[5m]))",
                                "legendFormat": "{{backend}} 95th percentile"
                            }
                        ],
                        "yAxes": [
                            {
                                "label": "Seconds"
                            }
                        ],
                        "gridPos": {
                            "h": 8,
                            "w": 24,
                            "x": 0,
                            "y": 8
                        }
                    }
                ],
                "time": {
                    "from": "now-1h",
                    "to": "now"
                },
                "refresh": "10s"
            }
        })
    }

    /// Create security monitoring dashboard
    fn create_security_dashboard(&self) -> Value {
        json!({
            "dashboard": {
                "id": null,
                "title": "Rusty Load Balancer - Security",
                "tags": ["rusty-lb", "security"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "Security Events",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "rate(rusty_lb_security_events_total[5m])",
                                "legendFormat": "{{event_type}} {{severity}}"
                            }
                        ],
                        "yAxes": [
                            {
                                "label": "Events/sec"
                            }
                        ],
                        "gridPos": {
                            "h": 8,
                            "w": 12,
                            "x": 0,
                            "y": 0
                        }
                    },
                    {
                        "id": 2,
                        "title": "Rate Limit Hits",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "rate(rusty_lb_rate_limit_hits_total[5m])",
                                "legendFormat": "{{limit_type}}"
                            }
                        ],
                        "yAxes": [
                            {
                                "label": "Hits/sec"
                            }
                        ],
                        "gridPos": {
                            "h": 8,
                            "w": 12,
                            "x": 12,
                            "y": 0
                        }
                    },
                    {
                        "id": 3,
                        "title": "TLS Handshakes",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "rate(rusty_lb_tls_handshakes_total[5m])",
                                "legendFormat": "{{status}} {{version}}"
                            }
                        ],
                        "yAxes": [
                            {
                                "label": "Handshakes/sec"
                            }
                        ],
                        "gridPos": {
                            "h": 8,
                            "w": 12,
                            "x": 0,
                            "y": 8
                        }
                    },
                    {
                        "id": 4,
                        "title": "Authentication Attempts",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "rate(rusty_lb_auth_attempts_total[5m])",
                                "legendFormat": "{{status}} {{method}}"
                            }
                        ],
                        "yAxes": [
                            {
                                "label": "Attempts/sec"
                            }
                        ],
                        "gridPos": {
                            "h": 8,
                            "w": 12,
                            "x": 12,
                            "y": 8
                        }
                    }
                ],
                "time": {
                    "from": "now-1h",
                    "to": "now"
                },
                "refresh": "10s"
            }
        })
    }

    /// Export dashboard as JSON string
    pub fn export_dashboard(&self, name: &str) -> Result<String, Box<dyn std::error::Error>> {
        match self.get_dashboard(name) {
            Some(dashboard) => Ok(serde_json::to_string_pretty(dashboard)?),
            None => Err(format!("Dashboard '{}' not found", name).into()),
        }
    }

    /// List available dashboards
    pub fn list_dashboards(&self) -> Vec<&String> {
        self.templates.keys().collect()
    }
}

impl Default for DashboardGenerator {
    fn default() -> Self {
        Self::new()
    }
}
```

## Integration with Load Balancer

Update `src/main.rs` to include comprehensive metrics:

```rust
use rusty_balancer::metrics::{LoadBalancerMetrics, MetricsExporter, SystemMetricsCollector};
use rusty_balancer::config::Config;
use std::sync::Arc;
use std::time::Duration;
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("🚀 Starting Rusty Load Balancer with comprehensive metrics");

    // Load configuration
    let config = Config::default();

    // Initialize metrics system
    let metrics = Arc::new(LoadBalancerMetrics::new()?);
    info!("📊 Metrics system initialized");

    // Start metrics exporter
    let metrics_exporter = MetricsExporter::new(
        metrics.clone(),
        config.observability.metrics.address,
    );

    let metrics_clone = metrics.clone();
    tokio::spawn(async move {
        if let Err(e) = metrics_exporter.start().await {
            error!("Metrics exporter error: {}", e);
        }
    });

    // Start system metrics collector
    let system_collector = SystemMetricsCollector::new(
        metrics.clone(),
        config.observability.metrics.interval,
    );

    let metrics_clone = metrics.clone();
    tokio::spawn(async move {
        system_collector.start().await;
    });

    // Create and start the load balancer with metrics integration
    let proxy = create_proxy_with_metrics(metrics.clone(), config).await?;

    // Start uptime tracking
    let uptime_metrics = metrics.clone();
    tokio::spawn(async move {
        let start_time = std::time::Instant::now();
        let mut interval = tokio::time::interval(Duration::from_secs(60));

        loop {
            interval.tick().await;
            let uptime_seconds = start_time.elapsed().as_secs_f64();
            uptime_metrics.update_uptime(uptime_seconds);
        }
    });

    // Start the proxy server
    let bind_addr = config.server.bind_address;
    info!("🌐 Load balancer listening on {}", bind_addr);

    proxy.start(bind_addr).await?;

    Ok(())
}

async fn create_proxy_with_metrics(
    metrics: Arc<LoadBalancerMetrics>,
    config: Config,
) -> Result<MetricsAwareProxy, Box<dyn std::error::Error>> {
    // Create backend pool
    let backend_pool = Arc::new(rusty_balancer::backend::BackendPool::new());

    // Add backends from configuration
    for server_config in &config.backends.servers {
        backend_pool.add_weighted_backend(
            server_config.address,
            server_config.weight,
        )?;

        // Initialize backend health metrics
        metrics.update_backend_health(
            &server_config.address.to_string(),
            true, // Assume healthy initially
        );

        info!("Added backend: {} (weight: {})",
              server_config.address, server_config.weight);
    }

    Ok(MetricsAwareProxy::new(backend_pool, metrics, config))
}

/// HTTP proxy with integrated metrics collection
pub struct MetricsAwareProxy {
    backend_pool: Arc<rusty_balancer::backend::BackendPool>,
    metrics: Arc<LoadBalancerMetrics>,
    config: Config,
}

impl MetricsAwareProxy {
    pub fn new(
        backend_pool: Arc<rusty_balancer::backend::BackendPool>,
        metrics: Arc<LoadBalancerMetrics>,
        config: Config,
    ) -> Self {
        Self {
            backend_pool,
            metrics,
            config,
        }
    }

    pub async fn start(&self, bind_addr: std::net::SocketAddr) -> Result<(), Box<dyn std::error::Error>> {
        use hyper::{Body, Request, Response, Server};
        use hyper::service::{make_service_fn, service_fn};
        use std::convert::Infallible;

        let backend_pool = self.backend_pool.clone();
        let metrics = self.metrics.clone();
        let config = self.config.clone();

        let make_svc = make_service_fn(move |_conn| {
            let backend_pool = backend_pool.clone();
            let metrics = metrics.clone();
            let config = config.clone();

            async move {
                Ok::<_, Infallible>(service_fn(move |req| {
                    Self::handle_request_with_metrics(
                        req,
                        backend_pool.clone(),
                        metrics.clone(),
                        config.clone(),
                    )
                }))
            }
        });

        let server = Server::bind(&bind_addr).serve(make_svc);

        if let Err(e) = server.await {
            error!("HTTP server error: {}", e);
        }

        Ok(())
    }

    async fn handle_request_with_metrics(
        req: Request<Body>,
        backend_pool: Arc<rusty_balancer::backend::BackendPool>,
        metrics: Arc<LoadBalancerMetrics>,
        config: Config,
    ) -> Result<Response<Body>, Infallible> {
        use rusty_balancer::metrics::RequestMetricsTracker;
        use hyper::StatusCode;

        // Create request tracker
        let mut tracker = RequestMetricsTracker::new(
            req.method().to_string(),
            req.uri().to_string(),
        );

        // Extract request size
        if let Some(content_length) = req.headers().get("content-length") {
            if let Ok(size_str) = content_length.to_str() {
                if let Ok(size) = size_str.parse::<u64>() {
                    tracker.set_request_size(size);
                }
            }
        }

        // Update active connections
        metrics.update_active_connections(1); // Simplified - in reality you'd track this properly

        // Select backend
        let backend_addr = match backend_pool.get_next_backend() {
            Some(addr) => {
                tracker.set_backend(addr.to_string());
                addr
            }
            None => {
                metrics.record_error("no_backends", "proxy");

                let response = Response::builder()
                    .status(StatusCode::SERVICE_UNAVAILABLE)
                    .body(Body::from("No backends available"))
                    .unwrap();

                tracker.finish(&metrics, 503, None);
                metrics.update_active_connections(0);
                return Ok(response);
            }
        };

        // Simulate backend request
        let backend_start = std::time::Instant::now();

        // Simulate processing (in real implementation, forward to backend)
        tokio::time::sleep(Duration::from_millis(50)).await;

        let backend_duration = backend_start.elapsed();
        let status_code = 200u16;
        let response_size = 1024u64;

        // Record backend metrics
        metrics.record_backend_request(
            &backend_addr.to_string(),
            status_code,
            backend_duration,
        );

        // Create response
        let response = Response::builder()
            .status(StatusCode::OK)
            .header("content-type", "application/json")
            .header("x-backend-server", backend_addr.to_string())
            .body(Body::from(r#"{"status": "success", "message": "Hello from backend!"}"#))
            .unwrap();

        // Record request metrics
        tracker.finish(&metrics, status_code, Some(response_size));

        // Update active connections
        metrics.update_active_connections(0);

        Ok(response)
    }
}
```

## Testing Metrics System

Create `tests/metrics_tests.rs`:

```rust
use rusty_balancer::metrics::{LoadBalancerMetrics, CircuitBreakerState, SystemMetrics};
use std::time::Duration;
use std::sync::Arc;

#[tokio::test]
async fn test_metrics_initialization() {
    let metrics = LoadBalancerMetrics::new().expect("Failed to create metrics");

    // Test that metrics are properly initialized
    assert!(!metrics.registry().gather().is_empty());
}

#[tokio::test]
async fn test_request_metrics() {
    let metrics = Arc::new(LoadBalancerMetrics::new().unwrap());

    // Record some test requests
    metrics.record_request(
        "GET",
        200,
        "backend1",
        Duration::from_millis(100),
        Some(1024),
        Some(2048),
    );

    metrics.record_request(
        "POST",
        201,
        "backend2",
        Duration::from_millis(150),
        Some(2048),
        Some(1024),
    );

    // Verify metrics are recorded
    let metric_families = metrics.registry().gather();
    let requests_metric = metric_families
        .iter()
        .find(|mf| mf.get_name() == "rusty_lb_requests_total")
        .expect("requests_total metric not found");

    assert!(!requests_metric.get_metric().is_empty());
}

#[tokio::test]
async fn test_backend_metrics() {
    let metrics = Arc::new(LoadBalancerMetrics::new().unwrap());

    // Test backend health updates
    metrics.update_backend_health("backend1", true);
    metrics.update_backend_health("backend2", false);

    // Test backend request recording
    metrics.record_backend_request(
        "backend1",
        200,
        Duration::from_millis(50),
    );

    // Test connection pool updates
    metrics.update_connection_pool("backend1", 10, 5);

    // Verify metrics are recorded
    let metric_families = metrics.registry().gather();

    let health_metric = metric_families
        .iter()
        .find(|mf| mf.get_name() == "rusty_lb_backend_health_status")
        .expect("backend_health_status metric not found");

    assert!(!health_metric.get_metric().is_empty());
}

#[tokio::test]
async fn test_error_metrics() {
    let metrics = Arc::new(LoadBalancerMetrics::new().unwrap());

    // Record various errors
    metrics.record_error("connection_timeout", "backend");
    metrics.record_error("rate_limit", "proxy");
    metrics.record_error("auth_failure", "security");

    // Verify error metrics are recorded
    let metric_families = metrics.registry().gather();
    let errors_metric = metric_families
        .iter()
        .find(|mf| mf.get_name() == "rusty_lb_errors_total")
        .expect("errors_total metric not found");

    assert!(!errors_metric.get_metric().is_empty());
}

#[tokio::test]
async fn test_circuit_breaker_metrics() {
    let metrics = Arc::new(LoadBalancerMetrics::new().unwrap());

    // Test circuit breaker state updates
    metrics.update_circuit_breaker_state("backend1", CircuitBreakerState::Closed);
    metrics.update_circuit_breaker_state("backend2", CircuitBreakerState::Open);
    metrics.update_circuit_breaker_state("backend3", CircuitBreakerState::HalfOpen);

    // Verify circuit breaker metrics
    let metric_families = metrics.registry().gather();
    let cb_metric = metric_families
        .iter()
        .find(|mf| mf.get_name() == "rusty_lb_circuit_breaker_state")
        .expect("circuit_breaker_state metric not found");

    assert!(!cb_metric.get_metric().is_empty());
}

#[tokio::test]
async fn test_system_metrics() {
    let metrics = Arc::new(LoadBalancerMetrics::new().unwrap());

    let system_metrics = SystemMetrics {
        memory_usage_bytes: 256_000_000, // 256MB
        cpu_usage_percent: 15.5,
        file_descriptors_used: 150,
        goroutines_count: 75,
    };

    metrics.update_system_metrics(system_metrics);

    // Verify system metrics are recorded
    let metric_families = metrics.registry().gather();

    let memory_metric = metric_families
        .iter()
        .find(|mf| mf.get_name() == "rusty_lb_memory_usage_bytes")
        .expect("memory_usage_bytes metric not found");

    assert!(!memory_metric.get_metric().is_empty());
}

#[tokio::test]
async fn test_security_metrics() {
    let metrics = Arc::new(LoadBalancerMetrics::new().unwrap());

    // Record security events
    metrics.record_security_event("rate_limit_exceeded", "medium", "*************");
    metrics.record_security_event("suspicious_request", "high", "********");

    // Record TLS handshakes
    metrics.record_tls_handshake(true, "1.3");
    metrics.record_tls_handshake(false, "1.2");

    // Record auth attempts
    metrics.record_auth_attempt(true, "bearer_token");
    metrics.record_auth_attempt(false, "basic_auth");

    // Verify security metrics
    let metric_families = metrics.registry().gather();

    let security_metric = metric_families
        .iter()
        .find(|mf| mf.get_name() == "rusty_lb_security_events_total")
        .expect("security_events_total metric not found");

    assert!(!security_metric.get_metric().is_empty());
}

#[tokio::test]
async fn test_custom_metrics() {
    let metrics = Arc::new(LoadBalancerMetrics::new().unwrap());

    // Define a custom metric
    struct CustomBusinessMetric {
        value: f64,
    }

    impl rusty_balancer::metrics::CustomMetric for CustomBusinessMetric {
        fn name(&self) -> &str { "business_transactions_total" }
        fn help(&self) -> &str { "Total business transactions processed" }
        fn metric_type(&self) -> &str { "counter" }
        fn value(&self) -> f64 { self.value }
        fn labels(&self) -> Vec<(&str, &str)> { vec![("type", "payment")] }
    }

    let custom_metric = Box::new(CustomBusinessMetric { value: 42.0 });
    metrics.add_custom_metric("business_transactions".to_string(), custom_metric).await;

    // Verify custom metric is added
    let custom_metrics = metrics.custom_metrics.read().await;
    assert!(custom_metrics.contains_key("business_transactions"));
}

#[test]
fn test_request_metrics_tracker() {
    use rusty_balancer::metrics::RequestMetricsTracker;

    let mut tracker = RequestMetricsTracker::new(
        "GET".to_string(),
        "/api/test".to_string(),
    );

    tracker.set_backend("backend1".to_string());
    tracker.set_request_size(1024);

    // In a real test, you'd verify the tracker records metrics correctly
    // when finish() is called
}
```
