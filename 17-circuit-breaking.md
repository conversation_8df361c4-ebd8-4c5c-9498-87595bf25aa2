# Module 17: Circuit Breaking

## Learning Objectives
- Implement circuit breaker patterns for fault tolerance and system resilience
- Design state-based circuit breakers with configurable thresholds and timeouts
- Build adaptive circuit breakers that respond to different failure patterns
- Create circuit breaker monitoring and alerting for operational visibility
- Understand circuit breaking patterns and failure isolation strategies

## Why Circuit Breaking is Essential

Circuit breakers prevent cascading failures and improve system resilience:

1. **Fault Isolation**: Prevent failures in one service from affecting others
2. **Fast Failure**: Fail fast instead of waiting for timeouts during outages
3. **System Recovery**: Allow failing services time to recover without load
4. **Resource Protection**: Prevent resource exhaustion from retry storms
5. **User Experience**: Provide graceful degradation instead of hanging requests

### Circuit Breaker Architecture

```mermaid
graph TD
    Client[Client Request] --> CB[Circuit Breaker]
    
    CB --> State{Circuit State?}
    
    State -->|CLOSED| Allow[Allow Request]
    State -->|OPEN| Block[Block Request]
    State -->|HALF_OPEN| Test[Test Request]
    
    Allow --> Backend[Backend Service]
    Backend --> Success{Success?}
    
    Success -->|Yes| UpdateSuccess[Update Success Count]
    Success -->|No| UpdateFailure[Update Failure Count]
    
    UpdateSuccess --> CheckThreshold1{Failure Rate OK?}
    UpdateFailure --> CheckThreshold2{Failure Threshold Exceeded?}
    
    CheckThreshold1 -->|Yes| KeepClosed[Keep CLOSED]
    CheckThreshold1 -->|No| OpenCircuit[Open Circuit]
    
    CheckThreshold2 -->|Yes| OpenCircuit
    CheckThreshold2 -->|No| KeepClosed
    
    OpenCircuit --> SetTimer[Set Recovery Timer]
    SetTimer --> WaitTimer[Wait for Timer]
    WaitTimer --> HalfOpen[Set HALF_OPEN]
    
    Test --> Backend
    Block --> FastFail[Return Fast Fail]
    
    HalfOpen --> TestSuccess{Test Success?}
    TestSuccess -->|Yes| CloseCircuit[Close Circuit]
    TestSuccess -->|No| ReopenCircuit[Reopen Circuit]
    
    subgraph "Circuit States"
        Closed[CLOSED<br/>Normal Operation<br/>Monitor Failures]
        Open[OPEN<br/>Block All Requests<br/>Fast Fail]
        HalfOpenState[HALF_OPEN<br/>Test Recovery<br/>Limited Requests]
    end
    
    subgraph "Monitoring & Metrics"
        Metrics[Circuit Metrics]
        Alerts[Failure Alerts]
        Dashboard[Circuit Dashboard]
        Logs[Circuit Logs]
    end
    
    CB --> Metrics
    Metrics --> Alerts
    Metrics --> Dashboard
    CB --> Logs
    
    subgraph "Configuration"
        FailureThreshold[Failure Threshold]
        RecoveryTimeout[Recovery Timeout]
        TestRequests[Test Request Count]
        WindowSize[Sliding Window Size]
    end
    
    CB --> FailureThreshold
    CB --> RecoveryTimeout
    CB --> TestRequests
    CB --> WindowSize
```

## Circuit Breaker Implementation

### Design Decisions

**Why state-based circuit breaker?**
- **Clear Behavior**: Well-defined states with predictable transitions
- **Fault Tolerance**: Automatic recovery without manual intervention
- **Performance**: Fast failure detection and response
- **Monitoring**: Easy to understand and monitor circuit state

**Why adaptive thresholds?**
- **Flexibility**: Adjust to different failure patterns and service characteristics
- **Accuracy**: Reduce false positives and negatives in failure detection
- **Responsiveness**: React quickly to changing service health
- **Optimization**: Balance between fault tolerance and availability

Create `src/circuit/mod.rs`:

```rust
//! Circuit breaker implementation for fault tolerance and resilience
//!
//! This module provides comprehensive circuit breaking capabilities including:
//! - State-based circuit breakers (CLOSED, OPEN, HALF_OPEN)
//! - Configurable failure thresholds and recovery timeouts
//! - Sliding window failure rate calculation
//! - Adaptive thresholds based on service characteristics
//! - Circuit breaker monitoring and metrics

pub mod state;
pub mod metrics;
pub mod adaptive;

use crate::error::{LoadBalancerError, Result};
use crate::metrics::LoadBalancerMetrics;
use serde::{Serialize, Deserialize};
use std::collections::{HashMap, VecDeque};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{info, warn, debug, trace};

/// Circuit breaker manager for all backend services
pub struct CircuitBreakerManager {
    /// Circuit breakers per backend
    circuit_breakers: Arc<RwLock<HashMap<SocketAddr, Arc<CircuitBreaker>>>>,
    
    /// Global configuration
    config: CircuitBreakerConfig,
    
    /// Metrics collector
    metrics: Option<Arc<LoadBalancerMetrics>>,
    
    /// Adaptive threshold manager
    adaptive_manager: Option<Arc<AdaptiveThresholdManager>>,
}

/// Circuit breaker configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerConfig {
    /// Enable circuit breaking
    pub enabled: bool,
    
    /// Failure threshold (number of failures to trigger open state)
    pub failure_threshold: u32,
    
    /// Failure rate threshold (percentage of failures to trigger open state)
    pub failure_rate_threshold: f64,
    
    /// Minimum number of requests before calculating failure rate
    pub minimum_requests: u32,
    
    /// Recovery timeout (how long to wait before trying half-open)
    pub recovery_timeout: Duration,
    
    /// Half-open test request count
    pub half_open_max_requests: u32,
    
    /// Sliding window size for failure rate calculation
    pub window_size: Duration,
    
    /// Enable adaptive thresholds
    pub enable_adaptive: bool,
    
    /// Adaptive adjustment interval
    pub adaptive_interval: Duration,
    
    /// Success threshold for closing circuit in half-open state
    pub success_threshold: u32,
    
    /// Timeout for individual requests
    pub request_timeout: Duration,
    
    /// Enable slow request detection
    pub enable_slow_request_detection: bool,
    
    /// Slow request threshold
    pub slow_request_threshold: Duration,
    
    /// Slow request rate threshold
    pub slow_request_rate_threshold: f64,
}

/// Circuit breaker states
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum CircuitState {
    /// Circuit is closed, requests are allowed
    Closed,
    
    /// Circuit is open, requests are blocked
    Open,
    
    /// Circuit is half-open, testing recovery
    HalfOpen,
}

/// Circuit breaker for a specific backend service
pub struct CircuitBreaker {
    /// Backend address
    backend_address: SocketAddr,
    
    /// Current circuit state
    state: Arc<RwLock<CircuitState>>,
    
    /// Circuit breaker configuration
    config: CircuitBreakerConfig,
    
    /// Request history for sliding window
    request_history: Arc<RwLock<VecDeque<RequestResult>>>,
    
    /// State transition timestamps
    state_transitions: Arc<RwLock<Vec<StateTransition>>>,
    
    /// Half-open request counter
    half_open_requests: Arc<RwLock<u32>>,
    
    /// Half-open success counter
    half_open_successes: Arc<RwLock<u32>>,
    
    /// Last state change time
    last_state_change: Arc<RwLock<Instant>>,
    
    /// Circuit breaker statistics
    stats: Arc<RwLock<CircuitStats>>,
}

/// Request result for circuit breaker evaluation
#[derive(Debug, Clone)]
pub struct RequestResult {
    /// Request timestamp
    pub timestamp: Instant,
    
    /// Whether the request was successful
    pub success: bool,
    
    /// Request duration
    pub duration: Duration,
    
    /// Error type if request failed
    pub error_type: Option<String>,
    
    /// Response status code if available
    pub status_code: Option<u16>,
}

/// State transition record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateTransition {
    /// Timestamp of transition
    pub timestamp: Instant,
    
    /// Previous state
    pub from_state: CircuitState,
    
    /// New state
    pub to_state: CircuitState,
    
    /// Reason for transition
    pub reason: String,
    
    /// Metrics at time of transition
    pub metrics: TransitionMetrics,
}

/// Metrics captured during state transitions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransitionMetrics {
    /// Total requests in window
    pub total_requests: u32,
    
    /// Failed requests in window
    pub failed_requests: u32,
    
    /// Failure rate at transition
    pub failure_rate: f64,
    
    /// Average response time
    pub avg_response_time: Duration,
}

/// Circuit breaker statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitStats {
    /// Total requests processed
    pub total_requests: u64,
    
    /// Total successful requests
    pub successful_requests: u64,
    
    /// Total failed requests
    pub failed_requests: u64,
    
    /// Total requests blocked by circuit breaker
    pub blocked_requests: u64,
    
    /// Number of times circuit opened
    pub circuit_opened_count: u32,
    
    /// Number of times circuit closed
    pub circuit_closed_count: u32,
    
    /// Total time circuit was open
    pub total_open_time: Duration,
    
    /// Current failure rate
    pub current_failure_rate: f64,
    
    /// Average response time
    pub avg_response_time: Duration,
    
    /// Last updated timestamp
    pub last_updated: Instant,
}

impl CircuitBreakerManager {
    /// Create a new circuit breaker manager
    pub fn new(config: CircuitBreakerConfig) -> Self {
        let adaptive_manager = if config.enable_adaptive {
            Some(Arc::new(AdaptiveThresholdManager::new(config.clone())))
        } else {
            None
        };
        
        info!("🔌 Circuit breaker manager initialized (enabled: {})", config.enabled);
        
        Self {
            circuit_breakers: Arc::new(RwLock::new(HashMap::new())),
            config,
            metrics: None,
            adaptive_manager,
        }
    }
    
    /// Set metrics collector
    pub fn with_metrics(mut self, metrics: Arc<LoadBalancerMetrics>) -> Self {
        self.metrics = Some(metrics);
        self
    }
    
    /// Start circuit breaker management services
    pub async fn start(&self) -> Result<()> {
        if !self.config.enabled {
            info!("Circuit breaking is disabled");
            return Ok(());
        }
        
        // Start adaptive threshold management if enabled
        if let Some(ref adaptive_manager) = self.adaptive_manager {
            adaptive_manager.start(self.circuit_breakers.clone()).await?;
        }
        
        // Start metrics collection
        self.start_metrics_collection().await;
        
        // Start cleanup task
        self.start_cleanup_task().await;
        
        info!("✅ Circuit breaker management services started");
        Ok(())
    }
    
    /// Add a backend to circuit breaker management
    pub async fn add_backend(&self, backend_address: SocketAddr) -> Result<()> {
        let mut circuit_breakers = self.circuit_breakers.write().await;
        
        if circuit_breakers.contains_key(&backend_address) {
            return Ok(()); // Circuit breaker already exists
        }
        
        let circuit_breaker = Arc::new(CircuitBreaker::new(backend_address, self.config.clone()));
        circuit_breakers.insert(backend_address, circuit_breaker);
        
        info!("🔌 Added circuit breaker for backend: {}", backend_address);
        Ok(())
    }
    
    /// Remove a backend from circuit breaker management
    pub async fn remove_backend(&self, backend_address: SocketAddr) -> Result<()> {
        let mut circuit_breakers = self.circuit_breakers.write().await;
        
        if circuit_breakers.remove(&backend_address).is_some() {
            info!("🗑️ Removed circuit breaker for backend: {}", backend_address);
        }
        
        Ok(())
    }
    
    /// Check if a request should be allowed through the circuit breaker
    pub async fn should_allow_request(&self, backend_address: SocketAddr) -> Result<bool> {
        if !self.config.enabled {
            return Ok(true);
        }
        
        let circuit_breakers = self.circuit_breakers.read().await;
        
        if let Some(circuit_breaker) = circuit_breakers.get(&backend_address) {
            circuit_breaker.should_allow_request().await
        } else {
            // No circuit breaker found, allow request
            Ok(true)
        }
    }
    
    /// Record the result of a request
    pub async fn record_request_result(
        &self,
        backend_address: SocketAddr,
        result: RequestResult,
    ) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }
        
        let circuit_breakers = self.circuit_breakers.read().await;
        
        if let Some(circuit_breaker) = circuit_breakers.get(&backend_address) {
            circuit_breaker.record_request_result(result).await?;
            
            // Update metrics
            if let Some(ref metrics) = self.metrics {
                let state = circuit_breaker.get_state().await;
                metrics.update_circuit_breaker_state(
                    &backend_address.to_string(),
                    match state {
                        CircuitState::Closed => crate::metrics::CircuitBreakerState::Closed,
                        CircuitState::Open => crate::metrics::CircuitBreakerState::Open,
                        CircuitState::HalfOpen => crate::metrics::CircuitBreakerState::HalfOpen,
                    },
                );
            }
        }
        
        Ok(())
    }
    
    /// Get circuit breaker state for a backend
    pub async fn get_circuit_state(&self, backend_address: SocketAddr) -> Option<CircuitState> {
        let circuit_breakers = self.circuit_breakers.read().await;
        
        if let Some(circuit_breaker) = circuit_breakers.get(&backend_address) {
            Some(circuit_breaker.get_state().await)
        } else {
            None
        }
    }
    
    /// Get circuit breaker statistics for all backends
    pub async fn get_circuit_stats(&self) -> HashMap<SocketAddr, CircuitStats> {
        let circuit_breakers = self.circuit_breakers.read().await;
        let mut stats = HashMap::new();
        
        for (addr, circuit_breaker) in circuit_breakers.iter() {
            stats.insert(*addr, circuit_breaker.get_stats().await);
        }
        
        stats
    }
    
    /// Manually open a circuit breaker
    pub async fn open_circuit(&self, backend_address: SocketAddr, reason: String) -> Result<()> {
        let circuit_breakers = self.circuit_breakers.read().await;
        
        if let Some(circuit_breaker) = circuit_breakers.get(&backend_address) {
            circuit_breaker.force_open(reason).await?;
            info!("🔌 Manually opened circuit for backend: {}", backend_address);
        }
        
        Ok(())
    }
    
    /// Manually close a circuit breaker
    pub async fn close_circuit(&self, backend_address: SocketAddr, reason: String) -> Result<()> {
        let circuit_breakers = self.circuit_breakers.read().await;
        
        if let Some(circuit_breaker) = circuit_breakers.get(&backend_address) {
            circuit_breaker.force_close(reason).await?;
            info!("🔌 Manually closed circuit for backend: {}", backend_address);
        }
        
        Ok(())
    }
    
    /// Start metrics collection task
    async fn start_metrics_collection(&self) {
        let circuit_breakers = self.circuit_breakers.clone();
        let metrics = self.metrics.clone();
        
        if let Some(metrics) = metrics {
            tokio::spawn(async move {
                let mut interval = tokio::time::interval(Duration::from_secs(10));
                
                loop {
                    interval.tick().await;
                    
                    let circuit_breakers_guard = circuit_breakers.read().await;
                    for (addr, circuit_breaker) in circuit_breakers_guard.iter() {
                        let state = circuit_breaker.get_state().await;
                        let stats = circuit_breaker.get_stats().await;
                        
                        // Update circuit breaker metrics
                        metrics.update_circuit_breaker_state(
                            &addr.to_string(),
                            match state {
                                CircuitState::Closed => crate::metrics::CircuitBreakerState::Closed,
                                CircuitState::Open => crate::metrics::CircuitBreakerState::Open,
                                CircuitState::HalfOpen => crate::metrics::CircuitBreakerState::HalfOpen,
                            },
                        );
                        
                        // Record circuit breaker statistics
                        trace!("Circuit breaker {} - State: {:?}, Failure Rate: {:.2}%", 
                               addr, state, stats.current_failure_rate * 100.0);
                    }
                }
            });
        }
    }
    
    /// Start cleanup task for old request history
    async fn start_cleanup_task(&self) {
        let circuit_breakers = self.circuit_breakers.clone();
        let window_size = self.config.window_size;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));
            
            loop {
                interval.tick().await;
                
                let circuit_breakers_guard = circuit_breakers.read().await;
                for circuit_breaker in circuit_breakers_guard.values() {
                    if let Err(e) = circuit_breaker.cleanup_old_requests(window_size).await {
                        debug!("Failed to cleanup old requests: {}", e);
                    }
                }
            }
        });
    }
}

impl CircuitBreaker {
    /// Create a new circuit breaker
    pub fn new(backend_address: SocketAddr, config: CircuitBreakerConfig) -> Self {
        Self {
            backend_address,
            state: Arc::new(RwLock::new(CircuitState::Closed)),
            config,
            request_history: Arc::new(RwLock::new(VecDeque::new())),
            state_transitions: Arc::new(RwLock::new(Vec::new())),
            half_open_requests: Arc::new(RwLock::new(0)),
            half_open_successes: Arc::new(RwLock::new(0)),
            last_state_change: Arc::new(RwLock::new(Instant::now())),
            stats: Arc::new(RwLock::new(CircuitStats::default())),
        }
    }
    
    /// Check if a request should be allowed
    pub async fn should_allow_request(&self) -> Result<bool> {
        let state = *self.state.read().await;
        
        match state {
            CircuitState::Closed => Ok(true),
            CircuitState::Open => {
                // Check if recovery timeout has elapsed
                let last_change = *self.last_state_change.read().await;
                if last_change.elapsed() >= self.config.recovery_timeout {
                    // Transition to half-open
                    self.transition_to_half_open("Recovery timeout elapsed".to_string()).await?;
                    Ok(true)
                } else {
                    // Still in open state, block request
                    {
                        let mut stats = self.stats.write().await;
                        stats.blocked_requests += 1;
                    }
                    Ok(false)
                }
            }
            CircuitState::HalfOpen => {
                // Allow limited requests in half-open state
                let current_requests = *self.half_open_requests.read().await;
                if current_requests < self.config.half_open_max_requests {
                    {
                        let mut half_open_requests = self.half_open_requests.write().await;
                        *half_open_requests += 1;
                    }
                    Ok(true)
                } else {
                    Ok(false)
                }
            }
        }
    }
    
    /// Record the result of a request
    pub async fn record_request_result(&self, result: RequestResult) -> Result<()> {
        // Add to request history
        {
            let mut history = self.request_history.write().await;
            history.push_back(result.clone());
        }
        
        // Update statistics
        {
            let mut stats = self.stats.write().await;
            stats.total_requests += 1;
            
            if result.success {
                stats.successful_requests += 1;
            } else {
                stats.failed_requests += 1;
            }
            
            // Update average response time
            let total_time = stats.avg_response_time.as_millis() as u64 * (stats.total_requests - 1) + result.duration.as_millis() as u64;
            stats.avg_response_time = Duration::from_millis(total_time / stats.total_requests);
            stats.last_updated = Instant::now();
        }
        
        // Check state transitions based on current state
        let current_state = *self.state.read().await;
        
        match current_state {
            CircuitState::Closed => {
                self.check_failure_threshold().await?;
            }
            CircuitState::HalfOpen => {
                if result.success {
                    let mut successes = self.half_open_successes.write().await;
                    *successes += 1;
                    
                    // Check if we have enough successes to close the circuit
                    if *successes >= self.config.success_threshold {
                        self.transition_to_closed("Sufficient successes in half-open state".to_string()).await?;
                    }
                } else {
                    // Failure in half-open state, reopen circuit
                    self.transition_to_open("Failure in half-open state".to_string()).await?;
                }
            }
            CircuitState::Open => {
                // Requests shouldn't reach here in open state, but handle gracefully
                debug!("Received request result in open state for {}", self.backend_address);
            }
        }
        
        Ok(())
    }
    
    /// Check if failure threshold is exceeded
    async fn check_failure_threshold(&self) -> Result<()> {
        let history = self.request_history.read().await;
        let now = Instant::now();
        let window_start = now - self.config.window_size;
        
        // Count requests in the sliding window
        let recent_requests: Vec<&RequestResult> = history
            .iter()
            .filter(|r| r.timestamp >= window_start)
            .collect();
        
        if recent_requests.len() < self.config.minimum_requests as usize {
            return Ok(()); // Not enough requests to make a decision
        }
        
        let failed_requests = recent_requests.iter().filter(|r| !r.success).count();
        let failure_rate = failed_requests as f64 / recent_requests.len() as f64;
        
        // Update current failure rate in stats
        {
            let mut stats = self.stats.write().await;
            stats.current_failure_rate = failure_rate;
        }
        
        // Check thresholds
        let should_open = failed_requests >= self.config.failure_threshold as usize ||
                         failure_rate >= self.config.failure_rate_threshold;
        
        if should_open {
            let reason = format!(
                "Failure threshold exceeded: {} failures ({:.2}% rate) in {} requests",
                failed_requests, failure_rate * 100.0, recent_requests.len()
            );
            self.transition_to_open(reason).await?;
        }
        
        Ok(())
    }
    
    /// Transition to open state
    async fn transition_to_open(&self, reason: String) -> Result<()> {
        let old_state = *self.state.read().await;
        
        if old_state != CircuitState::Open {
            {
                let mut state = self.state.write().await;
                *state = CircuitState::Open;
            }
            
            {
                let mut last_change = self.last_state_change.write().await;
                *last_change = Instant::now();
            }
            
            // Record state transition
            self.record_state_transition(old_state, CircuitState::Open, reason.clone()).await;
            
            // Update statistics
            {
                let mut stats = self.stats.write().await;
                stats.circuit_opened_count += 1;
            }
            
            warn!("🔌 Circuit breaker OPENED for {}: {}", self.backend_address, reason);
        }
        
        Ok(())
    }
    
    /// Transition to half-open state
    async fn transition_to_half_open(&self, reason: String) -> Result<()> {
        let old_state = *self.state.read().await;
        
        {
            let mut state = self.state.write().await;
            *state = CircuitState::HalfOpen;
        }
        
        {
            let mut last_change = self.last_state_change.write().await;
            *last_change = Instant::now();
        }
        
        // Reset half-open counters
        {
            let mut half_open_requests = self.half_open_requests.write().await;
            *half_open_requests = 0;
        }
        {
            let mut half_open_successes = self.half_open_successes.write().await;
            *half_open_successes = 0;
        }
        
        // Record state transition
        self.record_state_transition(old_state, CircuitState::HalfOpen, reason.clone()).await;
        
        info!("🔌 Circuit breaker HALF-OPEN for {}: {}", self.backend_address, reason);
        
        Ok(())
    }
    
    /// Transition to closed state
    async fn transition_to_closed(&self, reason: String) -> Result<()> {
        let old_state = *self.state.read().await;
        
        {
            let mut state = self.state.write().await;
            *state = CircuitState::Closed;
        }
        
        {
            let mut last_change = self.last_state_change.write().await;
            *last_change = Instant::now();
        }
        
        // Record state transition
        self.record_state_transition(old_state, CircuitState::Closed, reason.clone()).await;
        
        // Update statistics
        {
            let mut stats = self.stats.write().await;
            stats.circuit_closed_count += 1;
            
            // Calculate total open time if transitioning from open
            if old_state == CircuitState::Open {
                let open_duration = self.last_state_change.read().await.elapsed();
                stats.total_open_time += open_duration;
            }
        }
        
        info!("🔌 Circuit breaker CLOSED for {}: {}", self.backend_address, reason);
        
        Ok(())
    }
    
    /// Record a state transition
    async fn record_state_transition(&self, from_state: CircuitState, to_state: CircuitState, reason: String) {
        let history = self.request_history.read().await;
        let now = Instant::now();
        let window_start = now - self.config.window_size;
        
        let recent_requests: Vec<&RequestResult> = history
            .iter()
            .filter(|r| r.timestamp >= window_start)
            .collect();
        
        let total_requests = recent_requests.len() as u32;
        let failed_requests = recent_requests.iter().filter(|r| !r.success).count() as u32;
        let failure_rate = if total_requests > 0 {
            failed_requests as f64 / total_requests as f64
        } else {
            0.0
        };
        
        let avg_response_time = if !recent_requests.is_empty() {
            let total_time: Duration = recent_requests.iter().map(|r| r.duration).sum();
            total_time / recent_requests.len() as u32
        } else {
            Duration::from_millis(0)
        };
        
        let transition = StateTransition {
            timestamp: now,
            from_state,
            to_state,
            reason,
            metrics: TransitionMetrics {
                total_requests,
                failed_requests,
                failure_rate,
                avg_response_time,
            },
        };
        
        let mut transitions = self.state_transitions.write().await;
        transitions.push(transition);
        
        // Keep only recent transitions
        if transitions.len() > 100 {
            transitions.remove(0);
        }
    }
    
    /// Force open the circuit breaker
    pub async fn force_open(&self, reason: String) -> Result<()> {
        self.transition_to_open(format!("Manual: {}", reason)).await
    }
    
    /// Force close the circuit breaker
    pub async fn force_close(&self, reason: String) -> Result<()> {
        self.transition_to_closed(format!("Manual: {}", reason)).await
    }
    
    /// Get current circuit state
    pub async fn get_state(&self) -> CircuitState {
        *self.state.read().await
    }
    
    /// Get circuit breaker statistics
    pub async fn get_stats(&self) -> CircuitStats {
        self.stats.read().await.clone()
    }
    
    /// Cleanup old requests from history
    pub async fn cleanup_old_requests(&self, window_size: Duration) -> Result<()> {
        let mut history = self.request_history.write().await;
        let cutoff_time = Instant::now() - window_size * 2; // Keep extra history for safety
        
        while let Some(front) = history.front() {
            if front.timestamp < cutoff_time {
                history.pop_front();
            } else {
                break;
            }
        }
        
        Ok(())
    }
}

/// Adaptive threshold manager
pub struct AdaptiveThresholdManager {
    config: CircuitBreakerConfig,
}

impl AdaptiveThresholdManager {
    pub fn new(config: CircuitBreakerConfig) -> Self {
        Self { config }
    }
    
    pub async fn start(&self, _circuit_breakers: Arc<RwLock<HashMap<SocketAddr, Arc<CircuitBreaker>>>>) -> Result<()> {
        // Adaptive threshold adjustment would be implemented here
        // For now, we'll use static thresholds
        info!("🧠 Adaptive threshold manager started");
        Ok(())
    }
}

impl Default for CircuitStats {
    fn default() -> Self {
        Self {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            blocked_requests: 0,
            circuit_opened_count: 0,
            circuit_closed_count: 0,
            total_open_time: Duration::from_secs(0),
            current_failure_rate: 0.0,
            avg_response_time: Duration::from_millis(0),
            last_updated: Instant::now(),
        }
    }
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            failure_threshold: 5,
            failure_rate_threshold: 0.5, // 50%
            minimum_requests: 10,
            recovery_timeout: Duration::from_secs(60),
            half_open_max_requests: 3,
            window_size: Duration::from_secs(60),
            enable_adaptive: false,
            adaptive_interval: Duration::from_secs(300),
            success_threshold: 2,
            request_timeout: Duration::from_secs(30),
            enable_slow_request_detection: false,
            slow_request_threshold: Duration::from_secs(5),
            slow_request_rate_threshold: 0.3, // 30%
        }
    }
}
```

## Integration Example

Create `examples/circuit_breaking_example.rs`:

```rust
//! Example demonstrating circuit breaker patterns and fault tolerance

use rusty_balancer::circuit::{
    CircuitBreakerManager, CircuitBreakerConfig, RequestResult, CircuitState
};
use std::net::SocketAddr;
use std::time::{Duration, Instant};
use tracing::{info, warn, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("🔌 Starting Circuit Breaking Example");

    // Create circuit breaker configuration
    let config = CircuitBreakerConfig {
        enabled: true,
        failure_threshold: 3,
        failure_rate_threshold: 0.6, // 60%
        minimum_requests: 5,
        recovery_timeout: Duration::from_secs(10),
        half_open_max_requests: 2,
        window_size: Duration::from_secs(30),
        success_threshold: 2,
        ..Default::default()
    };

    // Create circuit breaker manager
    let cb_manager = CircuitBreakerManager::new(config);
    cb_manager.start().await?;

    // Add backend servers
    let backends = vec![
        "127.0.0.1:8081".parse::<SocketAddr>()?,
        "127.0.0.1:8082".parse::<SocketAddr>()?,
        "127.0.0.1:8083".parse::<SocketAddr>()?,
    ];

    for backend in &backends {
        cb_manager.add_backend(*backend).await?;
        info!("Added circuit breaker for backend: {}", backend);
    }

    // Scenario 1: Normal operation
    info!("📊 Scenario 1: Normal operation");
    let backend = backends[0];

    for i in 0..8 {
        if cb_manager.should_allow_request(backend).await? {
            // Simulate successful requests
            let result = RequestResult {
                timestamp: Instant::now(),
                success: true,
                duration: Duration::from_millis(50 + i * 10),
                error_type: None,
                status_code: Some(200),
            };

            cb_manager.record_request_result(backend, result).await?;
            info!("✅ Request {} succeeded", i + 1);
        } else {
            warn!("🚫 Request {} blocked by circuit breaker", i + 1);
        }

        tokio::time::sleep(Duration::from_millis(100)).await;
    }

    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    info!("Circuit state after normal operation: {:?}", state);

    // Scenario 2: Failure scenario - trigger circuit breaker
    info!("\n📊 Scenario 2: Failure scenario");

    for i in 0..10 {
        if cb_manager.should_allow_request(backend).await? {
            // Simulate failures to trigger circuit breaker
            let success = i < 2; // First 2 succeed, rest fail
            let result = RequestResult {
                timestamp: Instant::now(),
                success,
                duration: if success { Duration::from_millis(100) } else { Duration::from_millis(5000) },
                error_type: if success { None } else { Some("connection_timeout".to_string()) },
                status_code: if success { Some(200) } else { Some(500) },
            };

            cb_manager.record_request_result(backend, result).await?;

            if success {
                info!("✅ Request {} succeeded", i + 1);
            } else {
                error!("❌ Request {} failed", i + 1);
            }
        } else {
            warn!("🚫 Request {} blocked by circuit breaker", i + 1);
        }

        let state = cb_manager.get_circuit_state(backend).await.unwrap();
        info!("  Current circuit state: {:?}", state);

        tokio::time::sleep(Duration::from_millis(200)).await;
    }

    // Show circuit breaker statistics
    let stats = cb_manager.get_circuit_stats().await;
    if let Some(backend_stats) = stats.get(&backend) {
        info!("\n📈 Circuit Breaker Statistics for {}:", backend);
        info!("  Total requests: {}", backend_stats.total_requests);
        info!("  Successful requests: {}", backend_stats.successful_requests);
        info!("  Failed requests: {}", backend_stats.failed_requests);
        info!("  Blocked requests: {}", backend_stats.blocked_requests);
        info!("  Circuit opened count: {}", backend_stats.circuit_opened_count);
        info!("  Current failure rate: {:.2}%", backend_stats.current_failure_rate * 100.0);
        info!("  Average response time: {:?}", backend_stats.avg_response_time);
    }

    // Scenario 3: Recovery scenario
    info!("\n📊 Scenario 3: Recovery scenario");
    info!("Waiting for recovery timeout...");

    // Wait for recovery timeout
    tokio::time::sleep(Duration::from_secs(12)).await;

    // Try requests during half-open state
    for i in 0..5 {
        if cb_manager.should_allow_request(backend).await? {
            // Simulate recovery with successful requests
            let result = RequestResult {
                timestamp: Instant::now(),
                success: true,
                duration: Duration::from_millis(80),
                error_type: None,
                status_code: Some(200),
            };

            cb_manager.record_request_result(backend, result).await?;
            info!("✅ Recovery request {} succeeded", i + 1);
        } else {
            warn!("🚫 Recovery request {} blocked", i + 1);
        }

        let state = cb_manager.get_circuit_state(backend).await.unwrap();
        info!("  Circuit state during recovery: {:?}", state);

        tokio::time::sleep(Duration::from_millis(500)).await;
    }

    // Final state check
    let final_state = cb_manager.get_circuit_state(backend).await.unwrap();
    info!("Final circuit state: {:?}", final_state);

    // Scenario 4: Multiple backends with different behaviors
    info!("\n📊 Scenario 4: Multiple backends");

    let healthy_backend = backends[1];
    let unhealthy_backend = backends[2];

    // Simulate different backend behaviors
    for i in 0..15 {
        // Healthy backend - mostly successful
        if cb_manager.should_allow_request(healthy_backend).await? {
            let success = i % 10 != 0; // 90% success rate
            let result = RequestResult {
                timestamp: Instant::now(),
                success,
                duration: Duration::from_millis(60),
                error_type: if success { None } else { Some("occasional_error".to_string()) },
                status_code: if success { Some(200) } else { Some(503) },
            };

            cb_manager.record_request_result(healthy_backend, result).await?;
        }

        // Unhealthy backend - mostly failures
        if cb_manager.should_allow_request(unhealthy_backend).await? {
            let success = i % 5 == 0; // 20% success rate
            let result = RequestResult {
                timestamp: Instant::now(),
                success,
                duration: if success { Duration::from_millis(100) } else { Duration::from_millis(3000) },
                error_type: if success { None } else { Some("frequent_timeout".to_string()) },
                status_code: if success { Some(200) } else { Some(504) },
            };

            cb_manager.record_request_result(unhealthy_backend, result).await?;
        }

        tokio::time::sleep(Duration::from_millis(100)).await;
    }

    // Show final statistics for all backends
    let final_stats = cb_manager.get_circuit_stats().await;
    info!("\n📊 Final Statistics for All Backends:");

    for (addr, stats) in &final_stats {
        let state = cb_manager.get_circuit_state(*addr).await.unwrap_or(CircuitState::Closed);

        info!("Backend {}: State: {:?}", addr, state);
        info!("  Requests: {} total, {} successful, {} failed, {} blocked",
              stats.total_requests, stats.successful_requests,
              stats.failed_requests, stats.blocked_requests);
        info!("  Failure rate: {:.1}%, Avg response: {:?}",
              stats.current_failure_rate * 100.0, stats.avg_response_time);
        info!("  Circuit opened {} times, total open time: {:?}",
              stats.circuit_opened_count, stats.total_open_time);
    }

    // Test manual circuit control
    info!("\n🔧 Testing manual circuit control");

    // Manually open a circuit
    cb_manager.open_circuit(healthy_backend, "Manual test".to_string()).await?;
    let state = cb_manager.get_circuit_state(healthy_backend).await.unwrap();
    info!("Manually opened circuit for {}: {:?}", healthy_backend, state);

    // Try a request (should be blocked)
    if cb_manager.should_allow_request(healthy_backend).await? {
        info!("❌ Request was allowed when circuit should be open");
    } else {
        info!("✅ Request correctly blocked by manually opened circuit");
    }

    // Manually close the circuit
    cb_manager.close_circuit(healthy_backend, "Manual test complete".to_string()).await?;
    let state = cb_manager.get_circuit_state(healthy_backend).await.unwrap();
    info!("Manually closed circuit for {}: {:?}", healthy_backend, state);

    info!("✅ Circuit breaking example completed");

    Ok(())
}
```

## Testing Circuit Breaking

Create `tests/circuit_breaking_tests.rs`:

```rust
use rusty_balancer::circuit::{
    CircuitBreakerManager, CircuitBreakerConfig, RequestResult, CircuitState
};
use std::net::SocketAddr;
use std::time::{Duration, Instant};

#[tokio::test]
async fn test_circuit_breaker_creation() {
    let config = CircuitBreakerConfig::default();
    let cb_manager = CircuitBreakerManager::new(config);

    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Closed);
}

#[tokio::test]
async fn test_circuit_breaker_failure_threshold() {
    let config = CircuitBreakerConfig {
        failure_threshold: 3,
        minimum_requests: 3,
        failure_rate_threshold: 1.0, // 100% to test count threshold
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Send 3 failed requests
    for _ in 0..3 {
        assert!(cb_manager.should_allow_request(backend).await.unwrap());

        let result = RequestResult {
            timestamp: Instant::now(),
            success: false,
            duration: Duration::from_millis(100),
            error_type: Some("test_error".to_string()),
            status_code: Some(500),
        };

        cb_manager.record_request_result(backend, result).await.unwrap();
    }

    // Circuit should now be open
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Open);

    // Next request should be blocked
    assert!(!cb_manager.should_allow_request(backend).await.unwrap());
}

#[tokio::test]
async fn test_circuit_breaker_failure_rate() {
    let config = CircuitBreakerConfig {
        failure_threshold: 10, // High count threshold
        failure_rate_threshold: 0.6, // 60% rate threshold
        minimum_requests: 5,
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Send 5 requests: 2 success, 3 failures (60% failure rate)
    for i in 0..5 {
        assert!(cb_manager.should_allow_request(backend).await.unwrap());

        let success = i < 2;
        let result = RequestResult {
            timestamp: Instant::now(),
            success,
            duration: Duration::from_millis(100),
            error_type: if success { None } else { Some("test_error".to_string()) },
            status_code: if success { Some(200) } else { Some(500) },
        };

        cb_manager.record_request_result(backend, result).await.unwrap();
    }

    // Circuit should now be open due to failure rate
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Open);
}

#[tokio::test]
async fn test_circuit_breaker_recovery() {
    let config = CircuitBreakerConfig {
        failure_threshold: 2,
        minimum_requests: 2,
        recovery_timeout: Duration::from_millis(100),
        success_threshold: 1,
        half_open_max_requests: 1,
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Trigger circuit breaker with failures
    for _ in 0..2 {
        assert!(cb_manager.should_allow_request(backend).await.unwrap());

        let result = RequestResult {
            timestamp: Instant::now(),
            success: false,
            duration: Duration::from_millis(100),
            error_type: Some("test_error".to_string()),
            status_code: Some(500),
        };

        cb_manager.record_request_result(backend, result).await.unwrap();
    }

    // Circuit should be open
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Open);

    // Wait for recovery timeout
    tokio::time::sleep(Duration::from_millis(150)).await;

    // Should allow request (transitions to half-open)
    assert!(cb_manager.should_allow_request(backend).await.unwrap());

    // Send successful request
    let result = RequestResult {
        timestamp: Instant::now(),
        success: true,
        duration: Duration::from_millis(50),
        error_type: None,
        status_code: Some(200),
    };

    cb_manager.record_request_result(backend, result).await.unwrap();

    // Circuit should now be closed
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Closed);
}

#[tokio::test]
async fn test_manual_circuit_control() {
    let config = CircuitBreakerConfig::default();
    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Initially closed
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Closed);

    // Manually open
    cb_manager.open_circuit(backend, "Test".to_string()).await.unwrap();
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Open);

    // Should block requests
    assert!(!cb_manager.should_allow_request(backend).await.unwrap());

    // Manually close
    cb_manager.close_circuit(backend, "Test complete".to_string()).await.unwrap();
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Closed);

    // Should allow requests
    assert!(cb_manager.should_allow_request(backend).await.unwrap());
}

#[tokio::test]
async fn test_circuit_breaker_statistics() {
    let config = CircuitBreakerConfig::default();
    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Send some requests
    for i in 0..10 {
        if cb_manager.should_allow_request(backend).await.unwrap() {
            let success = i % 3 != 0; // 2/3 success rate
            let result = RequestResult {
                timestamp: Instant::now(),
                success,
                duration: Duration::from_millis(100 + i * 10),
                error_type: if success { None } else { Some("test_error".to_string()) },
                status_code: if success { Some(200) } else { Some(500) },
            };

            cb_manager.record_request_result(backend, result).await.unwrap();
        }
    }

    // Check statistics
    let stats = cb_manager.get_circuit_stats().await;
    let backend_stats = stats.get(&backend).unwrap();

    assert!(backend_stats.total_requests > 0);
    assert!(backend_stats.successful_requests > 0);
    assert!(backend_stats.failed_requests > 0);
    assert!(backend_stats.current_failure_rate > 0.0);
    assert!(backend_stats.avg_response_time > Duration::from_millis(0));
}
```

## Key Design Decisions Explained

### 1. State-Based Circuit Breaker
**Decision**: Implement three-state circuit breaker (CLOSED, OPEN, HALF_OPEN)

**Rationale**:
- **Clear Behavior**: Well-defined states with predictable transitions
- **Fault Tolerance**: Automatic recovery without manual intervention
- **Performance**: Fast failure detection and response
- **Monitoring**: Easy to understand and monitor circuit state

### 2. Sliding Window Failure Detection
**Decision**: Use sliding window for failure rate calculation instead of fixed windows

**Rationale**:
- **Accuracy**: More accurate representation of recent failure patterns
- **Responsiveness**: Quick detection of failure spikes and recovery
- **Smoothing**: Reduces false positives from temporary glitches
- **Flexibility**: Configurable window size for different service characteristics

### 3. Configurable Thresholds
**Decision**: Support both failure count and failure rate thresholds

**Rationale**:
- **Flexibility**: Different services have different failure patterns
- **Accuracy**: Combine absolute and relative thresholds for better detection
- **Tuning**: Allow fine-tuning based on service requirements
- **Safety**: Prevent premature circuit opening with minimum request requirements

## Performance Considerations

1. **State Checking**: O(1) circuit state checks for minimal request overhead
2. **History Management**: Efficient sliding window with automatic cleanup
3. **Memory Usage**: Bounded request history and state transition storage
4. **Concurrency**: Thread-safe state management with minimal locking

## Security Considerations

1. **Resource Protection**: Prevent resource exhaustion from failing services
2. **Cascading Failures**: Isolate failures to prevent system-wide outages
3. **Monitoring**: Comprehensive logging and metrics for security analysis
4. **Manual Control**: Secure interfaces for manual circuit breaker control

## Comprehensive Testing

Create `tests/circuit_breaking_tests.rs`:

```rust
use rusty_balancer::circuit::{
    CircuitBreakerManager, CircuitBreakerConfig, RequestResult, CircuitState
};
use std::net::SocketAddr;
use std::time::{Duration, Instant};

#[tokio::test]
async fn test_circuit_breaker_creation() {
    let config = CircuitBreakerConfig::default();
    let cb_manager = CircuitBreakerManager::new(config);

    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Closed);
}

#[tokio::test]
async fn test_circuit_breaker_failure_threshold() {
    let config = CircuitBreakerConfig {
        failure_threshold: 3,
        minimum_requests: 3,
        failure_rate_threshold: 1.0, // 100% to test count threshold
        recovery_timeout: Duration::from_secs(60),
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Send 3 failed requests
    for i in 0..3 {
        assert!(cb_manager.should_allow_request(backend).await.unwrap());

        let result = RequestResult {
            timestamp: Instant::now(),
            success: false,
            duration: Duration::from_millis(100),
            error_type: Some("test_error".to_string()),
            status_code: Some(500),
        };

        cb_manager.record_request_result(backend, result).await.unwrap();

        // Check state after each failure
        let state = cb_manager.get_circuit_state(backend).await.unwrap();
        if i < 2 {
            assert_eq!(state, CircuitState::Closed, "Circuit should remain closed until threshold");
        }
    }

    // Circuit should now be open
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Open);

    // Next request should be blocked
    assert!(!cb_manager.should_allow_request(backend).await.unwrap());
}

#[tokio::test]
async fn test_circuit_breaker_failure_rate() {
    let config = CircuitBreakerConfig {
        failure_threshold: 10, // High count threshold
        failure_rate_threshold: 0.6, // 60% rate threshold
        minimum_requests: 5,
        recovery_timeout: Duration::from_secs(60),
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Send 5 requests: 2 success, 3 failures (60% failure rate)
    for i in 0..5 {
        assert!(cb_manager.should_allow_request(backend).await.unwrap());

        let success = i < 2;
        let result = RequestResult {
            timestamp: Instant::now(),
            success,
            duration: Duration::from_millis(100),
            error_type: if success { None } else { Some("test_error".to_string()) },
            status_code: if success { Some(200) } else { Some(500) },
        };

        cb_manager.record_request_result(backend, result).await.unwrap();
    }

    // Circuit should now be open due to failure rate
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Open);
}

#[tokio::test]
async fn test_circuit_breaker_recovery() {
    let config = CircuitBreakerConfig {
        failure_threshold: 2,
        minimum_requests: 2,
        recovery_timeout: Duration::from_millis(100),
        success_threshold: 1,
        half_open_max_requests: 1,
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Trigger circuit breaker with failures
    for _ in 0..2 {
        assert!(cb_manager.should_allow_request(backend).await.unwrap());

        let result = RequestResult {
            timestamp: Instant::now(),
            success: false,
            duration: Duration::from_millis(100),
            error_type: Some("test_error".to_string()),
            status_code: Some(500),
        };

        cb_manager.record_request_result(backend, result).await.unwrap();
    }

    // Circuit should be open
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Open);

    // Wait for recovery timeout
    tokio::time::sleep(Duration::from_millis(150)).await;

    // Should allow request (transitions to half-open)
    assert!(cb_manager.should_allow_request(backend).await.unwrap());

    // Send successful request
    let result = RequestResult {
        timestamp: Instant::now(),
        success: true,
        duration: Duration::from_millis(50),
        error_type: None,
        status_code: Some(200),
    };

    cb_manager.record_request_result(backend, result).await.unwrap();

    // Circuit should now be closed
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Closed);
}

#[tokio::test]
async fn test_half_open_state_behavior() {
    let config = CircuitBreakerConfig {
        failure_threshold: 1,
        minimum_requests: 1,
        recovery_timeout: Duration::from_millis(50),
        success_threshold: 2,
        half_open_max_requests: 3,
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Trigger circuit breaker
    assert!(cb_manager.should_allow_request(backend).await.unwrap());
    let result = RequestResult {
        timestamp: Instant::now(),
        success: false,
        duration: Duration::from_millis(100),
        error_type: Some("test_error".to_string()),
        status_code: Some(500),
    };
    cb_manager.record_request_result(backend, result).await.unwrap();

    // Wait for recovery
    tokio::time::sleep(Duration::from_millis(100)).await;

    // Should be in half-open state after first request
    assert!(cb_manager.should_allow_request(backend).await.unwrap());
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    // State might be transitioning, so we'll test the behavior

    // Send successful requests to close circuit
    for _ in 0..2 {
        if cb_manager.should_allow_request(backend).await.unwrap() {
            let result = RequestResult {
                timestamp: Instant::now(),
                success: true,
                duration: Duration::from_millis(50),
                error_type: None,
                status_code: Some(200),
            };
            cb_manager.record_request_result(backend, result).await.unwrap();
        }
    }

    // Circuit should eventually be closed
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Closed);
}

#[tokio::test]
async fn test_half_open_failure_reopens_circuit() {
    let config = CircuitBreakerConfig {
        failure_threshold: 1,
        minimum_requests: 1,
        recovery_timeout: Duration::from_millis(50),
        success_threshold: 1,
        half_open_max_requests: 1,
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Trigger circuit breaker
    assert!(cb_manager.should_allow_request(backend).await.unwrap());
    let result = RequestResult {
        timestamp: Instant::now(),
        success: false,
        duration: Duration::from_millis(100),
        error_type: Some("test_error".to_string()),
        status_code: Some(500),
    };
    cb_manager.record_request_result(backend, result).await.unwrap();

    // Wait for recovery
    tokio::time::sleep(Duration::from_millis(100)).await;

    // Send another failed request in half-open state
    if cb_manager.should_allow_request(backend).await.unwrap() {
        let result = RequestResult {
            timestamp: Instant::now(),
            success: false,
            duration: Duration::from_millis(100),
            error_type: Some("test_error".to_string()),
            status_code: Some(500),
        };
        cb_manager.record_request_result(backend, result).await.unwrap();
    }

    // Circuit should be open again
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Open);
}

#[tokio::test]
async fn test_manual_circuit_control() {
    let config = CircuitBreakerConfig::default();
    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Initially closed
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Closed);

    // Manually open
    cb_manager.open_circuit(backend, "Test".to_string()).await.unwrap();
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Open);

    // Should block requests
    assert!(!cb_manager.should_allow_request(backend).await.unwrap());

    // Manually close
    cb_manager.close_circuit(backend, "Test complete".to_string()).await.unwrap();
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Closed);

    // Should allow requests
    assert!(cb_manager.should_allow_request(backend).await.unwrap());
}

#[tokio::test]
async fn test_circuit_breaker_statistics() {
    let config = CircuitBreakerConfig::default();
    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Send some requests
    for i in 0..10 {
        if cb_manager.should_allow_request(backend).await.unwrap() {
            let success = i % 3 != 0; // 2/3 success rate
            let result = RequestResult {
                timestamp: Instant::now(),
                success,
                duration: Duration::from_millis(100 + i * 10),
                error_type: if success { None } else { Some("test_error".to_string()) },
                status_code: if success { Some(200) } else { Some(500) },
            };

            cb_manager.record_request_result(backend, result).await.unwrap();
        }
    }

    // Check statistics
    let stats = cb_manager.get_circuit_stats().await;
    let backend_stats = stats.get(&backend).unwrap();

    assert!(backend_stats.total_requests > 0);
    assert!(backend_stats.successful_requests > 0);
    assert!(backend_stats.failed_requests > 0);
    assert!(backend_stats.current_failure_rate > 0.0);
    assert!(backend_stats.avg_response_time > Duration::from_millis(0));
}

#[tokio::test]
async fn test_multiple_backends() {
    let config = CircuitBreakerConfig {
        failure_threshold: 2,
        minimum_requests: 2,
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);

    let backend1: SocketAddr = "127.0.0.1:8081".parse().unwrap();
    let backend2: SocketAddr = "127.0.0.1:8082".parse().unwrap();

    cb_manager.add_backend(backend1).await.unwrap();
    cb_manager.add_backend(backend2).await.unwrap();

    // Make backend1 fail
    for _ in 0..2 {
        assert!(cb_manager.should_allow_request(backend1).await.unwrap());
        let result = RequestResult {
            timestamp: Instant::now(),
            success: false,
            duration: Duration::from_millis(100),
            error_type: Some("test_error".to_string()),
            status_code: Some(500),
        };
        cb_manager.record_request_result(backend1, result).await.unwrap();
    }

    // Make backend2 succeed
    for _ in 0..2 {
        assert!(cb_manager.should_allow_request(backend2).await.unwrap());
        let result = RequestResult {
            timestamp: Instant::now(),
            success: true,
            duration: Duration::from_millis(50),
            error_type: None,
            status_code: Some(200),
        };
        cb_manager.record_request_result(backend2, result).await.unwrap();
    }

    // Check states
    let state1 = cb_manager.get_circuit_state(backend1).await.unwrap();
    let state2 = cb_manager.get_circuit_state(backend2).await.unwrap();

    assert_eq!(state1, CircuitState::Open);
    assert_eq!(state2, CircuitState::Closed);

    // Backend1 should be blocked, backend2 should be allowed
    assert!(!cb_manager.should_allow_request(backend1).await.unwrap());
    assert!(cb_manager.should_allow_request(backend2).await.unwrap());
}

#[tokio::test]
async fn test_concurrent_requests() {
    let config = CircuitBreakerConfig {
        failure_threshold: 5,
        minimum_requests: 5,
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    let num_requests = 100;
    let mut handles = Vec::new();

    for i in 0..num_requests {
        let cb_manager_clone = cb_manager.clone();
        let handle = tokio::spawn(async move {
            if cb_manager_clone.should_allow_request(backend).await.unwrap() {
                let success = i % 4 != 0; // 75% success rate
                let result = RequestResult {
                    timestamp: Instant::now(),
                    success,
                    duration: Duration::from_millis(50 + (i % 100)),
                    error_type: if success { None } else { Some("concurrent_test_error".to_string()) },
                    status_code: if success { Some(200) } else { Some(500) },
                };

                cb_manager_clone.record_request_result(backend, result).await.unwrap();
                success
            } else {
                false // Request was blocked
            }
        });

        handles.push(handle);
    }

    // Wait for all requests to complete
    let mut successful_requests = 0;
    let mut blocked_requests = 0;

    for handle in handles {
        match handle.await.unwrap() {
            true => successful_requests += 1,
            false => blocked_requests += 1,
        }
    }

    println!("Concurrent test: {} successful, {} blocked", successful_requests, blocked_requests);

    // Should have processed some requests
    assert!(successful_requests > 0);

    // Check final statistics
    let stats = cb_manager.get_circuit_stats().await;
    let backend_stats = stats.get(&backend).unwrap();

    assert!(backend_stats.total_requests > 0);
    assert_eq!(backend_stats.total_requests, successful_requests as u64);
    assert_eq!(backend_stats.blocked_requests, blocked_requests as u64);
}

#[tokio::test]
async fn test_sliding_window_behavior() {
    let config = CircuitBreakerConfig {
        failure_threshold: 3,
        minimum_requests: 5,
        window_size: Duration::from_millis(200),
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Send some failures
    for _ in 0..3 {
        assert!(cb_manager.should_allow_request(backend).await.unwrap());
        let result = RequestResult {
            timestamp: Instant::now(),
            success: false,
            duration: Duration::from_millis(100),
            error_type: Some("test_error".to_string()),
            status_code: Some(500),
        };
        cb_manager.record_request_result(backend, result).await.unwrap();
    }

    // Send some successes
    for _ in 0..2 {
        assert!(cb_manager.should_allow_request(backend).await.unwrap());
        let result = RequestResult {
            timestamp: Instant::now(),
            success: true,
            duration: Duration::from_millis(50),
            error_type: None,
            status_code: Some(200),
        };
        cb_manager.record_request_result(backend, result).await.unwrap();
    }

    // Should still be closed (3/5 = 60% failure rate, but threshold might not be met)
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    // The exact state depends on the failure rate threshold

    // Wait for window to slide
    tokio::time::sleep(Duration::from_millis(250)).await;

    // Send more requests to test window sliding
    for _ in 0..3 {
        if cb_manager.should_allow_request(backend).await.unwrap() {
            let result = RequestResult {
                timestamp: Instant::now(),
                success: true,
                duration: Duration::from_millis(50),
                error_type: None,
                status_code: Some(200),
            };
            cb_manager.record_request_result(backend, result).await.unwrap();
        }
    }

    // Should be closed with recent successes
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    assert_eq!(state, CircuitState::Closed);
}

#[tokio::test]
async fn test_circuit_breaker_with_slow_requests() {
    let config = CircuitBreakerConfig {
        failure_threshold: 2,
        minimum_requests: 2,
        enable_slow_request_detection: true,
        slow_request_threshold: Duration::from_millis(100),
        slow_request_rate_threshold: 0.5, // 50%
        ..Default::default()
    };

    let cb_manager = CircuitBreakerManager::new(config);
    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    cb_manager.add_backend(backend).await.unwrap();

    // Send slow but successful requests
    for _ in 0..3 {
        assert!(cb_manager.should_allow_request(backend).await.unwrap());
        let result = RequestResult {
            timestamp: Instant::now(),
            success: true,
            duration: Duration::from_millis(200), // Slow request
            error_type: None,
            status_code: Some(200),
        };
        cb_manager.record_request_result(backend, result).await.unwrap();
    }

    // Circuit might open due to slow requests (depending on implementation)
    let state = cb_manager.get_circuit_state(backend).await.unwrap();
    // The behavior depends on how slow request detection is implemented

    println!("Circuit state after slow requests: {:?}", state);
}
```

## Performance Benchmarks

Create `benches/circuit_breaking_bench.rs`:

```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use rusty_balancer::circuit::{
    CircuitBreakerManager, CircuitBreakerConfig, RequestResult, CircuitState
};
use std::net::SocketAddr;
use std::time::{Duration, Instant};
use tokio::runtime::Runtime;

fn bench_circuit_breaker_check(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let config = CircuitBreakerConfig {
        failure_threshold: 5,
        minimum_requests: 10,
        failure_rate_threshold: 0.5,
        recovery_timeout: Duration::from_secs(60),
        ..Default::default()
    };

    let cb_manager = rt.block_on(async {
        let manager = CircuitBreakerManager::new(config);
        let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
        manager.add_backend(backend).await.unwrap();
        manager
    });

    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();

    c.bench_function("circuit_breaker_check", |b| {
        b.to_async(&rt).iter(|| async {
            black_box(cb_manager.should_allow_request(backend).await.unwrap())
        })
    });
}

fn bench_request_result_recording(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let config = CircuitBreakerConfig::default();
    let cb_manager = rt.block_on(async {
        let manager = CircuitBreakerManager::new(config);
        let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
        manager.add_backend(backend).await.unwrap();
        manager
    });

    let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();

    c.bench_function("request_result_recording", |b| {
        b.to_async(&rt).iter(|| async {
            let result = RequestResult {
                timestamp: Instant::now(),
                success: true,
                duration: Duration::from_millis(100),
                error_type: None,
                status_code: Some(200),
            };

            black_box(cb_manager.record_request_result(backend, result).await.unwrap())
        })
    });
}

fn bench_multiple_backends(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let backend_counts = vec![1, 10, 50, 100];
    let mut group = c.benchmark_group("multiple_backends");

    for &backend_count in &backend_counts {
        let config = CircuitBreakerConfig::default();
        let cb_manager = rt.block_on(async {
            let manager = CircuitBreakerManager::new(config);

            // Add multiple backends
            for i in 0..backend_count {
                let backend: SocketAddr = format!("127.0.0.1:{}", 8080 + i).parse().unwrap();
                manager.add_backend(backend).await.unwrap();
            }

            manager
        });

        group.bench_with_input(
            BenchmarkId::new("circuit_check", backend_count),
            &backend_count,
            |b, &backend_count| {
                b.to_async(&rt).iter(|| async {
                    // Check all backends
                    for i in 0..backend_count {
                        let backend: SocketAddr = format!("127.0.0.1:{}", 8080 + i).parse().unwrap();
                        black_box(cb_manager.should_allow_request(backend).await.unwrap());
                    }
                })
            },
        );
    }

    group.finish();
}

fn bench_concurrent_circuit_checks(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let config = CircuitBreakerConfig::default();
    let cb_manager = rt.block_on(async {
        let manager = CircuitBreakerManager::new(config);

        // Add multiple backends
        for i in 0..10 {
            let backend: SocketAddr = format!("127.0.0.1:{}", 8080 + i).parse().unwrap();
            manager.add_backend(backend).await.unwrap();
        }

        manager
    });

    let concurrency_levels = vec![1, 10, 50, 100];
    let mut group = c.benchmark_group("concurrent_circuit_checks");

    for &concurrency in &concurrency_levels {
        group.bench_with_input(
            BenchmarkId::new("concurrent_checks", concurrency),
            &concurrency,
            |b, &concurrency| {
                b.to_async(&rt).iter(|| async {
                    let mut handles = Vec::new();

                    for i in 0..concurrency {
                        let cb_manager_clone = cb_manager.clone();
                        let handle = tokio::spawn(async move {
                            let backend: SocketAddr = format!("127.0.0.1:{}", 8080 + (i % 10)).parse().unwrap();
                            cb_manager_clone.should_allow_request(backend).await.unwrap()
                        });

                        handles.push(handle);
                    }

                    // Wait for all checks to complete
                    for handle in handles {
                        black_box(handle.await.unwrap());
                    }
                })
            },
        );
    }

    group.finish();
}

fn bench_circuit_state_transitions(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let config = CircuitBreakerConfig {
        failure_threshold: 3,
        minimum_requests: 3,
        recovery_timeout: Duration::from_millis(10),
        success_threshold: 1,
        ..Default::default()
    };

    c.bench_function("circuit_state_transitions", |b| {
        b.to_async(&rt).iter(|| async {
            let cb_manager = CircuitBreakerManager::new(config.clone());
            let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
            cb_manager.add_backend(backend).await.unwrap();

            // Trigger circuit opening
            for _ in 0..3 {
                let _ = cb_manager.should_allow_request(backend).await.unwrap();
                let result = RequestResult {
                    timestamp: Instant::now(),
                    success: false,
                    duration: Duration::from_millis(100),
                    error_type: Some("bench_error".to_string()),
                    status_code: Some(500),
                };
                cb_manager.record_request_result(backend, result).await.unwrap();
            }

            // Wait for recovery
            tokio::time::sleep(Duration::from_millis(15)).await;

            // Trigger circuit closing
            if cb_manager.should_allow_request(backend).await.unwrap() {
                let result = RequestResult {
                    timestamp: Instant::now(),
                    success: true,
                    duration: Duration::from_millis(50),
                    error_type: None,
                    status_code: Some(200),
                };
                cb_manager.record_request_result(backend, result).await.unwrap();
            }

            black_box(cb_manager.get_circuit_state(backend).await.unwrap())
        })
    });
}

fn bench_sliding_window_performance(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let window_sizes = vec![
        Duration::from_secs(10),
        Duration::from_secs(60),
        Duration::from_secs(300),
    ];

    let mut group = c.benchmark_group("sliding_window_performance");

    for window_size in window_sizes {
        let config = CircuitBreakerConfig {
            window_size,
            failure_threshold: 10,
            minimum_requests: 20,
            ..Default::default()
        };

        let cb_manager = rt.block_on(async {
            let manager = CircuitBreakerManager::new(config);
            let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
            manager.add_backend(backend).await.unwrap();

            // Pre-populate with some history
            for i in 0..50 {
                let _ = manager.should_allow_request(backend).await.unwrap();
                let result = RequestResult {
                    timestamp: Instant::now(),
                    success: i % 3 != 0, // 2/3 success rate
                    duration: Duration::from_millis(50 + (i % 100)),
                    error_type: if i % 3 == 0 { Some("test_error".to_string()) } else { None },
                    status_code: if i % 3 == 0 { Some(500) } else { Some(200) },
                };
                manager.record_request_result(backend, result).await.unwrap();
            }

            manager
        });

        let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();

        group.bench_with_input(
            BenchmarkId::new("window_evaluation", format!("{}s", window_size.as_secs())),
            &window_size,
            |b, _| {
                b.to_async(&rt).iter(|| async {
                    // Record a new request and evaluate circuit
                    if cb_manager.should_allow_request(backend).await.unwrap() {
                        let result = RequestResult {
                            timestamp: Instant::now(),
                            success: true,
                            duration: Duration::from_millis(75),
                            error_type: None,
                            status_code: Some(200),
                        };
                        cb_manager.record_request_result(backend, result).await.unwrap();
                    }

                    black_box(cb_manager.get_circuit_state(backend).await.unwrap())
                })
            },
        );
    }

    group.finish();
}

fn bench_circuit_statistics(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let config = CircuitBreakerConfig::default();
    let cb_manager = rt.block_on(async {
        let manager = CircuitBreakerManager::new(config);

        // Add multiple backends with history
        for i in 0..10 {
            let backend: SocketAddr = format!("127.0.0.1:{}", 8080 + i).parse().unwrap();
            manager.add_backend(backend).await.unwrap();

            // Add some request history
            for j in 0..20 {
                let _ = manager.should_allow_request(backend).await.unwrap();
                let result = RequestResult {
                    timestamp: Instant::now(),
                    success: j % 4 != 0, // 75% success rate
                    duration: Duration::from_millis(50 + (j % 100)),
                    error_type: if j % 4 == 0 { Some("test_error".to_string()) } else { None },
                    status_code: if j % 4 == 0 { Some(500) } else { Some(200) },
                };
                manager.record_request_result(backend, result).await.unwrap();
            }
        }

        manager
    });

    c.bench_function("circuit_statistics", |b| {
        b.to_async(&rt).iter(|| async {
            black_box(cb_manager.get_circuit_stats().await)
        })
    });
}

fn bench_memory_usage_large_history(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let config = CircuitBreakerConfig {
        window_size: Duration::from_secs(300), // Large window
        ..Default::default()
    };

    c.bench_function("memory_usage_large_history", |b| {
        b.to_async(&rt).iter(|| async {
            let cb_manager = CircuitBreakerManager::new(config.clone());
            let backend: SocketAddr = "127.0.0.1:8080".parse().unwrap();
            cb_manager.add_backend(backend).await.unwrap();

            // Generate large request history
            for i in 0..1000 {
                if cb_manager.should_allow_request(backend).await.unwrap() {
                    let result = RequestResult {
                        timestamp: Instant::now(),
                        success: i % 5 != 0, // 80% success rate
                        duration: Duration::from_millis(50 + (i % 200)),
                        error_type: if i % 5 == 0 { Some("test_error".to_string()) } else { None },
                        status_code: if i % 5 == 0 { Some(500) } else { Some(200) },
                    };
                    cb_manager.record_request_result(backend, result).await.unwrap();
                }
            }

            black_box(cb_manager.get_circuit_stats().await)
        })
    });
}

criterion_group!(
    benches,
    bench_circuit_breaker_check,
    bench_request_result_recording,
    bench_multiple_backends,
    bench_concurrent_circuit_checks,
    bench_circuit_state_transitions,
    bench_sliding_window_performance,
    bench_circuit_statistics,
    bench_memory_usage_large_history
);
criterion_main!(benches);
```

## Navigation
- [Previous: Connection Pooling](16-connection-pooling.md)
- [Next: Graceful Shutdown](18-graceful-shutdown.md)
