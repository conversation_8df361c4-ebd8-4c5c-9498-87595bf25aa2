# Module 13: Weighted Load Balancing

## Learning Objectives
- Implement advanced weighted load balancing algorithms for optimal traffic distribution
- Design dynamic weight adjustment based on server performance and capacity
- Build weighted round-robin, least connections, and consistent hashing algorithms
- Create performance-based weight calculation and auto-scaling integration
- Understand weighted load balancing patterns and optimization strategies

## Why Weighted Load Balancing Matters

Weighted load balancing enables optimal resource utilization by considering server capabilities:

1. **Heterogeneous Infrastructure**: Different server capacities and performance characteristics
2. **Cost Optimization**: Maximize utilization of expensive high-performance servers
3. **Performance Tuning**: Direct more traffic to servers that can handle it efficiently
4. **Gradual Deployments**: Control traffic distribution during deployments and scaling
5. **Geographic Distribution**: Weight servers based on proximity and network conditions

### Weighted Load Balancing Architecture

```mermaid
graph TD
    Client[Client Requests] --> LB[Load Balancer]
    LB --> WS[Weight Selector]
    
    WS --> WRR[Weighted Round Robin]
    WS --> WLC[Weighted Least Connections]
    WS --> WCH[Weighted Consistent Hash]
    
    subgraph "Backend Servers"
        B1[Backend 1<br/>Weight: 3<br/>CPU: 2 cores<br/>RAM: 4GB]
        B2[Backend 2<br/>Weight: 5<br/>CPU: 4 cores<br/>RAM: 8GB]
        B3[Backend 3<br/>Weight: 2<br/>CPU: 1 core<br/>RAM: 2GB]
    end
    
    WRR --> B1
    WRR --> B2
    WRR --> B3
    
    WLC --> B1
    WLC --> B2
    WLC --> B3
    
    WCH --> B1
    WCH --> B2
    WCH --> B3
    
    subgraph "Weight Management"
        Static[Static Weights]
        Dynamic[Dynamic Weights]
        Performance[Performance-Based]
        Health[Health-Based]
    end
    
    Static --> WS
    Dynamic --> WS
    Performance --> WS
    Health --> WS
    
    subgraph "Monitoring"
        Metrics[Weight Metrics]
        Adjustment[Auto Adjustment]
        Alerts[Weight Alerts]
    end
    
    WS --> Metrics
    Metrics --> Adjustment
    Adjustment --> Dynamic
    Metrics --> Alerts
```

## Weighted Load Balancing Implementation

### Design Decisions

**Why multiple weighted algorithms?**
- **Flexibility**: Different algorithms suit different use cases
- **Performance**: Optimize for specific traffic patterns and requirements
- **Fairness**: Balance between weighted distribution and connection fairness
- **Adaptability**: Switch algorithms based on runtime conditions

**Why dynamic weight adjustment?**
- **Responsiveness**: Adapt to changing server conditions in real-time
- **Efficiency**: Maximize resource utilization across the server pool
- **Reliability**: Reduce load on struggling servers automatically
- **Scalability**: Handle varying traffic patterns and server capacities

Create `src/balancing/weighted.rs`:

```rust
//! Advanced weighted load balancing algorithms
//!
//! This module provides sophisticated weighted load balancing with:
//! - Multiple weighted algorithms (round-robin, least connections, consistent hash)
//! - Dynamic weight adjustment based on performance metrics
//! - Health-based weight modification
//! - Performance monitoring and optimization
//! - Auto-scaling integration

use crate::backend::{Backend, BackendPool};
use crate::metrics::LoadBalancerMetrics;
use crate::error::{LoadBalancerError, Result};
use std::collections::{HashMap, BTreeMap};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{info, warn, debug, trace};
use serde::{Serialize, Deserialize};

/// Weighted load balancer with multiple algorithms
pub struct WeightedLoadBalancer {
    /// Backend pool with weight information
    backend_pool: Arc<RwLock<WeightedBackendPool>>,
    
    /// Current balancing algorithm
    algorithm: WeightedAlgorithm,
    
    /// Weight manager for dynamic adjustments
    weight_manager: Arc<WeightManager>,
    
    /// Metrics collector
    metrics: Option<Arc<LoadBalancerMetrics>>,
    
    /// Configuration
    config: WeightedBalancingConfig,
}

/// Weighted balancing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WeightedBalancingConfig {
    /// Default weight for new backends
    pub default_weight: u32,
    
    /// Minimum weight (prevents zero weight)
    pub min_weight: u32,
    
    /// Maximum weight (prevents excessive weight)
    pub max_weight: u32,
    
    /// Enable dynamic weight adjustment
    pub enable_dynamic_weights: bool,
    
    /// Weight adjustment interval
    pub adjustment_interval: Duration,
    
    /// Performance window for weight calculation
    pub performance_window: Duration,
    
    /// Weight adjustment sensitivity (0.0 - 1.0)
    pub adjustment_sensitivity: f64,
    
    /// Enable health-based weight modification
    pub enable_health_weights: bool,
    
    /// Unhealthy server weight multiplier
    pub unhealthy_weight_multiplier: f64,
}

/// Weighted load balancing algorithms
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum WeightedAlgorithm {
    WeightedRoundRobin,
    WeightedLeastConnections,
    WeightedConsistentHash,
    WeightedResponseTime,
}

/// Backend with weight information
#[derive(Debug, Clone)]
pub struct WeightedBackend {
    /// Backend server information
    pub backend: Backend,
    
    /// Current effective weight
    pub current_weight: u32,
    
    /// Base configured weight
    pub base_weight: u32,
    
    /// Dynamic weight adjustment
    pub dynamic_weight: f64,
    
    /// Health-based weight multiplier
    pub health_multiplier: f64,
    
    /// Performance metrics
    pub performance: BackendPerformance,
    
    /// Weight history for trending
    pub weight_history: Vec<WeightHistoryEntry>,
}

/// Backend performance metrics for weight calculation
#[derive(Debug, Clone)]
pub struct BackendPerformance {
    /// Average response time
    pub avg_response_time: Duration,
    
    /// Current active connections
    pub active_connections: u32,
    
    /// Success rate (0.0 - 1.0)
    pub success_rate: f64,
    
    /// CPU utilization (0.0 - 1.0)
    pub cpu_utilization: f64,
    
    /// Memory utilization (0.0 - 1.0)
    pub memory_utilization: f64,
    
    /// Last update timestamp
    pub last_updated: Instant,
}

/// Weight history entry for trending analysis
#[derive(Debug, Clone)]
pub struct WeightHistoryEntry {
    pub timestamp: Instant,
    pub weight: u32,
    pub reason: WeightChangeReason,
}

/// Reasons for weight changes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WeightChangeReason {
    Manual,
    PerformanceBased,
    HealthBased,
    LoadBased,
    ConfigurationUpdate,
}

/// Weighted backend pool
pub struct WeightedBackendPool {
    /// Backends with weights
    backends: HashMap<SocketAddr, WeightedBackend>,
    
    /// Round-robin state for weighted round-robin
    rr_state: WeightedRoundRobinState,
    
    /// Consistent hash ring for weighted consistent hashing
    hash_ring: ConsistentHashRing,
}

/// Weighted round-robin algorithm state
#[derive(Debug)]
struct WeightedRoundRobinState {
    /// Current weights for each backend
    current_weights: HashMap<SocketAddr, i32>,
    
    /// Total weight sum
    total_weight: i32,
}

/// Consistent hash ring for weighted consistent hashing
#[derive(Debug)]
struct ConsistentHashRing {
    /// Hash ring with virtual nodes
    ring: BTreeMap<u64, SocketAddr>,
    
    /// Virtual nodes per backend (based on weight)
    virtual_nodes: HashMap<SocketAddr, u32>,
}

impl WeightedLoadBalancer {
    /// Create a new weighted load balancer
    pub fn new(
        algorithm: WeightedAlgorithm,
        config: WeightedBalancingConfig,
    ) -> Self {
        let backend_pool = Arc::new(RwLock::new(WeightedBackendPool::new()));
        let weight_manager = Arc::new(WeightManager::new(config.clone()));
        
        info!("⚖️ Weighted load balancer initialized with algorithm: {:?}", algorithm);
        
        Self {
            backend_pool,
            algorithm,
            weight_manager,
            metrics: None,
            config,
        }
    }
    
    /// Set metrics collector
    pub fn with_metrics(mut self, metrics: Arc<LoadBalancerMetrics>) -> Self {
        self.metrics = Some(metrics);
        self
    }
    
    /// Add a weighted backend
    pub async fn add_backend(&self, address: SocketAddr, weight: u32) -> Result<()> {
        let effective_weight = weight.clamp(self.config.min_weight, self.config.max_weight);
        
        let backend = Backend::new(address);
        let weighted_backend = WeightedBackend {
            backend,
            current_weight: effective_weight,
            base_weight: effective_weight,
            dynamic_weight: 1.0,
            health_multiplier: 1.0,
            performance: BackendPerformance::default(),
            weight_history: vec![WeightHistoryEntry {
                timestamp: Instant::now(),
                weight: effective_weight,
                reason: WeightChangeReason::Manual,
            }],
        };
        
        let mut pool = self.backend_pool.write().await;
        pool.add_backend(address, weighted_backend)?;
        
        info!("➕ Added weighted backend: {} (weight: {})", address, effective_weight);
        Ok(())
    }
    
    /// Remove a backend
    pub async fn remove_backend(&self, address: SocketAddr) -> Result<()> {
        let mut pool = self.backend_pool.write().await;
        pool.remove_backend(address)?;
        
        info!("➖ Removed weighted backend: {}", address);
        Ok(())
    }
    
    /// Update backend weight
    pub async fn update_weight(&self, address: SocketAddr, new_weight: u32, reason: WeightChangeReason) -> Result<()> {
        let effective_weight = new_weight.clamp(self.config.min_weight, self.config.max_weight);
        
        let mut pool = self.backend_pool.write().await;
        if let Some(backend) = pool.backends.get_mut(&address) {
            let old_weight = backend.current_weight;
            backend.current_weight = effective_weight;
            backend.base_weight = effective_weight;
            
            // Record weight change in history
            backend.weight_history.push(WeightHistoryEntry {
                timestamp: Instant::now(),
                weight: effective_weight,
                reason: reason.clone(),
            });
            
            // Keep only recent history
            if backend.weight_history.len() > 100 {
                backend.weight_history.remove(0);
            }
            
            // Update hash ring if using consistent hashing
            if matches!(self.algorithm, WeightedAlgorithm::WeightedConsistentHash) {
                pool.hash_ring.update_backend_weight(address, effective_weight);
            }
            
            info!("⚖️ Updated weight for {}: {} -> {} (reason: {:?})", 
                  address, old_weight, effective_weight, reason);
            
            if let Some(ref metrics) = self.metrics {
                // Record weight change metric
                debug!("Recording weight change metric for {}", address);
            }
        } else {
            return Err(LoadBalancerError::Backend {
                message: format!("Backend {} not found", address),
                backend_address: address,
                status_code: None,
                response_time: None,
                error_type: crate::error::BackendErrorType::InvalidResponse,
            });
        }
        
        Ok(())
    }
    
    /// Select next backend using weighted algorithm
    pub async fn select_backend(&self, client_key: Option<&str>) -> Option<SocketAddr> {
        let pool = self.backend_pool.read().await;
        
        match self.algorithm {
            WeightedAlgorithm::WeightedRoundRobin => {
                self.select_weighted_round_robin(&pool).await
            }
            WeightedAlgorithm::WeightedLeastConnections => {
                self.select_weighted_least_connections(&pool).await
            }
            WeightedAlgorithm::WeightedConsistentHash => {
                self.select_weighted_consistent_hash(&pool, client_key).await
            }
            WeightedAlgorithm::WeightedResponseTime => {
                self.select_weighted_response_time(&pool).await
            }
        }
    }
    
    /// Weighted round-robin selection
    async fn select_weighted_round_robin(&self, pool: &WeightedBackendPool) -> Option<SocketAddr> {
        if pool.backends.is_empty() {
            return None;
        }
        
        // Find backend with highest current weight
        let mut selected_backend = None;
        let mut max_current_weight = i32::MIN;
        
        for (addr, backend) in &pool.backends {
            if !backend.backend.is_healthy() {
                continue;
            }
            
            let current_weight = pool.rr_state.current_weights.get(addr).copied().unwrap_or(0);
            if current_weight > max_current_weight {
                max_current_weight = current_weight;
                selected_backend = Some(*addr);
            }
        }
        
        if let Some(selected) = selected_backend {
            // Update weights for next selection
            // This would be implemented with proper synchronization in production
            trace!("Selected backend {} with weight {}", selected, max_current_weight);
        }
        
        selected_backend
    }
    
    /// Weighted least connections selection
    async fn select_weighted_least_connections(&self, pool: &WeightedBackendPool) -> Option<SocketAddr> {
        if pool.backends.is_empty() {
            return None;
        }
        
        let mut best_backend = None;
        let mut best_ratio = f64::INFINITY;
        
        for (addr, backend) in &pool.backends {
            if !backend.backend.is_healthy() {
                continue;
            }
            
            let connections = backend.performance.active_connections as f64;
            let weight = backend.current_weight as f64;
            let ratio = if weight > 0.0 { connections / weight } else { f64::INFINITY };
            
            if ratio < best_ratio {
                best_ratio = ratio;
                best_backend = Some(*addr);
            }
        }
        
        if let Some(selected) = best_backend {
            trace!("Selected backend {} with connection ratio {:.2}", selected, best_ratio);
        }
        
        best_backend
    }
    
    /// Weighted consistent hash selection
    async fn select_weighted_consistent_hash(&self, pool: &WeightedBackendPool, client_key: Option<&str>) -> Option<SocketAddr> {
        let key = client_key.unwrap_or("default");
        pool.hash_ring.get_backend(key)
    }
    
    /// Weighted response time selection
    async fn select_weighted_response_time(&self, pool: &WeightedBackendPool) -> Option<SocketAddr> {
        if pool.backends.is_empty() {
            return None;
        }
        
        let mut best_backend = None;
        let mut best_score = f64::INFINITY;
        
        for (addr, backend) in &pool.backends {
            if !backend.backend.is_healthy() {
                continue;
            }
            
            let response_time_ms = backend.performance.avg_response_time.as_millis() as f64;
            let weight = backend.current_weight as f64;
            let score = if weight > 0.0 { response_time_ms / weight } else { f64::INFINITY };
            
            if score < best_score {
                best_score = score;
                best_backend = Some(*addr);
            }
        }
        
        if let Some(selected) = best_backend {
            trace!("Selected backend {} with response time score {:.2}", selected, best_score);
        }
        
        best_backend
    }
    
    /// Start dynamic weight adjustment
    pub async fn start_weight_adjustment(&self) -> Result<()> {
        if !self.config.enable_dynamic_weights {
            info!("Dynamic weight adjustment is disabled");
            return Ok(());
        }
        
        let backend_pool = self.backend_pool.clone();
        let weight_manager = self.weight_manager.clone();
        let config = self.config.clone();
        let metrics = self.metrics.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.adjustment_interval);
            
            info!("🔄 Starting dynamic weight adjustment (interval: {:?})", config.adjustment_interval);
            
            loop {
                interval.tick().await;
                
                let pool = backend_pool.read().await;
                for (addr, backend) in &pool.backends {
                    if let Some(new_weight) = weight_manager.calculate_dynamic_weight(backend, &config).await {
                        drop(pool); // Release read lock
                        
                        // Update weight if it changed significantly
                        let current_weight = {
                            let pool = backend_pool.read().await;
                            pool.backends.get(addr).map(|b| b.current_weight).unwrap_or(0)
                        };
                        
                        if (new_weight as i32 - current_weight as i32).abs() > 1 {
                            if let Ok(mut pool) = backend_pool.try_write() {
                                if let Some(backend) = pool.backends.get_mut(addr) {
                                    backend.current_weight = new_weight;
                                    backend.weight_history.push(WeightHistoryEntry {
                                        timestamp: Instant::now(),
                                        weight: new_weight,
                                        reason: WeightChangeReason::PerformanceBased,
                                    });
                                    
                                    debug!("🎯 Adjusted weight for {}: {} -> {} (performance-based)", 
                                           addr, current_weight, new_weight);
                                    
                                    if let Some(ref metrics) = metrics {
                                        // Record dynamic weight adjustment
                                        trace!("Recording dynamic weight adjustment for {}", addr);
                                    }
                                }
                            }
                        }
                        
                        let pool = backend_pool.read().await; // Re-acquire read lock
                    }
                }
            }
        });
        
        Ok(())
    }
    
    /// Get current backend weights
    pub async fn get_backend_weights(&self) -> HashMap<SocketAddr, u32> {
        let pool = self.backend_pool.read().await;
        pool.backends.iter()
            .map(|(addr, backend)| (*addr, backend.current_weight))
            .collect()
    }
    
    /// Get weight statistics
    pub async fn get_weight_stats(&self) -> WeightStats {
        let pool = self.backend_pool.read().await;
        
        let total_backends = pool.backends.len();
        let total_weight: u32 = pool.backends.values().map(|b| b.current_weight).sum();
        let avg_weight = if total_backends > 0 { total_weight as f64 / total_backends as f64 } else { 0.0 };
        
        let min_weight = pool.backends.values().map(|b| b.current_weight).min().unwrap_or(0);
        let max_weight = pool.backends.values().map(|b| b.current_weight).max().unwrap_or(0);
        
        WeightStats {
            total_backends,
            total_weight,
            avg_weight,
            min_weight,
            max_weight,
            algorithm: self.algorithm,
        }
    }
}

/// Weight statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WeightStats {
    pub total_backends: usize,
    pub total_weight: u32,
    pub avg_weight: f64,
    pub min_weight: u32,
    pub max_weight: u32,
    pub algorithm: WeightedAlgorithm,
}

/// Weight manager for dynamic adjustments
pub struct WeightManager {
    config: WeightedBalancingConfig,
}

impl WeightManager {
    fn new(config: WeightedBalancingConfig) -> Self {
        Self { config }
    }
    
    async fn calculate_dynamic_weight(&self, backend: &WeightedBackend, config: &WeightedBalancingConfig) -> Option<u32> {
        // Calculate performance-based weight adjustment
        let performance_score = self.calculate_performance_score(&backend.performance);
        
        // Apply sensitivity factor
        let adjustment_factor = 1.0 + (performance_score - 1.0) * config.adjustment_sensitivity;
        
        // Calculate new weight
        let base_weight = backend.base_weight as f64;
        let new_weight = (base_weight * adjustment_factor * backend.health_multiplier).round() as u32;
        
        // Clamp to configured limits
        let clamped_weight = new_weight.clamp(config.min_weight, config.max_weight);
        
        Some(clamped_weight)
    }
    
    fn calculate_performance_score(&self, performance: &BackendPerformance) -> f64 {
        // Combine multiple performance metrics into a single score
        let response_time_score = self.response_time_score(performance.avg_response_time);
        let success_rate_score = performance.success_rate;
        let resource_score = 1.0 - (performance.cpu_utilization + performance.memory_utilization) / 2.0;
        
        // Weighted average of different metrics
        (response_time_score * 0.4 + success_rate_score * 0.4 + resource_score * 0.2).clamp(0.1, 2.0)
    }
    
    fn response_time_score(&self, response_time: Duration) -> f64 {
        // Convert response time to a score (lower is better)
        let ms = response_time.as_millis() as f64;
        if ms <= 50.0 {
            1.5 // Excellent response time
        } else if ms <= 100.0 {
            1.2 // Good response time
        } else if ms <= 200.0 {
            1.0 // Average response time
        } else if ms <= 500.0 {
            0.8 // Poor response time
        } else {
            0.5 // Very poor response time
        }
    }
}

impl WeightedBackendPool {
    fn new() -> Self {
        Self {
            backends: HashMap::new(),
            rr_state: WeightedRoundRobinState::new(),
            hash_ring: ConsistentHashRing::new(),
        }
    }

    fn add_backend(&mut self, address: SocketAddr, backend: WeightedBackend) -> Result<()> {
        let weight = backend.current_weight;

        // Add to backends map
        self.backends.insert(address, backend);

        // Update round-robin state
        self.rr_state.add_backend(address, weight as i32);

        // Update consistent hash ring
        self.hash_ring.add_backend(address, weight);

        Ok(())
    }

    fn remove_backend(&mut self, address: SocketAddr) -> Result<()> {
        if self.backends.remove(&address).is_some() {
            self.rr_state.remove_backend(address);
            self.hash_ring.remove_backend(address);
            Ok(())
        } else {
            Err(LoadBalancerError::Backend {
                message: format!("Backend {} not found", address),
                backend_address: address,
                status_code: None,
                response_time: None,
                error_type: crate::error::BackendErrorType::InvalidResponse,
            })
        }
    }
}

impl WeightedRoundRobinState {
    fn new() -> Self {
        Self {
            current_weights: HashMap::new(),
            total_weight: 0,
        }
    }

    fn add_backend(&mut self, address: SocketAddr, weight: i32) {
        self.current_weights.insert(address, weight);
        self.total_weight += weight;
    }

    fn remove_backend(&mut self, address: SocketAddr) {
        if let Some(weight) = self.current_weights.remove(&address) {
            self.total_weight -= weight;
        }
    }

    fn update_weights(&mut self, selected: SocketAddr) {
        // Decrease selected backend's current weight by total weight
        if let Some(current) = self.current_weights.get_mut(&selected) {
            *current -= self.total_weight;
        }

        // Increase all backends' current weights by their configured weights
        for (addr, current_weight) in &mut self.current_weights {
            // In a real implementation, you'd store the configured weights
            // For now, we'll simulate this
            *current_weight += 1; // Simplified
        }
    }
}

impl ConsistentHashRing {
    fn new() -> Self {
        Self {
            ring: BTreeMap::new(),
            virtual_nodes: HashMap::new(),
        }
    }

    fn add_backend(&mut self, address: SocketAddr, weight: u32) {
        // Number of virtual nodes based on weight
        let virtual_node_count = weight * 100; // 100 virtual nodes per weight unit
        self.virtual_nodes.insert(address, virtual_node_count);

        // Add virtual nodes to the ring
        for i in 0..virtual_node_count {
            let virtual_key = format!("{}:{}", address, i);
            let hash = self.hash_key(&virtual_key);
            self.ring.insert(hash, address);
        }
    }

    fn remove_backend(&mut self, address: SocketAddr) {
        if let Some(virtual_count) = self.virtual_nodes.remove(&address) {
            // Remove virtual nodes from the ring
            for i in 0..virtual_count {
                let virtual_key = format!("{}:{}", address, i);
                let hash = self.hash_key(&virtual_key);
                self.ring.remove(&hash);
            }
        }
    }

    fn update_backend_weight(&mut self, address: SocketAddr, new_weight: u32) {
        self.remove_backend(address);
        self.add_backend(address, new_weight);
    }

    fn get_backend(&self, key: &str) -> Option<SocketAddr> {
        if self.ring.is_empty() {
            return None;
        }

        let hash = self.hash_key(key);

        // Find the first backend with hash >= key hash
        if let Some((_, &backend)) = self.ring.range(hash..).next() {
            Some(backend)
        } else {
            // Wrap around to the first backend
            self.ring.values().next().copied()
        }
    }

    fn hash_key(&self, key: &str) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        key.hash(&mut hasher);
        hasher.finish()
    }
}

impl BackendPerformance {
    fn default() -> Self {
        Self {
            avg_response_time: Duration::from_millis(100),
            active_connections: 0,
            success_rate: 1.0,
            cpu_utilization: 0.5,
            memory_utilization: 0.5,
            last_updated: Instant::now(),
        }
    }

    /// Update performance metrics
    pub fn update(&mut self, response_time: Duration, success: bool, connections: u32) {
        // Update average response time with exponential moving average
        let alpha = 0.1; // Smoothing factor
        let new_time_ms = response_time.as_millis() as f64;
        let current_time_ms = self.avg_response_time.as_millis() as f64;
        let updated_time_ms = alpha * new_time_ms + (1.0 - alpha) * current_time_ms;
        self.avg_response_time = Duration::from_millis(updated_time_ms as u64);

        // Update success rate with exponential moving average
        let success_value = if success { 1.0 } else { 0.0 };
        self.success_rate = alpha * success_value + (1.0 - alpha) * self.success_rate;

        // Update connection count
        self.active_connections = connections;

        self.last_updated = Instant::now();
    }
}

impl Default for WeightedBalancingConfig {
    fn default() -> Self {
        Self {
            default_weight: 1,
            min_weight: 1,
            max_weight: 100,
            enable_dynamic_weights: true,
            adjustment_interval: Duration::from_secs(30),
            performance_window: Duration::from_secs(300),
            adjustment_sensitivity: 0.5,
            enable_health_weights: true,
            unhealthy_weight_multiplier: 0.1,
        }
    }
}
```

## Integration Example

Create `examples/weighted_balancing_example.rs`:

```rust
//! Example demonstrating weighted load balancing with dynamic adjustments

use rusty_balancer::balancing::{WeightedLoadBalancer, WeightedAlgorithm, WeightedBalancingConfig, WeightChangeReason};
use std::net::SocketAddr;
use std::time::Duration;
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("⚖️ Starting Weighted Load Balancing Example");

    // Create weighted balancing configuration
    let config = WeightedBalancingConfig {
        default_weight: 1,
        min_weight: 1,
        max_weight: 10,
        enable_dynamic_weights: true,
        adjustment_interval: Duration::from_secs(10),
        adjustment_sensitivity: 0.3,
        ..Default::default()
    };

    // Create weighted load balancer with different algorithms
    let algorithms = [
        WeightedAlgorithm::WeightedRoundRobin,
        WeightedAlgorithm::WeightedLeastConnections,
        WeightedAlgorithm::WeightedConsistentHash,
        WeightedAlgorithm::WeightedResponseTime,
    ];

    for algorithm in &algorithms {
        info!("🧪 Testing algorithm: {:?}", algorithm);

        let balancer = WeightedLoadBalancer::new(*algorithm, config.clone());

        // Add backends with different weights
        balancer.add_backend("127.0.0.1:8081".parse()?, 1).await?; // Low-spec server
        balancer.add_backend("127.0.0.1:8082".parse()?, 3).await?; // Medium-spec server
        balancer.add_backend("127.0.0.1:8083".parse()?, 5).await?; // High-spec server

        // Start dynamic weight adjustment
        balancer.start_weight_adjustment().await?;

        // Simulate load balancing decisions
        info!("📊 Simulating load balancing decisions:");
        for i in 0..20 {
            let client_key = format!("client_{}", i);
            if let Some(backend) = balancer.select_backend(Some(&client_key)).await {
                info!("Request {} -> {}", i, backend);
            }

            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        // Update weights manually
        info!("🔧 Updating weights manually:");
        balancer.update_weight(
            "127.0.0.1:8081".parse()?,
            2,
            WeightChangeReason::Manual,
        ).await?;

        // Show weight statistics
        let stats = balancer.get_weight_stats().await;
        info!("📈 Weight Statistics: {:?}", stats);

        // Show current weights
        let weights = balancer.get_backend_weights().await;
        info!("⚖️ Current Weights: {:?}", weights);

        info!("✅ Algorithm {:?} test completed\n", algorithm);
    }

    Ok(())
}
```

## Key Design Decisions Explained

### 1. Multiple Weighted Algorithms
**Decision**: Support multiple weighted load balancing algorithms

**Rationale**:
- **Flexibility**: Different algorithms suit different use cases and traffic patterns
- **Performance**: Optimize for specific requirements (latency, throughput, fairness)
- **Adaptability**: Switch algorithms based on runtime conditions
- **Compatibility**: Support various client and application requirements

### 2. Dynamic Weight Adjustment
**Decision**: Automatically adjust weights based on performance metrics

**Rationale**:
- **Responsiveness**: Adapt to changing server conditions in real-time
- **Efficiency**: Maximize resource utilization across the server pool
- **Reliability**: Reduce load on struggling servers automatically
- **Scalability**: Handle varying traffic patterns and server capacities

### 3. Performance-Based Weight Calculation
**Decision**: Use multiple metrics for weight calculation (response time, success rate, resource utilization)

**Rationale**:
- **Comprehensive**: Consider multiple aspects of server performance
- **Balanced**: Avoid over-optimization for single metrics
- **Realistic**: Reflect actual server capacity and health
- **Predictive**: Use trending data for better weight decisions

## Performance Considerations

1. **Algorithm Efficiency**: O(1) selection for most algorithms
2. **Memory Usage**: Efficient storage of weight and performance data
3. **Update Overhead**: Minimal impact of dynamic weight adjustments
4. **Hash Ring Optimization**: Efficient consistent hashing implementation

## Security Considerations

1. **Weight Validation**: Prevent malicious weight manipulation
2. **Performance Data**: Secure collection and transmission of metrics
3. **Access Control**: Restrict weight modification capabilities
4. **Audit Trail**: Log all weight changes with reasons and timestamps

## Navigation
- [Previous: Hot Reloading](12-hot-reloading.md)
- [Next: Sticky Sessions](14-sticky-sessions.md)
