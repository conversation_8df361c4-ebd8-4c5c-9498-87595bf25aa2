# Module 19: Caching and Storage

## Learning Objectives
- Implement comprehensive caching strategies for improved performance and reduced backend load
- Design multi-tier caching with memory, disk, and distributed cache layers
- Build cache invalidation and consistency mechanisms for data integrity
- Create cache analytics and optimization strategies for maximum efficiency
- Understand caching patterns and storage optimization techniques

## Why Caching and Storage Matter

Effective caching dramatically improves load balancer performance and reduces backend load:

1. **Performance**: Serve cached responses with minimal latency
2. **Scalability**: Reduce backend load and handle more concurrent requests
3. **Reliability**: Serve stale content during backend outages
4. **Cost Efficiency**: Reduce bandwidth and compute costs
5. **User Experience**: Faster response times and improved availability

### Caching Architecture

```mermaid
graph TD
    Client[Client Request] --> LB[Load Balancer]
    LB --> CM[Cache Manager]
    
    CM --> L1[L1 Cache<br/>In-Memory<br/>Hot Data]
    CM --> L2[L2 Cache<br/>Local Disk<br/>Warm Data]
    CM --> L3[L3 Cache<br/>Distributed<br/>Cold Data]
    
    subgraph "Cache Layers"
        L1 --> |Miss| L2
        L2 --> |Miss| L3
        L3 --> |Miss| Backend[Backend Servers]
    end
    
    subgraph "Cache Policies"
        LRU[LRU Eviction]
        TTL[TTL Expiration]
        Size[Size Limits]
        Priority[Priority-based]
    end
    
    CM --> LRU
    CM --> TTL
    CM --> Size
    CM --> Priority
    
    subgraph "Cache Operations"
        Get[Cache Get]
        Set[Cache Set]
        Delete[Cache Delete]
        Invalidate[Cache Invalidate]
    end
    
    CM --> Get
    CM --> Set
    CM --> Delete
    CM --> Invalidate
    
    subgraph "Storage Backends"
        Memory[In-Memory Store]
        Disk[Disk Storage]
        Redis[Redis Cluster]
        Memcached[Memcached]
    end
    
    L1 --> Memory
    L2 --> Disk
    L3 --> Redis
    L3 --> Memcached
    
    subgraph "Cache Analytics"
        HitRate[Hit Rate Metrics]
        Performance[Performance Stats]
        Usage[Usage Patterns]
        Optimization[Auto Optimization]
    end
    
    CM --> HitRate
    CM --> Performance
    CM --> Usage
    Usage --> Optimization
    
    subgraph "Invalidation"
        TimeBasedInv[Time-based]
        EventBasedInv[Event-based]
        ManualInv[Manual]
        TagBasedInv[Tag-based]
    end
    
    Invalidate --> TimeBasedInv
    Invalidate --> EventBasedInv
    Invalidate --> ManualInv
    Invalidate --> TagBasedInv
```

## Caching Implementation

### Design Decisions

**Why multi-tier caching?**
- **Performance**: Different access patterns benefit from different cache types
- **Capacity**: Balance between speed and storage capacity
- **Cost**: Optimize cost per byte across different storage tiers
- **Reliability**: Multiple fallback options for cache misses

**Why intelligent cache invalidation?**
- **Consistency**: Ensure cached data remains accurate and up-to-date
- **Efficiency**: Minimize unnecessary cache invalidations
- **Flexibility**: Support different invalidation strategies per use case
- **Performance**: Maintain high cache hit rates while ensuring freshness

Create `src/cache/mod.rs`:

```rust
//! Comprehensive caching and storage system for load balancers
//!
//! This module provides advanced caching capabilities including:
//! - Multi-tier caching (memory, disk, distributed)
//! - Intelligent cache invalidation and consistency
//! - Cache analytics and performance optimization
//! - Configurable eviction policies and storage backends
//! - Cache warming and preloading strategies

pub mod manager;
pub mod storage;
pub mod policies;
pub mod invalidation;
pub mod analytics;

use crate::error::{LoadBalancerError, Result};
use crate::metrics::LoadBalancerMetrics;
use serde::{Serialize, Deserialize};
use std::collections::{HashMap, BTreeMap};
use std::hash::{Hash, Hasher};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::RwLock;
use tracing::{info, warn, debug, trace};

/// Cache manager coordinating multiple cache tiers
pub struct CacheManager {
    /// Cache configuration
    config: CacheConfig,
    
    /// L1 cache (in-memory, hot data)
    l1_cache: Arc<dyn CacheStorage + Send + Sync>,
    
    /// L2 cache (local disk, warm data)
    l2_cache: Option<Arc<dyn CacheStorage + Send + Sync>>,
    
    /// L3 cache (distributed, cold data)
    l3_cache: Option<Arc<dyn CacheStorage + Send + Sync>>,
    
    /// Cache analytics
    analytics: Arc<CacheAnalytics>,
    
    /// Invalidation manager
    invalidation_manager: Arc<InvalidationManager>,
    
    /// Metrics collector
    metrics: Option<Arc<LoadBalancerMetrics>>,
}

/// Cache configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    /// Enable caching
    pub enabled: bool,
    
    /// L1 cache configuration
    pub l1_config: CacheLayerConfig,
    
    /// L2 cache configuration
    pub l2_config: Option<CacheLayerConfig>,
    
    /// L3 cache configuration
    pub l3_config: Option<CacheLayerConfig>,
    
    /// Default TTL for cached items
    pub default_ttl: Duration,
    
    /// Maximum cache key size
    pub max_key_size: usize,
    
    /// Maximum cache value size
    pub max_value_size: usize,
    
    /// Enable cache compression
    pub enable_compression: bool,
    
    /// Compression threshold (compress values larger than this)
    pub compression_threshold: usize,
    
    /// Enable cache warming
    pub enable_warming: bool,
    
    /// Cache warming patterns
    pub warming_patterns: Vec<String>,
    
    /// Enable cache analytics
    pub enable_analytics: bool,
    
    /// Analytics collection interval
    pub analytics_interval: Duration,
    
    /// Enable automatic optimization
    pub enable_auto_optimization: bool,
    
    /// Optimization interval
    pub optimization_interval: Duration,
}

/// Configuration for a cache layer
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheLayerConfig {
    /// Cache storage type
    pub storage_type: CacheStorageType,
    
    /// Maximum cache size (in bytes)
    pub max_size: u64,
    
    /// Maximum number of entries
    pub max_entries: usize,
    
    /// Eviction policy
    pub eviction_policy: EvictionPolicy,
    
    /// Default TTL for this layer
    pub default_ttl: Duration,
    
    /// Storage-specific configuration
    pub storage_config: HashMap<String, String>,
}

/// Cache storage types
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum CacheStorageType {
    Memory,
    Disk,
    Redis,
    Memcached,
}

/// Cache eviction policies
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum EvictionPolicy {
    /// Least Recently Used
    LRU,
    
    /// Least Frequently Used
    LFU,
    
    /// First In, First Out
    FIFO,
    
    /// Time-based expiration only
    TTL,
    
    /// Random eviction
    Random,
    
    /// Priority-based eviction
    Priority,
}

/// Cache entry with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry {
    /// Cache key
    pub key: String,
    
    /// Cached value
    pub value: Vec<u8>,
    
    /// Entry creation time
    pub created_at: SystemTime,
    
    /// Last access time
    pub last_accessed: SystemTime,
    
    /// Access count
    pub access_count: u64,
    
    /// Time-to-live
    pub ttl: Duration,
    
    /// Entry priority
    pub priority: u8,
    
    /// Entry tags for invalidation
    pub tags: Vec<String>,
    
    /// Entry size in bytes
    pub size: usize,
    
    /// Whether entry is compressed
    pub compressed: bool,
    
    /// Entry metadata
    pub metadata: HashMap<String, String>,
}

/// Cache operation result
#[derive(Debug, Clone)]
pub struct CacheResult {
    /// Whether the operation was successful
    pub success: bool,
    
    /// Cache hit/miss information
    pub hit_info: CacheHitInfo,
    
    /// Retrieved value (for get operations)
    pub value: Option<Vec<u8>>,
    
    /// Operation duration
    pub duration: Duration,
    
    /// Error message if operation failed
    pub error: Option<String>,
}

/// Cache hit information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheHitInfo {
    /// Whether it was a cache hit
    pub hit: bool,
    
    /// Which cache layer served the request
    pub layer: Option<CacheLayer>,
    
    /// Cache key
    pub key: String,
    
    /// Entry age
    pub age: Duration,
    
    /// Entry size
    pub size: usize,
}

/// Cache layers
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum CacheLayer {
    L1,
    L2,
    L3,
}

impl CacheManager {
    /// Create a new cache manager
    pub async fn new(config: CacheConfig) -> Result<Self> {
        // Create L1 cache (always present)
        let l1_cache = Self::create_cache_storage(&config.l1_config).await?;
        
        // Create L2 cache if configured
        let l2_cache = if let Some(ref l2_config) = config.l2_config {
            Some(Self::create_cache_storage(l2_config).await?)
        } else {
            None
        };
        
        // Create L3 cache if configured
        let l3_cache = if let Some(ref l3_config) = config.l3_config {
            Some(Self::create_cache_storage(l3_config).await?)
        } else {
            None
        };
        
        let analytics = Arc::new(CacheAnalytics::new(config.enable_analytics));
        let invalidation_manager = Arc::new(InvalidationManager::new());
        
        info!("💾 Cache manager initialized with {} layers", 
              1 + l2_cache.is_some() as usize + l3_cache.is_some() as usize);
        
        Ok(Self {
            config,
            l1_cache,
            l2_cache,
            l3_cache,
            analytics,
            invalidation_manager,
            metrics: None,
        })
    }
    
    /// Set metrics collector
    pub fn with_metrics(mut self, metrics: Arc<LoadBalancerMetrics>) -> Self {
        self.metrics = Some(metrics);
        self
    }
    
    /// Start cache management services
    pub async fn start(&self) -> Result<()> {
        if !self.config.enabled {
            info!("Caching is disabled");
            return Ok(());
        }
        
        // Start analytics collection
        if self.config.enable_analytics {
            self.start_analytics_collection().await;
        }
        
        // Start cache warming
        if self.config.enable_warming {
            self.start_cache_warming().await;
        }
        
        // Start auto-optimization
        if self.config.enable_auto_optimization {
            self.start_auto_optimization().await;
        }
        
        // Start invalidation manager
        self.invalidation_manager.start().await?;
        
        info!("✅ Cache management services started");
        Ok(())
    }
    
    /// Get value from cache
    pub async fn get(&self, key: &str) -> Result<CacheResult> {
        if !self.config.enabled {
            return Ok(CacheResult {
                success: false,
                hit_info: CacheHitInfo {
                    hit: false,
                    layer: None,
                    key: key.to_string(),
                    age: Duration::from_secs(0),
                    size: 0,
                },
                value: None,
                duration: Duration::from_secs(0),
                error: Some("Caching disabled".to_string()),
            });
        }
        
        let start_time = Instant::now();
        
        // Validate key
        if key.len() > self.config.max_key_size {
            return Err(LoadBalancerError::Cache {
                message: "Cache key too large".to_string(),
                key: Some(key.to_string()),
                operation: "get".to_string(),
                layer: None,
            });
        }
        
        // Try L1 cache first
        if let Some(entry) = self.l1_cache.get(key).await? {
            if !self.is_expired(&entry) {
                self.update_access_stats(&entry).await;
                
                let result = CacheResult {
                    success: true,
                    hit_info: CacheHitInfo {
                        hit: true,
                        layer: Some(CacheLayer::L1),
                        key: key.to_string(),
                        age: SystemTime::now().duration_since(entry.created_at).unwrap_or_default(),
                        size: entry.size,
                    },
                    value: Some(self.decompress_if_needed(&entry)?),
                    duration: start_time.elapsed(),
                    error: None,
                };
                
                self.analytics.record_hit(CacheLayer::L1, &result).await;
                return Ok(result);
            } else {
                // Remove expired entry
                self.l1_cache.delete(key).await?;
            }
        }
        
        // Try L2 cache
        if let Some(ref l2_cache) = self.l2_cache {
            if let Some(entry) = l2_cache.get(key).await? {
                if !self.is_expired(&entry) {
                    // Promote to L1 cache
                    self.l1_cache.set(entry.clone()).await?;
                    self.update_access_stats(&entry).await;
                    
                    let result = CacheResult {
                        success: true,
                        hit_info: CacheHitInfo {
                            hit: true,
                            layer: Some(CacheLayer::L2),
                            key: key.to_string(),
                            age: SystemTime::now().duration_since(entry.created_at).unwrap_or_default(),
                            size: entry.size,
                        },
                        value: Some(self.decompress_if_needed(&entry)?),
                        duration: start_time.elapsed(),
                        error: None,
                    };
                    
                    self.analytics.record_hit(CacheLayer::L2, &result).await;
                    return Ok(result);
                } else {
                    // Remove expired entry
                    l2_cache.delete(key).await?;
                }
            }
        }
        
        // Try L3 cache
        if let Some(ref l3_cache) = self.l3_cache {
            if let Some(entry) = l3_cache.get(key).await? {
                if !self.is_expired(&entry) {
                    // Promote to L1 and L2 caches
                    self.l1_cache.set(entry.clone()).await?;
                    if let Some(ref l2_cache) = self.l2_cache {
                        l2_cache.set(entry.clone()).await?;
                    }
                    self.update_access_stats(&entry).await;
                    
                    let result = CacheResult {
                        success: true,
                        hit_info: CacheHitInfo {
                            hit: true,
                            layer: Some(CacheLayer::L3),
                            key: key.to_string(),
                            age: SystemTime::now().duration_since(entry.created_at).unwrap_or_default(),
                            size: entry.size,
                        },
                        value: Some(self.decompress_if_needed(&entry)?),
                        duration: start_time.elapsed(),
                        error: None,
                    };
                    
                    self.analytics.record_hit(CacheLayer::L3, &result).await;
                    return Ok(result);
                } else {
                    // Remove expired entry
                    l3_cache.delete(key).await?;
                }
            }
        }
        
        // Cache miss
        let result = CacheResult {
            success: false,
            hit_info: CacheHitInfo {
                hit: false,
                layer: None,
                key: key.to_string(),
                age: Duration::from_secs(0),
                size: 0,
            },
            value: None,
            duration: start_time.elapsed(),
            error: None,
        };
        
        self.analytics.record_miss(&result).await;
        Ok(result)
    }
    
    /// Set value in cache
    pub async fn set(&self, key: String, value: Vec<u8>, ttl: Option<Duration>, tags: Vec<String>) -> Result<CacheResult> {
        if !self.config.enabled {
            return Ok(CacheResult {
                success: false,
                hit_info: CacheHitInfo {
                    hit: false,
                    layer: None,
                    key,
                    age: Duration::from_secs(0),
                    size: 0,
                },
                value: None,
                duration: Duration::from_secs(0),
                error: Some("Caching disabled".to_string()),
            });
        }
        
        let start_time = Instant::now();
        
        // Validate key and value sizes
        if key.len() > self.config.max_key_size {
            return Err(LoadBalancerError::Cache {
                message: "Cache key too large".to_string(),
                key: Some(key),
                operation: "set".to_string(),
                layer: None,
            });
        }
        
        if value.len() > self.config.max_value_size {
            return Err(LoadBalancerError::Cache {
                message: "Cache value too large".to_string(),
                key: Some(key),
                operation: "set".to_string(),
                layer: None,
            });
        }
        
        // Compress value if needed
        let (final_value, compressed) = self.compress_if_needed(&value)?;
        
        let entry = CacheEntry {
            key: key.clone(),
            value: final_value,
            created_at: SystemTime::now(),
            last_accessed: SystemTime::now(),
            access_count: 0,
            ttl: ttl.unwrap_or(self.config.default_ttl),
            priority: 128, // Default priority
            tags,
            size: value.len(), // Original size
            compressed,
            metadata: HashMap::new(),
        };
        
        // Store in all cache layers
        self.l1_cache.set(entry.clone()).await?;
        
        if let Some(ref l2_cache) = self.l2_cache {
            l2_cache.set(entry.clone()).await?;
        }
        
        if let Some(ref l3_cache) = self.l3_cache {
            l3_cache.set(entry.clone()).await?;
        }
        
        let result = CacheResult {
            success: true,
            hit_info: CacheHitInfo {
                hit: false, // This is a set operation
                layer: Some(CacheLayer::L1),
                key: key.clone(),
                age: Duration::from_secs(0),
                size: entry.size,
            },
            value: None,
            duration: start_time.elapsed(),
            error: None,
        };
        
        self.analytics.record_set(&result).await;
        
        // Record metrics
        if let Some(ref metrics) = self.metrics {
            metrics.record_cache_operation("set", true, start_time.elapsed());
        }
        
        Ok(result)
    }
    
    /// Delete value from cache
    pub async fn delete(&self, key: &str) -> Result<CacheResult> {
        let start_time = Instant::now();
        
        // Delete from all cache layers
        let mut deleted = false;
        
        if self.l1_cache.delete(key).await.is_ok() {
            deleted = true;
        }
        
        if let Some(ref l2_cache) = self.l2_cache {
            if l2_cache.delete(key).await.is_ok() {
                deleted = true;
            }
        }
        
        if let Some(ref l3_cache) = self.l3_cache {
            if l3_cache.delete(key).await.is_ok() {
                deleted = true;
            }
        }
        
        let result = CacheResult {
            success: deleted,
            hit_info: CacheHitInfo {
                hit: deleted,
                layer: None,
                key: key.to_string(),
                age: Duration::from_secs(0),
                size: 0,
            },
            value: None,
            duration: start_time.elapsed(),
            error: if deleted { None } else { Some("Key not found".to_string()) },
        };
        
        self.analytics.record_delete(&result).await;
        Ok(result)
    }
    
    /// Invalidate cache entries by tags
    pub async fn invalidate_by_tags(&self, tags: &[String]) -> Result<u64> {
        self.invalidation_manager.invalidate_by_tags(
            tags,
            &[&*self.l1_cache, self.l2_cache.as_deref(), self.l3_cache.as_deref()]
                .into_iter()
                .flatten()
                .collect::<Vec<_>>()
        ).await
    }
    
    /// Get cache statistics
    pub async fn get_cache_stats(&self) -> CacheStats {
        self.analytics.get_stats().await
    }
    
    /// Create cache storage based on configuration
    async fn create_cache_storage(config: &CacheLayerConfig) -> Result<Arc<dyn CacheStorage + Send + Sync>> {
        match config.storage_type {
            CacheStorageType::Memory => {
                Ok(Arc::new(MemoryCacheStorage::new(config.clone())))
            }
            CacheStorageType::Disk => {
                Ok(Arc::new(DiskCacheStorage::new(config.clone()).await?))
            }
            CacheStorageType::Redis => {
                Ok(Arc::new(RedisCacheStorage::new(config.clone()).await?))
            }
            CacheStorageType::Memcached => {
                Ok(Arc::new(MemcachedCacheStorage::new(config.clone()).await?))
            }
        }
    }
    
    /// Check if cache entry is expired
    fn is_expired(&self, entry: &CacheEntry) -> bool {
        SystemTime::now()
            .duration_since(entry.created_at)
            .unwrap_or_default() > entry.ttl
    }
    
    /// Compress value if compression is enabled and value is large enough
    fn compress_if_needed(&self, value: &[u8]) -> Result<(Vec<u8>, bool)> {
        if self.config.enable_compression && value.len() > self.config.compression_threshold {
            // In a real implementation, you'd use a compression library like zstd or lz4
            // For now, we'll just return the original value
            Ok((value.to_vec(), false))
        } else {
            Ok((value.to_vec(), false))
        }
    }
    
    /// Decompress value if it was compressed
    fn decompress_if_needed(&self, entry: &CacheEntry) -> Result<Vec<u8>> {
        if entry.compressed {
            // In a real implementation, you'd decompress the value
            // For now, we'll just return the original value
            Ok(entry.value.clone())
        } else {
            Ok(entry.value.clone())
        }
    }
    
    /// Update access statistics for cache entry
    async fn update_access_stats(&self, entry: &CacheEntry) {
        // In a real implementation, you'd update the entry's access statistics
        trace!("Updated access stats for cache key: {}", entry.key);
    }
    
    /// Start analytics collection
    async fn start_analytics_collection(&self) {
        let analytics = self.analytics.clone();
        let interval = self.config.analytics_interval;
        
        tokio::spawn(async move {
            let mut collection_interval = tokio::time::interval(interval);
            
            loop {
                collection_interval.tick().await;
                analytics.collect_periodic_stats().await;
            }
        });
    }
    
    /// Start cache warming
    async fn start_cache_warming(&self) {
        info!("🔥 Starting cache warming");
        
        // In a real implementation, you'd implement cache warming logic
        // based on the warming patterns in the configuration
    }
    
    /// Start auto-optimization
    async fn start_auto_optimization(&self) {
        let analytics = self.analytics.clone();
        let interval = self.config.optimization_interval;
        
        tokio::spawn(async move {
            let mut optimization_interval = tokio::time::interval(interval);
            
            loop {
                optimization_interval.tick().await;
                
                // Analyze cache performance and optimize
                let stats = analytics.get_stats().await;
                
                // In a real implementation, you'd implement optimization logic
                // based on cache hit rates, eviction patterns, etc.
                debug!("Cache optimization check - Hit rate: {:.2}%", 
                       stats.overall_hit_rate * 100.0);
            }
        });
    }
}

/// Trait for cache storage backends
#[async_trait::async_trait]
pub trait CacheStorage {
    /// Get cache entry by key
    async fn get(&self, key: &str) -> Result<Option<CacheEntry>>;

    /// Set cache entry
    async fn set(&self, entry: CacheEntry) -> Result<()>;

    /// Delete cache entry by key
    async fn delete(&self, key: &str) -> Result<()>;

    /// Get all keys matching a pattern
    async fn keys(&self, pattern: &str) -> Result<Vec<String>>;

    /// Clear all cache entries
    async fn clear(&self) -> Result<()>;

    /// Get storage statistics
    async fn stats(&self) -> Result<StorageStats>;
}

/// Storage statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageStats {
    /// Total number of entries
    pub entry_count: usize,

    /// Total size in bytes
    pub total_size: u64,

    /// Available space
    pub available_space: u64,

    /// Hit count
    pub hit_count: u64,

    /// Miss count
    pub miss_count: u64,

    /// Eviction count
    pub eviction_count: u64,
}

/// In-memory cache storage
pub struct MemoryCacheStorage {
    /// Cache entries
    entries: Arc<RwLock<HashMap<String, CacheEntry>>>,

    /// LRU tracking
    lru_order: Arc<RwLock<BTreeMap<SystemTime, String>>>,

    /// Configuration
    config: CacheLayerConfig,

    /// Statistics
    stats: Arc<RwLock<StorageStats>>,
}

impl MemoryCacheStorage {
    pub fn new(config: CacheLayerConfig) -> Self {
        Self {
            entries: Arc::new(RwLock::new(HashMap::new())),
            lru_order: Arc::new(RwLock::new(BTreeMap::new())),
            config,
            stats: Arc::new(RwLock::new(StorageStats::default())),
        }
    }

    async fn evict_if_needed(&self) -> Result<()> {
        let entries_count = self.entries.read().await.len();

        if entries_count >= self.config.max_entries {
            match self.config.eviction_policy {
                EvictionPolicy::LRU => self.evict_lru().await?,
                EvictionPolicy::LFU => self.evict_lfu().await?,
                EvictionPolicy::FIFO => self.evict_fifo().await?,
                EvictionPolicy::Random => self.evict_random().await?,
                _ => {} // TTL and Priority handled elsewhere
            }
        }

        Ok(())
    }

    async fn evict_lru(&self) -> Result<()> {
        let mut lru_order = self.lru_order.write().await;
        let mut entries = self.entries.write().await;

        if let Some((_, key)) = lru_order.iter().next() {
            let key = key.clone();
            entries.remove(&key);
            lru_order.remove(&lru_order.iter().next().unwrap().0);

            let mut stats = self.stats.write().await;
            stats.eviction_count += 1;
        }

        Ok(())
    }

    async fn evict_lfu(&self) -> Result<()> {
        let mut entries = self.entries.write().await;

        // Find entry with lowest access count
        let mut min_access_count = u64::MAX;
        let mut key_to_evict = None;

        for (key, entry) in entries.iter() {
            if entry.access_count < min_access_count {
                min_access_count = entry.access_count;
                key_to_evict = Some(key.clone());
            }
        }

        if let Some(key) = key_to_evict {
            entries.remove(&key);

            let mut stats = self.stats.write().await;
            stats.eviction_count += 1;
        }

        Ok(())
    }

    async fn evict_fifo(&self) -> Result<()> {
        let mut entries = self.entries.write().await;

        // Find oldest entry
        let mut oldest_time = SystemTime::now();
        let mut key_to_evict = None;

        for (key, entry) in entries.iter() {
            if entry.created_at < oldest_time {
                oldest_time = entry.created_at;
                key_to_evict = Some(key.clone());
            }
        }

        if let Some(key) = key_to_evict {
            entries.remove(&key);

            let mut stats = self.stats.write().await;
            stats.eviction_count += 1;
        }

        Ok(())
    }

    async fn evict_random(&self) -> Result<()> {
        let mut entries = self.entries.write().await;

        if let Some(key) = entries.keys().next().cloned() {
            entries.remove(&key);

            let mut stats = self.stats.write().await;
            stats.eviction_count += 1;
        }

        Ok(())
    }
}

#[async_trait::async_trait]
impl CacheStorage for MemoryCacheStorage {
    async fn get(&self, key: &str) -> Result<Option<CacheEntry>> {
        let entries = self.entries.read().await;

        if let Some(entry) = entries.get(key) {
            let mut stats = self.stats.write().await;
            stats.hit_count += 1;
            Ok(Some(entry.clone()))
        } else {
            let mut stats = self.stats.write().await;
            stats.miss_count += 1;
            Ok(None)
        }
    }

    async fn set(&self, entry: CacheEntry) -> Result<()> {
        self.evict_if_needed().await?;

        let mut entries = self.entries.write().await;
        let mut lru_order = self.lru_order.write().await;

        // Update LRU tracking
        lru_order.insert(entry.last_accessed, entry.key.clone());

        entries.insert(entry.key.clone(), entry);

        let mut stats = self.stats.write().await;
        stats.entry_count = entries.len();

        Ok(())
    }

    async fn delete(&self, key: &str) -> Result<()> {
        let mut entries = self.entries.write().await;

        if entries.remove(key).is_some() {
            let mut stats = self.stats.write().await;
            stats.entry_count = entries.len();
            Ok(())
        } else {
            Err(LoadBalancerError::Cache {
                message: "Key not found".to_string(),
                key: Some(key.to_string()),
                operation: "delete".to_string(),
                layer: None,
            })
        }
    }

    async fn keys(&self, pattern: &str) -> Result<Vec<String>> {
        let entries = self.entries.read().await;

        // Simple pattern matching (in production, use regex)
        let keys: Vec<String> = entries.keys()
            .filter(|key| key.contains(pattern))
            .cloned()
            .collect();

        Ok(keys)
    }

    async fn clear(&self) -> Result<()> {
        let mut entries = self.entries.write().await;
        let mut lru_order = self.lru_order.write().await;

        entries.clear();
        lru_order.clear();

        let mut stats = self.stats.write().await;
        stats.entry_count = 0;
        stats.total_size = 0;

        Ok(())
    }

    async fn stats(&self) -> Result<StorageStats> {
        Ok(self.stats.read().await.clone())
    }
}

/// Disk-based cache storage
pub struct DiskCacheStorage {
    /// Base directory for cache files
    base_dir: std::path::PathBuf,

    /// Configuration
    config: CacheLayerConfig,

    /// Statistics
    stats: Arc<RwLock<StorageStats>>,
}

impl DiskCacheStorage {
    pub async fn new(config: CacheLayerConfig) -> Result<Self> {
        let base_dir = std::path::PathBuf::from(
            config.storage_config.get("base_dir")
                .unwrap_or(&"/tmp/rusty_balancer_cache".to_string())
        );

        // Create cache directory if it doesn't exist
        tokio::fs::create_dir_all(&base_dir).await
            .map_err(|e| LoadBalancerError::Storage {
                message: format!("Failed to create cache directory: {}", e),
                path: Some(base_dir.to_string_lossy().to_string()),
                operation: "create_dir".to_string(),
            })?;

        Ok(Self {
            base_dir,
            config,
            stats: Arc::new(RwLock::new(StorageStats::default())),
        })
    }

    fn key_to_path(&self, key: &str) -> std::path::PathBuf {
        // Simple key-to-path mapping (in production, use proper hashing)
        let mut hasher = std::collections::hash_map::DefaultHasher::new();
        key.hash(&mut hasher);
        let hash = hasher.finish();

        self.base_dir.join(format!("{:x}.cache", hash))
    }
}

#[async_trait::async_trait]
impl CacheStorage for DiskCacheStorage {
    async fn get(&self, key: &str) -> Result<Option<CacheEntry>> {
        let path = self.key_to_path(key);

        match tokio::fs::read(&path).await {
            Ok(data) => {
                // In a real implementation, you'd deserialize the cache entry
                // For now, we'll return None to indicate miss
                let mut stats = self.stats.write().await;
                stats.miss_count += 1;
                Ok(None)
            }
            Err(_) => {
                let mut stats = self.stats.write().await;
                stats.miss_count += 1;
                Ok(None)
            }
        }
    }

    async fn set(&self, entry: CacheEntry) -> Result<()> {
        let path = self.key_to_path(&entry.key);

        // In a real implementation, you'd serialize the cache entry
        // For now, we'll just write the value
        tokio::fs::write(&path, &entry.value).await
            .map_err(|e| LoadBalancerError::Storage {
                message: format!("Failed to write cache entry: {}", e),
                path: Some(path.to_string_lossy().to_string()),
                operation: "write".to_string(),
            })?;

        let mut stats = self.stats.write().await;
        stats.entry_count += 1;
        stats.total_size += entry.size as u64;

        Ok(())
    }

    async fn delete(&self, key: &str) -> Result<()> {
        let path = self.key_to_path(key);

        tokio::fs::remove_file(&path).await
            .map_err(|e| LoadBalancerError::Storage {
                message: format!("Failed to delete cache entry: {}", e),
                path: Some(path.to_string_lossy().to_string()),
                operation: "delete".to_string(),
            })?;

        let mut stats = self.stats.write().await;
        stats.entry_count = stats.entry_count.saturating_sub(1);

        Ok(())
    }

    async fn keys(&self, _pattern: &str) -> Result<Vec<String>> {
        // In a real implementation, you'd scan the directory and match patterns
        Ok(Vec::new())
    }

    async fn clear(&self) -> Result<()> {
        // Remove all cache files
        let mut entries = tokio::fs::read_dir(&self.base_dir).await
            .map_err(|e| LoadBalancerError::Storage {
                message: format!("Failed to read cache directory: {}", e),
                path: Some(self.base_dir.to_string_lossy().to_string()),
                operation: "read_dir".to_string(),
            })?;

        while let Some(entry) = entries.next_entry().await.map_err(|e| LoadBalancerError::Storage {
            message: format!("Failed to read directory entry: {}", e),
            path: Some(self.base_dir.to_string_lossy().to_string()),
            operation: "read_entry".to_string(),
        })? {
            if entry.path().extension().and_then(|s| s.to_str()) == Some("cache") {
                let _ = tokio::fs::remove_file(entry.path()).await;
            }
        }

        let mut stats = self.stats.write().await;
        stats.entry_count = 0;
        stats.total_size = 0;

        Ok(())
    }

    async fn stats(&self) -> Result<StorageStats> {
        Ok(self.stats.read().await.clone())
    }
}

/// Redis-based cache storage
pub struct RedisCacheStorage {
    /// Redis connection URL
    redis_url: String,

    /// Configuration
    config: CacheLayerConfig,

    /// Statistics
    stats: Arc<RwLock<StorageStats>>,
}

impl RedisCacheStorage {
    pub async fn new(config: CacheLayerConfig) -> Result<Self> {
        let redis_url = config.storage_config.get("redis_url")
            .unwrap_or(&"redis://localhost:6379".to_string())
            .clone();

        // In a real implementation, you'd establish Redis connection here
        info!("🔗 Connecting to Redis cache: {}", redis_url);

        Ok(Self {
            redis_url,
            config,
            stats: Arc::new(RwLock::new(StorageStats::default())),
        })
    }
}

#[async_trait::async_trait]
impl CacheStorage for RedisCacheStorage {
    async fn get(&self, _key: &str) -> Result<Option<CacheEntry>> {
        // Redis implementation would go here
        let mut stats = self.stats.write().await;
        stats.miss_count += 1;
        Ok(None)
    }

    async fn set(&self, _entry: CacheEntry) -> Result<()> {
        // Redis implementation would go here
        Ok(())
    }

    async fn delete(&self, _key: &str) -> Result<()> {
        // Redis implementation would go here
        Ok(())
    }

    async fn keys(&self, _pattern: &str) -> Result<Vec<String>> {
        // Redis implementation would go here
        Ok(Vec::new())
    }

    async fn clear(&self) -> Result<()> {
        // Redis implementation would go here
        Ok(())
    }

    async fn stats(&self) -> Result<StorageStats> {
        Ok(self.stats.read().await.clone())
    }
}

/// Memcached-based cache storage
pub struct MemcachedCacheStorage {
    /// Memcached servers
    servers: Vec<String>,

    /// Configuration
    config: CacheLayerConfig,

    /// Statistics
    stats: Arc<RwLock<StorageStats>>,
}

impl MemcachedCacheStorage {
    pub async fn new(config: CacheLayerConfig) -> Result<Self> {
        let servers_str = config.storage_config.get("servers")
            .unwrap_or(&"localhost:11211".to_string());

        let servers: Vec<String> = servers_str
            .split(',')
            .map(|s| s.trim().to_string())
            .collect();

        // In a real implementation, you'd establish Memcached connections here
        info!("🔗 Connecting to Memcached servers: {:?}", servers);

        Ok(Self {
            servers,
            config,
            stats: Arc::new(RwLock::new(StorageStats::default())),
        })
    }
}

#[async_trait::async_trait]
impl CacheStorage for MemcachedCacheStorage {
    async fn get(&self, _key: &str) -> Result<Option<CacheEntry>> {
        // Memcached implementation would go here
        let mut stats = self.stats.write().await;
        stats.miss_count += 1;
        Ok(None)
    }

    async fn set(&self, _entry: CacheEntry) -> Result<()> {
        // Memcached implementation would go here
        Ok(())
    }

    async fn delete(&self, _key: &str) -> Result<()> {
        // Memcached implementation would go here
        Ok(())
    }

    async fn keys(&self, _pattern: &str) -> Result<Vec<String>> {
        // Memcached implementation would go here
        Ok(Vec::new())
    }

    async fn clear(&self) -> Result<()> {
        // Memcached implementation would go here
        Ok(())
    }

    async fn stats(&self) -> Result<StorageStats> {
        Ok(self.stats.read().await.clone())
    }
}

/// Cache analytics for performance monitoring
pub struct CacheAnalytics {
    /// Whether analytics is enabled
    enabled: bool,

    /// Analytics data
    data: Arc<RwLock<CacheAnalyticsData>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheAnalyticsData {
    /// Hit counts per layer
    pub hits_by_layer: HashMap<CacheLayer, u64>,

    /// Miss count
    pub misses: u64,

    /// Set operations count
    pub sets: u64,

    /// Delete operations count
    pub deletes: u64,

    /// Total response time
    pub total_response_time: Duration,

    /// Operation count
    pub operation_count: u64,

    /// Cache size by layer
    pub size_by_layer: HashMap<CacheLayer, u64>,

    /// Last updated
    pub last_updated: SystemTime,
}

impl CacheAnalytics {
    pub fn new(enabled: bool) -> Self {
        Self {
            enabled,
            data: Arc::new(RwLock::new(CacheAnalyticsData::default())),
        }
    }

    pub async fn record_hit(&self, layer: CacheLayer, result: &CacheResult) {
        if !self.enabled {
            return;
        }

        let mut data = self.data.write().await;
        *data.hits_by_layer.entry(layer).or_insert(0) += 1;
        data.total_response_time += result.duration;
        data.operation_count += 1;
        data.last_updated = SystemTime::now();
    }

    pub async fn record_miss(&self, result: &CacheResult) {
        if !self.enabled {
            return;
        }

        let mut data = self.data.write().await;
        data.misses += 1;
        data.total_response_time += result.duration;
        data.operation_count += 1;
        data.last_updated = SystemTime::now();
    }

    pub async fn record_set(&self, result: &CacheResult) {
        if !self.enabled {
            return;
        }

        let mut data = self.data.write().await;
        data.sets += 1;
        data.total_response_time += result.duration;
        data.operation_count += 1;
        data.last_updated = SystemTime::now();
    }

    pub async fn record_delete(&self, result: &CacheResult) {
        if !self.enabled {
            return;
        }

        let mut data = self.data.write().await;
        data.deletes += 1;
        data.total_response_time += result.duration;
        data.operation_count += 1;
        data.last_updated = SystemTime::now();
    }

    pub async fn get_stats(&self) -> CacheStats {
        let data = self.data.read().await;

        let total_hits: u64 = data.hits_by_layer.values().sum();
        let total_operations = total_hits + data.misses;

        let overall_hit_rate = if total_operations > 0 {
            total_hits as f64 / total_operations as f64
        } else {
            0.0
        };

        let avg_response_time = if data.operation_count > 0 {
            data.total_response_time / data.operation_count as u32
        } else {
            Duration::from_secs(0)
        };

        CacheStats {
            overall_hit_rate,
            hits_by_layer: data.hits_by_layer.clone(),
            total_misses: data.misses,
            total_sets: data.sets,
            total_deletes: data.deletes,
            avg_response_time,
            total_operations: data.operation_count,
            size_by_layer: data.size_by_layer.clone(),
            last_updated: data.last_updated,
        }
    }

    pub async fn collect_periodic_stats(&self) {
        if !self.enabled {
            return;
        }

        // Collect periodic statistics
        debug!("📊 Collecting cache analytics");
    }
}

/// Cache invalidation manager
pub struct InvalidationManager {
    /// Tag-based invalidation tracking
    tag_tracking: Arc<RwLock<HashMap<String, Vec<String>>>>,
}

impl InvalidationManager {
    pub fn new() -> Self {
        Self {
            tag_tracking: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn start(&self) -> Result<()> {
        info!("🗑️ Cache invalidation manager started");
        Ok(())
    }

    pub async fn invalidate_by_tags(
        &self,
        tags: &[String],
        storages: &[&dyn CacheStorage],
    ) -> Result<u64> {
        let mut invalidated_count = 0;

        for tag in tags {
            let tag_tracking = self.tag_tracking.read().await;

            if let Some(keys) = tag_tracking.get(tag) {
                for key in keys {
                    for storage in storages {
                        if storage.delete(key).await.is_ok() {
                            invalidated_count += 1;
                        }
                    }
                }
            }
        }

        info!("🗑️ Invalidated {} cache entries for tags: {:?}", invalidated_count, tags);
        Ok(invalidated_count)
    }
}

/// Cache statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    /// Overall hit rate
    pub overall_hit_rate: f64,

    /// Hit counts by layer
    pub hits_by_layer: HashMap<CacheLayer, u64>,

    /// Total misses
    pub total_misses: u64,

    /// Total set operations
    pub total_sets: u64,

    /// Total delete operations
    pub total_deletes: u64,

    /// Average response time
    pub avg_response_time: Duration,

    /// Total operations
    pub total_operations: u64,

    /// Cache size by layer
    pub size_by_layer: HashMap<CacheLayer, u64>,

    /// Last updated
    pub last_updated: SystemTime,
}

impl Default for StorageStats {
    fn default() -> Self {
        Self {
            entry_count: 0,
            total_size: 0,
            available_space: u64::MAX,
            hit_count: 0,
            miss_count: 0,
            eviction_count: 0,
        }
    }
}

impl Default for CacheAnalyticsData {
    fn default() -> Self {
        Self {
            hits_by_layer: HashMap::new(),
            misses: 0,
            sets: 0,
            deletes: 0,
            total_response_time: Duration::from_secs(0),
            operation_count: 0,
            size_by_layer: HashMap::new(),
            last_updated: SystemTime::now(),
        }
    }
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            l1_config: CacheLayerConfig {
                storage_type: CacheStorageType::Memory,
                max_size: 100 * 1024 * 1024, // 100MB
                max_entries: 10000,
                eviction_policy: EvictionPolicy::LRU,
                default_ttl: Duration::from_secs(3600), // 1 hour
                storage_config: HashMap::new(),
            },
            l2_config: Some(CacheLayerConfig {
                storage_type: CacheStorageType::Disk,
                max_size: 1024 * 1024 * 1024, // 1GB
                max_entries: 100000,
                eviction_policy: EvictionPolicy::LRU,
                default_ttl: Duration::from_secs(86400), // 24 hours
                storage_config: HashMap::new(),
            }),
            l3_config: None,
            default_ttl: Duration::from_secs(3600),
            max_key_size: 1024,
            max_value_size: 10 * 1024 * 1024, // 10MB
            enable_compression: true,
            compression_threshold: 1024, // 1KB
            enable_warming: false,
            warming_patterns: Vec::new(),
            enable_analytics: true,
            analytics_interval: Duration::from_secs(60),
            enable_auto_optimization: false,
            optimization_interval: Duration::from_secs(300),
        }
    }
}
```

## Integration Example

Create `examples/caching_storage_example.rs`:

```rust
//! Example demonstrating multi-tier caching with analytics

use rusty_balancer::cache::{CacheManager, CacheConfig, CacheLayerConfig, CacheStorageType, EvictionPolicy};
use std::collections::HashMap;
use std::time::Duration;
use tracing::{info, debug};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("💾 Starting Caching and Storage Example");

    // Create cache configuration with multiple tiers
    let config = CacheConfig {
        enabled: true,
        l1_config: CacheLayerConfig {
            storage_type: CacheStorageType::Memory,
            max_size: 10 * 1024 * 1024, // 10MB
            max_entries: 1000,
            eviction_policy: EvictionPolicy::LRU,
            default_ttl: Duration::from_secs(300), // 5 minutes
            storage_config: HashMap::new(),
        },
        l2_config: Some(CacheLayerConfig {
            storage_type: CacheStorageType::Disk,
            max_size: 100 * 1024 * 1024, // 100MB
            max_entries: 10000,
            eviction_policy: EvictionPolicy::LRU,
            default_ttl: Duration::from_secs(3600), // 1 hour
            storage_config: {
                let mut config = HashMap::new();
                config.insert("base_dir".to_string(), "/tmp/cache_example".to_string());
                config
            },
        }),
        l3_config: None, // No distributed cache for this example
        default_ttl: Duration::from_secs(1800), // 30 minutes
        max_key_size: 512,
        max_value_size: 1024 * 1024, // 1MB
        enable_compression: true,
        compression_threshold: 1024, // 1KB
        enable_analytics: true,
        analytics_interval: Duration::from_secs(30),
        enable_auto_optimization: true,
        optimization_interval: Duration::from_secs(120),
        ..Default::default()
    };

    // Create cache manager
    let cache_manager = CacheManager::new(config).await?;
    cache_manager.start().await?;

    info!("✅ Cache manager started with multi-tier configuration");

    // Test cache operations
    info!("🧪 Testing cache operations");

    // Test 1: Basic cache operations
    info!("📝 Test 1: Basic cache operations");

    let test_data = vec![
        ("user:123", b"John Doe".to_vec()),
        ("user:456", b"Jane Smith".to_vec()),
        ("config:app", b"{'theme': 'dark', 'lang': 'en'}".to_vec()),
        ("session:abc123", b"{'user_id': 123, 'expires': 1234567890}".to_vec()),
    ];

    for (key, value) in &test_data {
        let result = cache_manager.set(
            key.to_string(),
            value.clone(),
            Some(Duration::from_secs(600)), // 10 minutes
            vec!["users".to_string(), "test".to_string()]
        ).await?;

        info!("✅ Set {}: success={}, duration={:?}", key, result.success, result.duration);
    }

    // Test 2: Cache retrieval and hit/miss patterns
    info!("📖 Test 2: Cache retrieval");

    for (key, expected_value) in &test_data {
        let result = cache_manager.get(key).await?;

        if result.success {
            info!("✅ Get {}: HIT from {:?}, size={} bytes, age={:?}",
                  key, result.hit_info.layer, result.hit_info.size, result.hit_info.age);

            if let Some(value) = result.value {
                assert_eq!(&value, expected_value, "Retrieved value should match stored value");
            }
        } else {
            info!("❌ Get {}: MISS", key);
        }
    }

    // Test 3: Cache miss scenario
    info!("🔍 Test 3: Cache miss scenario");

    let missing_keys = ["nonexistent:1", "missing:data", "not:found"];
    for key in &missing_keys {
        let result = cache_manager.get(key).await?;
        info!("🔍 Get {}: hit={}, layer={:?}", key, result.hit_info.hit, result.hit_info.layer);
    }

    // Test 4: Large value caching (compression test)
    info!("🗜️ Test 4: Large value caching");

    let large_value = vec![b'X'; 2048]; // 2KB value to trigger compression
    let result = cache_manager.set(
        "large:data".to_string(),
        large_value.clone(),
        Some(Duration::from_secs(300)),
        vec!["large".to_string()]
    ).await?;

    info!("✅ Set large value: success={}, duration={:?}", result.success, result.duration);

    let get_result = cache_manager.get("large:data").await?;
    if get_result.success {
        info!("✅ Retrieved large value: size={} bytes", get_result.hit_info.size);
        if let Some(retrieved_value) = get_result.value {
            assert_eq!(retrieved_value, large_value, "Large value should be retrieved correctly");
        }
    }

    // Test 5: Cache invalidation by tags
    info!("🗑️ Test 5: Cache invalidation by tags");

    let invalidated_count = cache_manager.invalidate_by_tags(&["users".to_string()]).await?;
    info!("🗑️ Invalidated {} entries with 'users' tag", invalidated_count);

    // Verify invalidation
    for (key, _) in &test_data[..2] { // First two entries had 'users' tag
        let result = cache_manager.get(key).await?;
        if result.success {
            info!("⚠️ Key {} still exists after invalidation", key);
        } else {
            info!("✅ Key {} successfully invalidated", key);
        }
    }

    // Test 6: Cache performance under load
    info!("⚡ Test 6: Performance test");

    let start_time = std::time::Instant::now();
    let mut operations = 0;

    for i in 0..100 {
        let key = format!("perf:test:{}", i);
        let value = format!("Performance test value {}", i).into_bytes();

        // Set operation
        let _result = cache_manager.set(
            key.clone(),
            value,
            Some(Duration::from_secs(60)),
            vec!["performance".to_string()]
        ).await?;
        operations += 1;

        // Get operation
        let _result = cache_manager.get(&key).await?;
        operations += 1;

        if i % 20 == 0 {
            debug!("Completed {} operations", operations);
        }
    }

    let elapsed = start_time.elapsed();
    let ops_per_sec = operations as f64 / elapsed.as_secs_f64();

    info!("⚡ Performance test completed: {} operations in {:?} ({:.0} ops/sec)",
          operations, elapsed, ops_per_sec);

    // Test 7: Cache statistics and analytics
    info!("📊 Test 7: Cache statistics");

    // Wait a moment for analytics to collect
    tokio::time::sleep(Duration::from_secs(2)).await;

    let stats = cache_manager.get_cache_stats().await;

    info!("📊 Cache Statistics:");
    info!("  Overall hit rate: {:.2}%", stats.overall_hit_rate * 100.0);
    info!("  Total operations: {}", stats.total_operations);
    info!("  Total misses: {}", stats.total_misses);
    info!("  Total sets: {}", stats.total_sets);
    info!("  Total deletes: {}", stats.total_deletes);
    info!("  Average response time: {:?}", stats.avg_response_time);

    for (layer, hits) in &stats.hits_by_layer {
        info!("  {:?} layer hits: {}", layer, hits);
    }

    // Test 8: Cache deletion
    info!("🗑️ Test 8: Cache deletion");

    let delete_keys = ["config:app", "session:abc123"];
    for key in &delete_keys {
        let result = cache_manager.delete(key).await?;
        info!("🗑️ Delete {}: success={}, duration={:?}", key, result.success, result.duration);
    }

    // Verify deletion
    for key in &delete_keys {
        let result = cache_manager.get(key).await?;
        if result.success {
            info!("⚠️ Key {} still exists after deletion", key);
        } else {
            info!("✅ Key {} successfully deleted", key);
        }
    }

    // Final statistics
    info!("📊 Final Cache Statistics:");
    let final_stats = cache_manager.get_cache_stats().await;
    info!("  Hit rate: {:.2}%", final_stats.overall_hit_rate * 100.0);
    info!("  Total operations: {}", final_stats.total_operations);
    info!("  Cache efficiency: {:.2} hits per operation",
          final_stats.hits_by_layer.values().sum::<u64>() as f64 / final_stats.total_operations as f64);

    info!("✅ Caching and storage example completed");

    Ok(())
}
```

## Key Design Decisions Explained

### 1. Multi-Tier Caching Architecture
**Decision**: Implement L1 (memory), L2 (disk), and L3 (distributed) cache tiers

**Rationale**:
- **Performance**: Different access patterns benefit from different cache types
- **Capacity**: Balance between speed and storage capacity across tiers
- **Cost Optimization**: Optimize cost per byte across different storage types
- **Reliability**: Multiple fallback options for cache misses and failures

### 2. Intelligent Cache Promotion
**Decision**: Automatically promote frequently accessed items to higher cache tiers

**Rationale**:
- **Performance**: Keep hot data in fastest cache tier
- **Efficiency**: Reduce access latency for popular content
- **Resource Utilization**: Optimize cache space usage across tiers
- **Adaptive Behavior**: Automatically adapt to changing access patterns

### 3. Comprehensive Cache Analytics
**Decision**: Track detailed metrics and performance statistics for optimization

**Rationale**:
- **Visibility**: Understand cache performance and usage patterns
- **Optimization**: Identify opportunities for cache tuning and improvement
- **Monitoring**: Detect cache performance issues and anomalies
- **Business Intelligence**: Provide insights for capacity planning and optimization

## Performance Considerations

1. **Cache Lookup Speed**: O(1) lookups across all cache tiers
2. **Memory Efficiency**: Efficient storage and eviction policies
3. **Compression**: Reduce memory usage for large values
4. **Batch Operations**: Optimize bulk cache operations

## Security Considerations

1. **Data Encryption**: Encrypt sensitive cached data
2. **Access Control**: Secure cache management interfaces
3. **Cache Poisoning**: Validate cached data integrity
4. **Resource Limits**: Prevent cache-based DoS attacks

## Navigation
- [Previous: Graceful Shutdown](18-graceful-shutdown.md)
- [Next: Advanced Health Checks](20-advanced-health-checks.md)
