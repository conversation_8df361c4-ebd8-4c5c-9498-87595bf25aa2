# Module 08: Configuration System

## Learning Objectives
- Design a flexible, hierarchical configuration system
- Implement configuration loading from multiple sources (files, environment, CLI)
- Create type-safe configuration with validation and defaults
- Support hot-reloading for dynamic configuration updates
- Understand configuration management best practices in production systems

## Why Configuration Management Matters

Configuration management is critical for production systems because:

1. **Environment Flexibility**: Different settings for dev/staging/production
2. **Runtime Adaptability**: Change behavior without code changes
3. **Security**: Separate sensitive data from code
4. **Operational Control**: Tune performance and features dynamically
5. **Compliance**: Audit trails and controlled changes

### Configuration Sources Priority

```mermaid
graph TD
    CLI[Command Line Args] --> ENV[Environment Variables]
    ENV --> FILE[Configuration Files]
    FILE --> DEFAULT[Default Values]
    
    CLI -.->|Highest Priority| FINAL[Final Configuration]
    ENV -.->|High Priority| FINAL
    FILE -.->|Medium Priority| FINAL
    DEFAULT -.->|Lowest Priority| FINAL
    
    FINAL --> VALIDATE[Validation]
    VALIDATE --> APPLY[Apply Configuration]
```

## Configuration Architecture

### Design Decisions

**Why hierarchical configuration?**
- **Flexibility**: Override specific settings without changing everything
- **Maintainability**: Clear precedence rules prevent confusion
- **Security**: Sensitive values can be provided via environment variables
- **Deployment**: Same binary works across environments

**Why structured configuration over key-value pairs?**
- **Type Safety**: Compile-time validation of configuration structure
- **Documentation**: Self-documenting through type definitions
- **IDE Support**: Auto-completion and error detection
- **Validation**: Complex validation rules and relationships

Create `src/config/mod.rs`:

```rust
//! Configuration management system
//!
//! This module provides a comprehensive configuration system that supports:
//! - Multiple configuration sources with clear precedence
//! - Type-safe configuration structures with validation
//! - Hot-reloading for dynamic updates
//! - Environment-specific overrides
//! - Secure handling of sensitive data

pub mod loader;
pub mod validation;
pub mod watcher;

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::path::PathBuf;
use std::time::Duration;

/// Main configuration structure for the load balancer
///
/// This structure defines all configurable aspects of the load balancer.
/// Each field has sensible defaults and can be overridden through various sources.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// Server configuration
    pub server: ServerConfig,
    
    /// Backend pool configuration
    pub backends: BackendConfig,
    
    /// Load balancing algorithm settings
    pub load_balancing: LoadBalancingConfig,
    
    /// Health checking configuration
    pub health_checks: HealthCheckConfig,
    
    /// Logging and observability settings
    pub observability: ObservabilityConfig,
    
    /// Security and TLS configuration
    pub security: SecurityConfig,
    
    /// Performance tuning parameters
    pub performance: PerformanceConfig,
}

/// Server listening and connection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    /// Address to bind the load balancer
    #[serde(default = "default_bind_address")]
    pub bind_address: SocketAddr,
    
    /// Maximum number of concurrent connections
    #[serde(default = "default_max_connections")]
    pub max_connections: usize,
    
    /// Connection timeout duration
    #[serde(default = "default_connection_timeout")]
    pub connection_timeout: Duration,
    
    /// Keep-alive timeout for idle connections
    #[serde(default = "default_keepalive_timeout")]
    pub keepalive_timeout: Duration,
    
    /// Enable HTTP/2 support
    #[serde(default = "default_http2_enabled")]
    pub http2_enabled: bool,
}

/// Backend server pool configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackendConfig {
    /// List of backend servers with their configurations
    pub servers: Vec<BackendServerConfig>,
    
    /// Default backend timeout
    #[serde(default = "default_backend_timeout")]
    pub default_timeout: Duration,
    
    /// Maximum number of retries for failed requests
    #[serde(default = "default_max_retries")]
    pub max_retries: u32,
    
    /// Connection pool settings
    pub connection_pool: ConnectionPoolConfig,
}

/// Individual backend server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackendServerConfig {
    /// Server address
    pub address: SocketAddr,
    
    /// Server weight for load balancing (1-100)
    #[serde(default = "default_server_weight")]
    pub weight: u32,
    
    /// Maximum connections to this server
    #[serde(default = "default_max_connections_per_server")]
    pub max_connections: usize,
    
    /// Server-specific timeout override
    pub timeout: Option<Duration>,
    
    /// Tags for server categorization
    #[serde(default)]
    pub tags: HashMap<String, String>,
}

/// Connection pool configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionPoolConfig {
    /// Maximum connections per backend
    #[serde(default = "default_pool_max_connections")]
    pub max_connections_per_backend: usize,
    
    /// Idle connection timeout
    #[serde(default = "default_pool_idle_timeout")]
    pub idle_timeout: Duration,
    
    /// Connection establishment timeout
    #[serde(default = "default_pool_connect_timeout")]
    pub connect_timeout: Duration,
    
    /// Enable connection pooling
    #[serde(default = "default_pool_enabled")]
    pub enabled: bool,
}

/// Load balancing algorithm configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoadBalancingConfig {
    /// Algorithm type
    #[serde(default)]
    pub algorithm: LoadBalancingAlgorithm,
    
    /// Sticky session configuration
    pub sticky_sessions: Option<StickySessionConfig>,
    
    /// Circuit breaker settings
    pub circuit_breaker: CircuitBreakerConfig,
}

/// Available load balancing algorithms
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum LoadBalancingAlgorithm {
    RoundRobin,
    WeightedRoundRobin,
    LeastConnections,
    WeightedLeastConnections,
    IpHash,
    ConsistentHash,
}

impl Default for LoadBalancingAlgorithm {
    fn default() -> Self {
        LoadBalancingAlgorithm::RoundRobin
    }
}

/// Sticky session configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StickySessionConfig {
    /// Cookie name for session tracking
    #[serde(default = "default_session_cookie")]
    pub cookie_name: String,
    
    /// Session timeout
    #[serde(default = "default_session_timeout")]
    pub timeout: Duration,
    
    /// Use secure cookies (HTTPS only)
    #[serde(default)]
    pub secure: bool,
}

/// Circuit breaker configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerConfig {
    /// Enable circuit breaker
    #[serde(default = "default_circuit_breaker_enabled")]
    pub enabled: bool,
    
    /// Failure threshold to open circuit
    #[serde(default = "default_failure_threshold")]
    pub failure_threshold: u32,
    
    /// Success threshold to close circuit
    #[serde(default = "default_success_threshold")]
    pub success_threshold: u32,
    
    /// Timeout before attempting to close circuit
    #[serde(default = "default_circuit_timeout")]
    pub timeout: Duration,
}

/// Health check configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    /// Enable health checking
    #[serde(default = "default_health_check_enabled")]
    pub enabled: bool,
    
    /// Health check interval
    #[serde(default = "default_health_check_interval")]
    pub interval: Duration,
    
    /// Health check timeout
    #[serde(default = "default_health_check_timeout")]
    pub timeout: Duration,
    
    /// Number of consecutive failures before marking unhealthy
    #[serde(default = "default_unhealthy_threshold")]
    pub unhealthy_threshold: u32,
    
    /// Number of consecutive successes before marking healthy
    #[serde(default = "default_healthy_threshold")]
    pub healthy_threshold: u32,
    
    /// Health check endpoint path
    #[serde(default = "default_health_check_path")]
    pub path: String,
    
    /// Expected HTTP status codes for healthy response
    #[serde(default = "default_healthy_status_codes")]
    pub healthy_status_codes: Vec<u16>,
}

/// Observability configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ObservabilityConfig {
    /// Logging configuration
    pub logging: LoggingConfig,
    
    /// Metrics configuration
    pub metrics: MetricsConfig,
    
    /// Tracing configuration
    pub tracing: TracingConfig,
}

/// Logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// Log level
    #[serde(default = "default_log_level")]
    pub level: String,
    
    /// Log format (json, pretty, compact)
    #[serde(default = "default_log_format")]
    pub format: String,
    
    /// Log file path (optional)
    pub file: Option<PathBuf>,
    
    /// Enable structured logging
    #[serde(default = "default_structured_logging")]
    pub structured: bool,
}

/// Metrics configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsConfig {
    /// Enable metrics collection
    #[serde(default = "default_metrics_enabled")]
    pub enabled: bool,
    
    /// Metrics endpoint address
    #[serde(default = "default_metrics_address")]
    pub address: SocketAddr,
    
    /// Metrics collection interval
    #[serde(default = "default_metrics_interval")]
    pub interval: Duration,
}

/// Distributed tracing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TracingConfig {
    /// Enable distributed tracing
    #[serde(default)]
    pub enabled: bool,
    
    /// Jaeger endpoint
    pub jaeger_endpoint: Option<String>,
    
    /// Sampling rate (0.0 to 1.0)
    #[serde(default = "default_sampling_rate")]
    pub sampling_rate: f64,
}

/// Security and TLS configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// TLS configuration
    pub tls: Option<TlsConfig>,
    
    /// Rate limiting configuration
    pub rate_limiting: Option<RateLimitConfig>,
    
    /// Access control configuration
    pub access_control: Option<AccessControlConfig>,
}

/// TLS configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TlsConfig {
    /// Certificate file path
    pub cert_file: PathBuf,
    
    /// Private key file path
    pub key_file: PathBuf,
    
    /// CA certificate file path (for client verification)
    pub ca_file: Option<PathBuf>,
    
    /// Require client certificates
    #[serde(default)]
    pub require_client_cert: bool,
    
    /// Supported TLS versions
    #[serde(default = "default_tls_versions")]
    pub versions: Vec<String>,
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Enable rate limiting
    #[serde(default)]
    pub enabled: bool,
    
    /// Requests per second limit
    pub requests_per_second: u32,
    
    /// Burst capacity
    pub burst_capacity: u32,
    
    /// Rate limiting window
    #[serde(default = "default_rate_limit_window")]
    pub window: Duration,
}

/// Access control configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessControlConfig {
    /// Allowed IP addresses/ranges
    #[serde(default)]
    pub allowed_ips: Vec<String>,
    
    /// Blocked IP addresses/ranges
    #[serde(default)]
    pub blocked_ips: Vec<String>,
    
    /// Enable IP-based access control
    #[serde(default)]
    pub enabled: bool,
}

/// Performance tuning configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// Number of worker threads
    pub worker_threads: Option<usize>,
    
    /// TCP socket buffer sizes
    pub socket_buffers: SocketBufferConfig,
    
    /// Memory allocation settings
    pub memory: MemoryConfig,
}

/// Socket buffer configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SocketBufferConfig {
    /// Receive buffer size
    #[serde(default = "default_recv_buffer_size")]
    pub recv_buffer_size: usize,
    
    /// Send buffer size
    #[serde(default = "default_send_buffer_size")]
    pub send_buffer_size: usize,
    
    /// Enable TCP_NODELAY
    #[serde(default = "default_tcp_nodelay")]
    pub tcp_nodelay: bool,
}

/// Memory configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryConfig {
    /// Maximum memory usage (bytes)
    pub max_memory: Option<usize>,
    
    /// Buffer pool size
    #[serde(default = "default_buffer_pool_size")]
    pub buffer_pool_size: usize,
    
    /// Enable memory-mapped files for large responses
    #[serde(default)]
    pub use_mmap: bool,
}

// Default value functions
fn default_bind_address() -> SocketAddr { "0.0.0.0:8080".parse().unwrap() }
fn default_max_connections() -> usize { 10000 }
fn default_connection_timeout() -> Duration { Duration::from_secs(30) }
fn default_keepalive_timeout() -> Duration { Duration::from_secs(60) }
fn default_http2_enabled() -> bool { true }
fn default_backend_timeout() -> Duration { Duration::from_secs(30) }
fn default_max_retries() -> u32 { 3 }
fn default_server_weight() -> u32 { 1 }
fn default_max_connections_per_server() -> usize { 100 }
fn default_pool_max_connections() -> usize { 100 }
fn default_pool_idle_timeout() -> Duration { Duration::from_secs(300) }
fn default_pool_connect_timeout() -> Duration { Duration::from_secs(10) }
fn default_pool_enabled() -> bool { true }
fn default_session_cookie() -> String { "RUSTY_LB_SESSION".to_string() }
fn default_session_timeout() -> Duration { Duration::from_secs(3600) }
fn default_circuit_breaker_enabled() -> bool { true }
fn default_failure_threshold() -> u32 { 5 }
fn default_success_threshold() -> u32 { 3 }
fn default_circuit_timeout() -> Duration { Duration::from_secs(60) }
fn default_health_check_enabled() -> bool { true }
fn default_health_check_interval() -> Duration { Duration::from_secs(30) }
fn default_health_check_timeout() -> Duration { Duration::from_secs(5) }
fn default_unhealthy_threshold() -> u32 { 3 }
fn default_healthy_threshold() -> u32 { 2 }
fn default_health_check_path() -> String { "/health".to_string() }
fn default_healthy_status_codes() -> Vec<u16> { vec![200, 204] }
fn default_log_level() -> String { "info".to_string() }
fn default_log_format() -> String { "json".to_string() }
fn default_structured_logging() -> bool { true }
fn default_metrics_enabled() -> bool { true }
fn default_metrics_address() -> SocketAddr { "0.0.0.0:9090".parse().unwrap() }
fn default_metrics_interval() -> Duration { Duration::from_secs(15) }
fn default_sampling_rate() -> f64 { 0.1 }
fn default_tls_versions() -> Vec<String> { vec!["1.2".to_string(), "1.3".to_string()] }
fn default_rate_limit_window() -> Duration { Duration::from_secs(60) }
fn default_recv_buffer_size() -> usize { 65536 }
fn default_send_buffer_size() -> usize { 65536 }
fn default_tcp_nodelay() -> bool { true }
fn default_buffer_pool_size() -> usize { 1024 }

impl Default for Config {
    fn default() -> Self {
        Self {
            server: ServerConfig::default(),
            backends: BackendConfig::default(),
            load_balancing: LoadBalancingConfig::default(),
            health_checks: HealthCheckConfig::default(),
            observability: ObservabilityConfig::default(),
            security: SecurityConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }
}

// Implement Default for all config structs
impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            bind_address: default_bind_address(),
            max_connections: default_max_connections(),
            connection_timeout: default_connection_timeout(),
            keepalive_timeout: default_keepalive_timeout(),
            http2_enabled: default_http2_enabled(),
        }
    }
}

impl Default for BackendConfig {
    fn default() -> Self {
        Self {
            servers: vec![],
            default_timeout: default_backend_timeout(),
            max_retries: default_max_retries(),
            connection_pool: ConnectionPoolConfig::default(),
        }
    }
}

impl Default for ConnectionPoolConfig {
    fn default() -> Self {
        Self {
            max_connections_per_backend: default_pool_max_connections(),
            idle_timeout: default_pool_idle_timeout(),
            connect_timeout: default_pool_connect_timeout(),
            enabled: default_pool_enabled(),
        }
    }
}

impl Default for LoadBalancingConfig {
    fn default() -> Self {
        Self {
            algorithm: LoadBalancingAlgorithm::default(),
            sticky_sessions: None,
            circuit_breaker: CircuitBreakerConfig::default(),
        }
    }
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            enabled: default_circuit_breaker_enabled(),
            failure_threshold: default_failure_threshold(),
            success_threshold: default_success_threshold(),
            timeout: default_circuit_timeout(),
        }
    }
}

impl Default for HealthCheckConfig {
    fn default() -> Self {
        Self {
            enabled: default_health_check_enabled(),
            interval: default_health_check_interval(),
            timeout: default_health_check_timeout(),
            unhealthy_threshold: default_unhealthy_threshold(),
            healthy_threshold: default_healthy_threshold(),
            path: default_health_check_path(),
            healthy_status_codes: default_healthy_status_codes(),
        }
    }
}

impl Default for ObservabilityConfig {
    fn default() -> Self {
        Self {
            logging: LoggingConfig::default(),
            metrics: MetricsConfig::default(),
            tracing: TracingConfig::default(),
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: default_log_level(),
            format: default_log_format(),
            file: None,
            structured: default_structured_logging(),
        }
    }
}

impl Default for MetricsConfig {
    fn default() -> Self {
        Self {
            enabled: default_metrics_enabled(),
            address: default_metrics_address(),
            interval: default_metrics_interval(),
        }
    }
}

impl Default for TracingConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            jaeger_endpoint: None,
            sampling_rate: default_sampling_rate(),
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            tls: None,
            rate_limiting: None,
            access_control: None,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            worker_threads: None,
            socket_buffers: SocketBufferConfig::default(),
            memory: MemoryConfig::default(),
        }
    }
}

impl Default for SocketBufferConfig {
    fn default() -> Self {
        Self {
            recv_buffer_size: default_recv_buffer_size(),
            send_buffer_size: default_send_buffer_size(),
            tcp_nodelay: default_tcp_nodelay(),
        }
    }
}

impl Default for MemoryConfig {
    fn default() -> Self {
        Self {
            max_memory: None,
            buffer_pool_size: default_buffer_pool_size(),
            use_mmap: false,
        }
    }
}
```

## Configuration Loader Implementation

Create `src/config/loader.rs`:

```rust
//! Configuration loading from multiple sources
//!
//! This module implements the configuration loading logic with support for:
//! - YAML/JSON configuration files
//! - Environment variable overrides
//! - Command-line argument overrides
//! - Configuration validation and error reporting

use super::Config;
use crate::Result;
use serde_yaml;
use std::env;
use std::fs;
use std::path::Path;
use tracing::{info, warn, debug};
use clap::{Arg, Command};

/// Configuration loader with multiple source support
pub struct ConfigLoader {
    /// Configuration file paths to try (in order)
    config_paths: Vec<String>,

    /// Environment variable prefix
    env_prefix: String,

    /// Enable environment variable loading
    load_env: bool,

    /// Enable CLI argument parsing
    parse_cli: bool,
}

impl ConfigLoader {
    /// Create a new configuration loader with default settings
    pub fn new() -> Self {
        Self {
            config_paths: vec![
                "config.yaml".to_string(),
                "config.yml".to_string(),
                "/etc/rusty-balancer/config.yaml".to_string(),
                "~/.config/rusty-balancer/config.yaml".to_string(),
            ],
            env_prefix: "RUSTY_LB".to_string(),
            load_env: true,
            parse_cli: true,
        }
    }

    /// Add a configuration file path
    pub fn add_config_path<P: AsRef<Path>>(mut self, path: P) -> Self {
        self.config_paths.insert(0, path.as_ref().to_string_lossy().to_string());
        self
    }

    /// Set environment variable prefix
    pub fn with_env_prefix(mut self, prefix: &str) -> Self {
        self.env_prefix = prefix.to_string();
        self
    }

    /// Enable/disable environment variable loading
    pub fn with_env_loading(mut self, enabled: bool) -> Self {
        self.load_env = enabled;
        self
    }

    /// Enable/disable CLI argument parsing
    pub fn with_cli_parsing(mut self, enabled: bool) -> Self {
        self.parse_cli = enabled;
        self
    }

    /// Load configuration from all sources
    pub fn load(&self) -> Result<Config> {
        info!("Loading configuration from multiple sources");

        // Start with default configuration
        let mut config = Config::default();

        // 1. Load from configuration file (lowest priority)
        if let Some(file_config) = self.load_from_file()? {
            config = self.merge_configs(config, file_config)?;
            info!("✅ Configuration loaded from file");
        }

        // 2. Override with environment variables (medium priority)
        if self.load_env {
            config = self.apply_env_overrides(config)?;
            info!("✅ Environment variable overrides applied");
        }

        // 3. Override with CLI arguments (highest priority)
        if self.parse_cli {
            config = self.apply_cli_overrides(config)?;
            info!("✅ CLI argument overrides applied");
        }

        // 4. Validate final configuration
        self.validate_config(&config)?;
        info!("✅ Configuration validation passed");

        Ok(config)
    }

    /// Load configuration from the first available file
    fn load_from_file(&self) -> Result<Option<Config>> {
        for path in &self.config_paths {
            let expanded_path = self.expand_path(path);

            if Path::new(&expanded_path).exists() {
                debug!("Found configuration file: {}", expanded_path);

                let content = fs::read_to_string(&expanded_path)
                    .map_err(|e| format!("Failed to read config file {}: {}", expanded_path, e))?;

                let config: Config = if expanded_path.ends_with(".json") {
                    serde_json::from_str(&content)
                        .map_err(|e| format!("Failed to parse JSON config: {}", e))?
                } else {
                    serde_yaml::from_str(&content)
                        .map_err(|e| format!("Failed to parse YAML config: {}", e))?
                };

                info!("Loaded configuration from: {}", expanded_path);
                return Ok(Some(config));
            }
        }

        warn!("No configuration file found, using defaults");
        Ok(None)
    }

    /// Apply environment variable overrides
    fn apply_env_overrides(&self, mut config: Config) -> Result<Config> {
        debug!("Applying environment variable overrides with prefix: {}", self.env_prefix);

        // Server configuration
        if let Ok(bind_addr) = env::var(format!("{}_BIND_ADDRESS", self.env_prefix)) {
            config.server.bind_address = bind_addr.parse()
                .map_err(|e| format!("Invalid bind address in env: {}", e))?;
        }

        if let Ok(max_conn) = env::var(format!("{}_MAX_CONNECTIONS", self.env_prefix)) {
            config.server.max_connections = max_conn.parse()
                .map_err(|e| format!("Invalid max connections in env: {}", e))?;
        }

        // Backend configuration
        if let Ok(backend_timeout) = env::var(format!("{}_BACKEND_TIMEOUT", self.env_prefix)) {
            let timeout_secs: u64 = backend_timeout.parse()
                .map_err(|e| format!("Invalid backend timeout in env: {}", e))?;
            config.backends.default_timeout = std::time::Duration::from_secs(timeout_secs);
        }

        // Load balancing algorithm
        if let Ok(algorithm) = env::var(format!("{}_ALGORITHM", self.env_prefix)) {
            config.load_balancing.algorithm = match algorithm.to_lowercase().as_str() {
                "round_robin" => super::LoadBalancingAlgorithm::RoundRobin,
                "weighted_round_robin" => super::LoadBalancingAlgorithm::WeightedRoundRobin,
                "least_connections" => super::LoadBalancingAlgorithm::LeastConnections,
                "weighted_least_connections" => super::LoadBalancingAlgorithm::WeightedLeastConnections,
                "ip_hash" => super::LoadBalancingAlgorithm::IpHash,
                "consistent_hash" => super::LoadBalancingAlgorithm::ConsistentHash,
                _ => return Err(format!("Unknown load balancing algorithm: {}", algorithm).into()),
            };
        }

        // Logging configuration
        if let Ok(log_level) = env::var(format!("{}_LOG_LEVEL", self.env_prefix)) {
            config.observability.logging.level = log_level;
        }

        // TLS configuration
        if let Ok(cert_file) = env::var(format!("{}_TLS_CERT_FILE", self.env_prefix)) {
            if let Ok(key_file) = env::var(format!("{}_TLS_KEY_FILE", self.env_prefix)) {
                config.security.tls = Some(super::TlsConfig {
                    cert_file: cert_file.into(),
                    key_file: key_file.into(),
                    ca_file: env::var(format!("{}_TLS_CA_FILE", self.env_prefix)).ok().map(Into::into),
                    require_client_cert: env::var(format!("{}_TLS_REQUIRE_CLIENT_CERT", self.env_prefix))
                        .map(|v| v.parse().unwrap_or(false))
                        .unwrap_or(false),
                    versions: super::default_tls_versions(),
                });
            }
        }

        Ok(config)
    }

    /// Apply CLI argument overrides
    fn apply_cli_overrides(&self, mut config: Config) -> Result<Config> {
        let app = Command::new("rusty-balancer")
            .version("1.0.0")
            .about("High-performance load balancer written in Rust")
            .arg(Arg::new("bind-address")
                .long("bind-address")
                .short('b')
                .value_name("ADDRESS")
                .help("Address to bind the load balancer")
                .takes_value(true))
            .arg(Arg::new("config")
                .long("config")
                .short('c')
                .value_name("FILE")
                .help("Configuration file path")
                .takes_value(true))
            .arg(Arg::new("log-level")
                .long("log-level")
                .short('l')
                .value_name("LEVEL")
                .help("Log level (trace, debug, info, warn, error)")
                .takes_value(true))
            .arg(Arg::new("max-connections")
                .long("max-connections")
                .value_name("COUNT")
                .help("Maximum number of concurrent connections")
                .takes_value(true))
            .arg(Arg::new("backend-timeout")
                .long("backend-timeout")
                .value_name("SECONDS")
                .help("Backend request timeout in seconds")
                .takes_value(true))
            .arg(Arg::new("algorithm")
                .long("algorithm")
                .short('a')
                .value_name("ALGORITHM")
                .help("Load balancing algorithm")
                .possible_values(&["round_robin", "weighted_round_robin", "least_connections",
                                 "weighted_least_connections", "ip_hash", "consistent_hash"])
                .takes_value(true));

        let matches = app.try_get_matches()
            .map_err(|e| format!("CLI argument parsing failed: {}", e))?;

        // Apply CLI overrides
        if let Some(bind_addr) = matches.value_of("bind-address") {
            config.server.bind_address = bind_addr.parse()
                .map_err(|e| format!("Invalid bind address: {}", e))?;
        }

        if let Some(log_level) = matches.value_of("log-level") {
            config.observability.logging.level = log_level.to_string();
        }

        if let Some(max_conn) = matches.value_of("max-connections") {
            config.server.max_connections = max_conn.parse()
                .map_err(|e| format!("Invalid max connections: {}", e))?;
        }

        if let Some(timeout) = matches.value_of("backend-timeout") {
            let timeout_secs: u64 = timeout.parse()
                .map_err(|e| format!("Invalid backend timeout: {}", e))?;
            config.backends.default_timeout = std::time::Duration::from_secs(timeout_secs);
        }

        if let Some(algorithm) = matches.value_of("algorithm") {
            config.load_balancing.algorithm = match algorithm {
                "round_robin" => super::LoadBalancingAlgorithm::RoundRobin,
                "weighted_round_robin" => super::LoadBalancingAlgorithm::WeightedRoundRobin,
                "least_connections" => super::LoadBalancingAlgorithm::LeastConnections,
                "weighted_least_connections" => super::LoadBalancingAlgorithm::WeightedLeastConnections,
                "ip_hash" => super::LoadBalancingAlgorithm::IpHash,
                "consistent_hash" => super::LoadBalancingAlgorithm::ConsistentHash,
                _ => unreachable!(), // clap validates this
            };
        }

        Ok(config)
    }

    /// Merge two configurations, with the second taking precedence
    fn merge_configs(&self, base: Config, override_config: Config) -> Result<Config> {
        // For now, we'll do a simple replacement merge
        // In a production system, you might want more sophisticated merging
        Ok(override_config)
    }

    /// Validate the final configuration
    fn validate_config(&self, config: &Config) -> Result<()> {
        // Validate server configuration
        if config.server.max_connections == 0 {
            return Err("max_connections must be greater than 0".into());
        }

        if config.server.connection_timeout.as_secs() == 0 {
            return Err("connection_timeout must be greater than 0".into());
        }

        // Validate backend configuration
        if config.backends.servers.is_empty() {
            warn!("No backend servers configured - load balancer will not be able to serve requests");
        }

        for (i, server) in config.backends.servers.iter().enumerate() {
            if server.weight == 0 {
                return Err(format!("Backend server {} weight must be greater than 0", i).into());
            }

            if server.max_connections == 0 {
                return Err(format!("Backend server {} max_connections must be greater than 0", i).into());
            }
        }

        // Validate health check configuration
        if config.health_checks.enabled {
            if config.health_checks.interval.as_secs() == 0 {
                return Err("health_check_interval must be greater than 0".into());
            }

            if config.health_checks.timeout >= config.health_checks.interval {
                return Err("health_check_timeout must be less than health_check_interval".into());
            }

            if config.health_checks.unhealthy_threshold == 0 {
                return Err("unhealthy_threshold must be greater than 0".into());
            }

            if config.health_checks.healthy_threshold == 0 {
                return Err("healthy_threshold must be greater than 0".into());
            }
        }

        // Validate TLS configuration
        if let Some(ref tls) = config.security.tls {
            if !tls.cert_file.exists() {
                return Err(format!("TLS certificate file not found: {:?}", tls.cert_file).into());
            }

            if !tls.key_file.exists() {
                return Err(format!("TLS private key file not found: {:?}", tls.key_file).into());
            }

            if let Some(ref ca_file) = tls.ca_file {
                if !ca_file.exists() {
                    return Err(format!("TLS CA file not found: {:?}", ca_file).into());
                }
            }
        }

        // Validate rate limiting configuration
        if let Some(ref rate_limit) = config.security.rate_limiting {
            if rate_limit.enabled {
                if rate_limit.requests_per_second == 0 {
                    return Err("rate_limit requests_per_second must be greater than 0".into());
                }

                if rate_limit.burst_capacity == 0 {
                    return Err("rate_limit burst_capacity must be greater than 0".into());
                }
            }
        }

        info!("✅ Configuration validation completed successfully");
        Ok(())
    }

    /// Expand path with home directory support
    fn expand_path(&self, path: &str) -> String {
        if path.starts_with("~/") {
            if let Ok(home) = env::var("HOME") {
                return path.replacen("~", &home, 1);
            }
        }
        path.to_string()
    }
}

impl Default for ConfigLoader {
    fn default() -> Self {
        Self::new()
    }
}
```

## Configuration Hot-Reloading

Create `src/config/watcher.rs`:

```rust
//! Configuration file watching for hot-reloading
//!
//! This module provides file system watching capabilities to detect
//! configuration changes and trigger reloads without service interruption.

use super::{Config, ConfigLoader};
use crate::Result;
use notify::{Watcher, RecursiveMode, watcher, DebouncedEvent};
use std::path::Path;
use std::sync::mpsc;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug};

/// Configuration watcher for hot-reloading
pub struct ConfigWatcher {
    /// Current configuration
    config: Arc<RwLock<Config>>,

    /// Configuration loader
    loader: ConfigLoader,

    /// File system watcher
    _watcher: notify::RecommendedWatcher,

    /// Configuration change receiver
    receiver: mpsc::Receiver<DebouncedEvent>,
}

impl ConfigWatcher {
    /// Create a new configuration watcher
    pub fn new(config_path: &Path, loader: ConfigLoader) -> Result<Self> {
        let (tx, receiver) = mpsc::channel();

        // Create file system watcher with 1 second debounce
        let mut watcher = watcher(tx, Duration::from_secs(1))
            .map_err(|e| format!("Failed to create file watcher: {}", e))?;

        // Watch the configuration file
        watcher.watch(config_path, RecursiveMode::NonRecursive)
            .map_err(|e| format!("Failed to watch config file: {}", e))?;

        // Load initial configuration
        let initial_config = loader.load()?;

        info!("🔍 Configuration watcher started for: {:?}", config_path);

        Ok(Self {
            config: Arc::new(RwLock::new(initial_config)),
            loader,
            _watcher: watcher,
            receiver,
        })
    }

    /// Get the current configuration
    pub async fn get_config(&self) -> Config {
        self.config.read().await.clone()
    }

    /// Get a reference to the configuration for sharing
    pub fn config_ref(&self) -> Arc<RwLock<Config>> {
        self.config.clone()
    }

    /// Start watching for configuration changes
    pub async fn start_watching(&mut self) -> Result<()> {
        info!("🔄 Starting configuration hot-reload monitoring");

        loop {
            match self.receiver.try_recv() {
                Ok(event) => {
                    match event {
                        DebouncedEvent::Write(_) | DebouncedEvent::Create(_) => {
                            debug!("Configuration file changed, reloading...");

                            match self.reload_config().await {
                                Ok(_) => {
                                    info!("✅ Configuration reloaded successfully");
                                }
                                Err(e) => {
                                    error!("❌ Failed to reload configuration: {}", e);
                                    warn!("Continuing with previous configuration");
                                }
                            }
                        }
                        DebouncedEvent::Remove(_) => {
                            warn!("⚠️ Configuration file was removed");
                        }
                        _ => {
                            // Ignore other events
                        }
                    }
                }
                Err(mpsc::TryRecvError::Empty) => {
                    // No events, sleep briefly
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
                Err(mpsc::TryRecvError::Disconnected) => {
                    error!("Configuration watcher disconnected");
                    break;
                }
            }
        }

        Ok(())
    }

    /// Reload configuration from file
    async fn reload_config(&self) -> Result<()> {
        // Load new configuration
        let new_config = self.loader.load()?;

        // Validate the new configuration before applying
        // This prevents applying invalid configurations
        self.validate_config_change(&new_config).await?;

        // Apply the new configuration
        {
            let mut config = self.config.write().await;
            *config = new_config;
        }

        Ok(())
    }

    /// Validate configuration changes
    async fn validate_config_change(&self, new_config: &Config) -> Result<()> {
        let current_config = self.config.read().await;

        // Check for critical changes that might require restart
        if current_config.server.bind_address != new_config.server.bind_address {
            warn!("⚠️ Bind address changed - this requires a restart to take effect");
        }

        if current_config.security.tls.is_some() != new_config.security.tls.is_some() {
            warn!("⚠️ TLS configuration changed - this requires a restart to take effect");
        }

        // Validate backend servers are reachable (optional)
        // This could be extended to actually test connectivity
        for server in &new_config.backends.servers {
            debug!("Backend server configured: {}", server.address);
        }

        Ok(())
    }
}
```

## Example Configuration Files

### Basic Configuration (config.yaml)

```yaml
# Rusty Load Balancer Configuration
# This file demonstrates a basic production configuration

server:
  bind_address: "0.0.0.0:8080"
  max_connections: 10000
  connection_timeout: "30s"
  keepalive_timeout: "60s"
  http2_enabled: true

backends:
  default_timeout: "30s"
  max_retries: 3
  servers:
    - address: "*********:8080"
      weight: 1
      max_connections: 100
      tags:
        zone: "us-east-1a"
        role: "api"
    - address: "*********:8080"
      weight: 2  # Higher capacity server
      max_connections: 200
      tags:
        zone: "us-east-1b"
        role: "api"
    - address: "*********:8080"
      weight: 1
      max_connections: 100
      tags:
        zone: "us-east-1c"
        role: "api"

  connection_pool:
    max_connections_per_backend: 100
    idle_timeout: "300s"
    connect_timeout: "10s"
    enabled: true

load_balancing:
  algorithm: "weighted_round_robin"
  circuit_breaker:
    enabled: true
    failure_threshold: 5
    success_threshold: 3
    timeout: "60s"

health_checks:
  enabled: true
  interval: "30s"
  timeout: "5s"
  unhealthy_threshold: 3
  healthy_threshold: 2
  path: "/health"
  healthy_status_codes: [200, 204]

observability:
  logging:
    level: "info"
    format: "json"
    structured: true

  metrics:
    enabled: true
    address: "0.0.0.0:9090"
    interval: "15s"

  tracing:
    enabled: false
    sampling_rate: 0.1

security:
  # TLS configuration (optional)
  # tls:
  #   cert_file: "/etc/ssl/certs/server.crt"
  #   key_file: "/etc/ssl/private/server.key"
  #   require_client_cert: false
  #   versions: ["1.2", "1.3"]

  # Rate limiting (optional)
  # rate_limiting:
  #   enabled: true
  #   requests_per_second: 1000
  #   burst_capacity: 2000
  #   window: "60s"

performance:
  # worker_threads: 8  # Auto-detect if not specified
  socket_buffers:
    recv_buffer_size: 65536
    send_buffer_size: 65536
    tcp_nodelay: true

  memory:
    buffer_pool_size: 1024
    use_mmap: false
```

### Production Configuration with TLS (production.yaml)

```yaml
# Production configuration with TLS and security features

server:
  bind_address: "0.0.0.0:443"
  max_connections: 50000
  connection_timeout: "30s"
  keepalive_timeout: "120s"
  http2_enabled: true

backends:
  default_timeout: "15s"
  max_retries: 2
  servers:
    - address: "*********:8080"
      weight: 3
      max_connections: 500
    - address: "*********:8080"
      weight: 3
      max_connections: 500
    - address: "*********:8080"
      weight: 2
      max_connections: 300

load_balancing:
  algorithm: "least_connections"
  sticky_sessions:
    cookie_name: "RUSTY_LB_SESSION"
    timeout: "3600s"
    secure: true
  circuit_breaker:
    enabled: true
    failure_threshold: 3
    success_threshold: 5
    timeout: "30s"

health_checks:
  enabled: true
  interval: "15s"
  timeout: "3s"
  unhealthy_threshold: 2
  healthy_threshold: 3
  path: "/api/health"
  healthy_status_codes: [200]

observability:
  logging:
    level: "warn"
    format: "json"
    file: "/var/log/rusty-balancer/app.log"
    structured: true

  metrics:
    enabled: true
    address: "127.0.0.1:9090"
    interval: "10s"

  tracing:
    enabled: true
    jaeger_endpoint: "http://jaeger:14268/api/traces"
    sampling_rate: 0.01

security:
  tls:
    cert_file: "/etc/ssl/certs/loadbalancer.crt"
    key_file: "/etc/ssl/private/loadbalancer.key"
    ca_file: "/etc/ssl/certs/ca.crt"
    require_client_cert: false
    versions: ["1.2", "1.3"]

  rate_limiting:
    enabled: true
    requests_per_second: 5000
    burst_capacity: 10000
    window: "60s"

  access_control:
    enabled: true
    allowed_ips:
      - "10.0.0.0/8"
      - "**********/12"
      - "***********/16"
    blocked_ips: []

performance:
  worker_threads: 16
  socket_buffers:
    recv_buffer_size: 131072
    send_buffer_size: 131072
    tcp_nodelay: true

  memory:
    max_memory: **********  # 8GB
    buffer_pool_size: 4096
    use_mmap: true
```

## Integration with Load Balancer

Update `src/main.rs` to use the configuration system:

```rust
use rusty_balancer::config::{ConfigLoader, ConfigWatcher};
use rusty_balancer::proxy::HttpProxy;
use rusty_balancer::backend::BackendPool;
use std::sync::Arc;
use std::path::Path;
use tracing::{info, error};
use tokio::signal;

#[tokio::main]
async fn main() -> rusty_balancer::Result<()> {
    // Initialize logging (will be reconfigured after loading config)
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("🚀 Starting Rusty Load Balancer");

    // Load configuration
    let config_loader = ConfigLoader::new()
        .add_config_path("config.yaml")
        .add_config_path("/etc/rusty-balancer/config.yaml");

    let config = config_loader.load()?;
    info!("✅ Configuration loaded successfully");

    // Reconfigure logging based on config
    configure_logging(&config.observability.logging)?;

    // Create backend pool from configuration
    let backend_pool = Arc::new(BackendPool::new());
    for server_config in &config.backends.servers {
        backend_pool.add_weighted_backend(
            server_config.address,
            server_config.weight,
        )?;
        info!("Added backend: {} (weight: {})",
              server_config.address, server_config.weight);
    }

    // Create HTTP proxy with configuration
    let proxy_config = rusty_balancer::proxy::ProxyConfig {
        backend_timeout: config.backends.default_timeout,
        max_retries: config.backends.max_retries,
        max_concurrent_requests: config.server.max_connections,
        connection_timeout: config.server.connection_timeout,
        keepalive_timeout: config.server.keepalive_timeout,
    };

    let proxy = HttpProxy::new(backend_pool.clone(), proxy_config);

    // Start configuration watcher for hot-reloading
    let config_path = Path::new("config.yaml");
    if config_path.exists() {
        let mut watcher = ConfigWatcher::new(config_path, config_loader)?;
        let config_ref = watcher.config_ref();

        // Spawn configuration watcher task
        tokio::spawn(async move {
            if let Err(e) = watcher.start_watching().await {
                error!("Configuration watcher error: {}", e);
            }
        });

        // Spawn configuration update task
        let backend_pool_clone = backend_pool.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(5));
            loop {
                interval.tick().await;

                let current_config = config_ref.read().await;
                // Update backend pool if configuration changed
                // This is a simplified example - in production you'd want
                // more sophisticated change detection and gradual updates

                // For now, just log that we're monitoring
                tracing::debug!("Configuration monitoring active");
            }
        });
    }

    // Start metrics server if enabled
    if config.observability.metrics.enabled {
        let metrics_addr = config.observability.metrics.address;
        tokio::spawn(async move {
            if let Err(e) = start_metrics_server(metrics_addr).await {
                error!("Metrics server error: {}", e);
            }
        });
        info!("📊 Metrics server started on {}", metrics_addr);
    }

    // Start the main proxy server
    let bind_addr = config.server.bind_address;
    info!("🌐 Load balancer listening on {}", bind_addr);

    // Handle graceful shutdown
    let shutdown_signal = async {
        signal::ctrl_c()
            .await
            .expect("Failed to install CTRL+C signal handler");
        info!("🛑 Shutdown signal received");
    };

    // Start proxy server with graceful shutdown
    tokio::select! {
        result = proxy.start(bind_addr) => {
            if let Err(e) = result {
                error!("Proxy server error: {}", e);
            }
        }
        _ = shutdown_signal => {
            info!("Initiating graceful shutdown...");
        }
    }

    info!("👋 Rusty Load Balancer stopped");
    Ok(())
}

/// Configure logging based on configuration
fn configure_logging(config: &rusty_balancer::config::LoggingConfig) -> rusty_balancer::Result<()> {
    use tracing_subscriber::{fmt, EnvFilter};

    let filter = EnvFilter::try_new(&config.level)
        .unwrap_or_else(|_| EnvFilter::new("info"));

    let subscriber = fmt()
        .with_env_filter(filter)
        .with_target(true)
        .with_thread_ids(true);

    match config.format.as_str() {
        "json" => {
            subscriber.json().init();
        }
        "pretty" => {
            subscriber.pretty().init();
        }
        _ => {
            subscriber.compact().init();
        }
    }

    Ok(())
}

/// Start metrics server
async fn start_metrics_server(addr: std::net::SocketAddr) -> rusty_balancer::Result<()> {
    use warp::Filter;

    let metrics = warp::path("metrics")
        .map(|| {
            // In a real implementation, you'd collect actual metrics here
            "# HELP rusty_balancer_requests_total Total number of requests\n\
             # TYPE rusty_balancer_requests_total counter\n\
             rusty_balancer_requests_total 42\n"
        });

    warp::serve(metrics)
        .run(addr)
        .await;

    Ok(())
}
```

## Testing the Configuration System

Create `tests/config_tests.rs`:

```rust
use rusty_balancer::config::{Config, ConfigLoader};
use std::env;
use std::fs;
use tempfile::TempDir;

#[test]
fn test_default_configuration() {
    let config = Config::default();

    assert_eq!(config.server.bind_address.to_string(), "0.0.0.0:8080");
    assert_eq!(config.server.max_connections, 10000);
    assert_eq!(config.backends.max_retries, 3);
    assert!(config.health_checks.enabled);
    assert!(config.observability.metrics.enabled);
}

#[test]
fn test_yaml_configuration_loading() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("test_config.yaml");

    let yaml_content = r#"
server:
  bind_address: "127.0.0.1:9090"
  max_connections: 5000

backends:
  default_timeout: "15s"
  max_retries: 5
  servers:
    - address: "************:8080"
      weight: 2
      max_connections: 200

load_balancing:
  algorithm: "least_connections"

health_checks:
  enabled: false
  interval: "60s"
"#;

    fs::write(&config_path, yaml_content).unwrap();

    let loader = ConfigLoader::new()
        .add_config_path(&config_path)
        .with_env_loading(false)
        .with_cli_parsing(false);

    let config = loader.load().unwrap();

    assert_eq!(config.server.bind_address.to_string(), "127.0.0.1:9090");
    assert_eq!(config.server.max_connections, 5000);
    assert_eq!(config.backends.max_retries, 5);
    assert!(!config.health_checks.enabled);
    assert_eq!(config.backends.servers.len(), 1);
    assert_eq!(config.backends.servers[0].weight, 2);
}

#[test]
fn test_environment_variable_overrides() {
    env::set_var("RUSTY_LB_BIND_ADDRESS", "0.0.0.0:3000");
    env::set_var("RUSTY_LB_MAX_CONNECTIONS", "15000");
    env::set_var("RUSTY_LB_ALGORITHM", "weighted_round_robin");
    env::set_var("RUSTY_LB_LOG_LEVEL", "debug");

    let loader = ConfigLoader::new()
        .with_cli_parsing(false);

    let config = loader.load().unwrap();

    assert_eq!(config.server.bind_address.to_string(), "0.0.0.0:3000");
    assert_eq!(config.server.max_connections, 15000);
    assert_eq!(config.observability.logging.level, "debug");

    // Clean up
    env::remove_var("RUSTY_LB_BIND_ADDRESS");
    env::remove_var("RUSTY_LB_MAX_CONNECTIONS");
    env::remove_var("RUSTY_LB_ALGORITHM");
    env::remove_var("RUSTY_LB_LOG_LEVEL");
}

#[test]
fn test_configuration_validation() {
    let mut config = Config::default();

    // Test invalid max_connections
    config.server.max_connections = 0;
    let loader = ConfigLoader::new();
    assert!(loader.validate_config(&config).is_err());

    // Test invalid backend weight
    config.server.max_connections = 1000;
    config.backends.servers.push(rusty_balancer::config::BackendServerConfig {
        address: "127.0.0.1:8080".parse().unwrap(),
        weight: 0,
        max_connections: 100,
        timeout: None,
        tags: std::collections::HashMap::new(),
    });

    assert!(loader.validate_config(&config).is_err());
}

#[tokio::test]
async fn test_configuration_hot_reload() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("hot_reload_test.yaml");

    // Initial configuration
    let initial_config = r#"
server:
  bind_address: "127.0.0.1:8080"
  max_connections: 1000
"#;

    fs::write(&config_path, initial_config).unwrap();

    let loader = ConfigLoader::new()
        .add_config_path(&config_path)
        .with_env_loading(false)
        .with_cli_parsing(false);

    let mut watcher = rusty_balancer::config::ConfigWatcher::new(&config_path, loader).unwrap();

    // Get initial config
    let config = watcher.get_config().await;
    assert_eq!(config.server.max_connections, 1000);

    // Update configuration file
    let updated_config = r#"
server:
  bind_address: "127.0.0.1:8080"
  max_connections: 2000
"#;

    fs::write(&config_path, updated_config).unwrap();

    // Give the watcher time to detect the change
    tokio::time::sleep(std::time::Duration::from_millis(1500)).await;

    // Note: In a real test, you'd need to actually trigger the watcher
    // This is a simplified example showing the structure
}
```

## Key Design Decisions Explained

### 1. Hierarchical Configuration Sources
**Decision**: CLI args > Environment variables > Config files > Defaults

**Rationale**:
- **Operational Flexibility**: Operators can override specific settings without changing files
- **Security**: Sensitive values (passwords, keys) can be provided via environment variables
- **Deployment**: Same configuration file can work across environments with env overrides
- **Debugging**: CLI args allow quick testing without permanent changes

### 2. Structured Configuration vs Key-Value
**Decision**: Use strongly-typed structs with serde

**Rationale**:
- **Type Safety**: Compile-time validation prevents runtime configuration errors
- **Documentation**: Configuration structure is self-documenting
- **IDE Support**: Auto-completion and error detection during development
- **Validation**: Complex validation rules and relationships between settings

### 3. Hot-Reloading Support
**Decision**: File system watching with validation before applying

**Rationale**:
- **Zero Downtime**: Configuration changes without service restart
- **Safety**: Validation prevents applying invalid configurations
- **Gradual Rollout**: Can implement gradual configuration changes
- **Operational Efficiency**: Faster iteration and debugging

### 4. Default Values Strategy
**Decision**: Sensible defaults for all optional settings

**Rationale**:
- **Ease of Use**: Minimal configuration required to get started
- **Production Ready**: Defaults are tuned for production workloads
- **Documentation**: Defaults serve as examples of reasonable values
- **Backward Compatibility**: New settings can be added without breaking existing configs

## Performance Considerations

1. **Configuration Access**: Use `Arc<RwLock<Config>>` for shared access with minimal contention
2. **Hot-Reload Frequency**: Debounced file watching prevents excessive reloads
3. **Validation Cost**: Validation only runs during configuration changes, not per request
4. **Memory Usage**: Configuration is cloned only when changed, not per access

## Security Considerations

1. **Sensitive Data**: Environment variables for secrets, never in config files
2. **File Permissions**: Configuration files should have restricted permissions
3. **Validation**: All configuration values are validated before use
4. **Audit Trail**: Configuration changes are logged for security auditing

## Navigation
- [Previous: Health Checking System](07-health-checking.md)
- [Next: Logging and Error Handling](09-logging-error-handling.md)
