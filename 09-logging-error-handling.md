# Module 09: Logging and Error Handling

## Learning Objectives
- Design a comprehensive error handling strategy for production systems
- Implement structured logging with contextual information
- Create custom error types with proper error propagation
- Build observability into error handling for debugging and monitoring
- Understand error handling best practices in distributed systems

## Why Robust Error Handling Matters

In production load balancers, error handling is critical because:

1. **Reliability**: Graceful degradation instead of crashes
2. **Observability**: Clear error information for debugging
3. **User Experience**: Meaningful error responses to clients
4. **Operations**: Actionable alerts and monitoring data
5. **Security**: Prevent information leakage through error messages

### Error Handling Architecture

```mermaid
graph TD
    Request[Incoming Request] --> Validate[Request Validation]
    Validate -->|Valid| Process[Process Request]
    Validate -->|Invalid| ClientError[Client Error 4xx]
    
    Process --> Backend[Backend Request]
    Backend -->|Success| Response[Success Response]
    Backend -->|Network Error| Retry[Retry Logic]
    Backend -->|Server Error| ServerError[Server Error 5xx]
    
    Retry -->|Max Retries| ServerError
    Retry -->|Retry Success| Response
    
    ClientError --> Log[Log Error]
    ServerError --> Log
    
    Log --> Metrics[Update Metrics]
    Metrics --> Alert[Alert if Needed]
    Alert --> Response
    
    Response --> Client[Client Response]
```

## Error Type Design

### Design Decisions

**Why custom error types?**
- **Context**: Include relevant information for debugging
- **Classification**: Different handling for different error types
- **Propagation**: Clean error bubbling through the call stack
- **Monitoring**: Structured error data for metrics and alerting

**Why structured errors over string messages?**
- **Programmatic Handling**: Code can react differently to different errors
- **Consistency**: Standardized error format across the system
- **Debugging**: Rich context information for troubleshooting
- **Monitoring**: Structured data for automated analysis

Create `src/error/mod.rs`:

```rust
//! Comprehensive error handling system
//!
//! This module provides a structured approach to error handling with:
//! - Custom error types for different failure scenarios
//! - Rich context information for debugging
//! - Proper error propagation and conversion
//! - Integration with logging and metrics systems

use std::fmt;
use std::error::Error as StdError;
use std::net::SocketAddr;
use std::time::Duration;
use serde::{Serialize, Deserialize};
use tracing::{error, warn, info};

/// Main error type for the load balancer
///
/// This enum covers all possible error scenarios in the load balancer,
/// providing structured error information for proper handling and debugging.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LoadBalancerError {
    /// Configuration-related errors
    Configuration {
        message: String,
        field: Option<String>,
        value: Option<String>,
    },
    
    /// Network connectivity errors
    Network {
        message: String,
        address: Option<SocketAddr>,
        error_code: Option<String>,
        retry_after: Option<Duration>,
    },
    
    /// Backend server errors
    Backend {
        message: String,
        backend_address: SocketAddr,
        status_code: Option<u16>,
        response_time: Option<Duration>,
        error_type: BackendErrorType,
    },
    
    /// HTTP protocol errors
    Http {
        message: String,
        status_code: u16,
        method: Option<String>,
        uri: Option<String>,
        headers: Option<Vec<(String, String)>>,
    },
    
    /// TLS/SSL related errors
    Tls {
        message: String,
        certificate_info: Option<String>,
        error_type: TlsErrorType,
    },
    
    /// Rate limiting errors
    RateLimit {
        message: String,
        client_ip: Option<String>,
        limit: u32,
        window: Duration,
        retry_after: Duration,
    },
    
    /// Health check errors
    HealthCheck {
        message: String,
        backend_address: SocketAddr,
        check_type: String,
        consecutive_failures: u32,
    },
    
    /// Circuit breaker errors
    CircuitBreaker {
        message: String,
        backend_address: SocketAddr,
        state: CircuitBreakerState,
        failure_count: u32,
    },
    
    /// Resource exhaustion errors
    Resource {
        message: String,
        resource_type: ResourceType,
        current_usage: Option<u64>,
        limit: Option<u64>,
    },
    
    /// Authentication and authorization errors
    Auth {
        message: String,
        auth_type: AuthErrorType,
        client_info: Option<String>,
    },
    
    /// Internal system errors
    Internal {
        message: String,
        component: String,
        error_id: String,
        context: Option<String>,
    },
    
    /// Timeout errors
    Timeout {
        message: String,
        operation: String,
        duration: Duration,
        timeout_limit: Duration,
    },
}

/// Backend error classification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BackendErrorType {
    ConnectionRefused,
    ConnectionTimeout,
    ReadTimeout,
    WriteTimeout,
    InvalidResponse,
    ServerError,
    ServiceUnavailable,
    TooManyRequests,
}

/// TLS error classification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TlsErrorType {
    CertificateExpired,
    CertificateInvalid,
    HandshakeFailed,
    ProtocolMismatch,
    CipherSuiteUnsupported,
}

/// Circuit breaker states
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CircuitBreakerState {
    Closed,
    Open,
    HalfOpen,
}

/// Resource types for resource exhaustion errors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResourceType {
    Memory,
    FileDescriptors,
    Connections,
    ThreadPool,
    BufferPool,
}

/// Authentication error types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AuthErrorType {
    MissingCredentials,
    InvalidCredentials,
    ExpiredToken,
    InsufficientPermissions,
    RateLimited,
}

impl fmt::Display for LoadBalancerError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            LoadBalancerError::Configuration { message, field, value } => {
                write!(f, "Configuration error: {}", message)?;
                if let Some(field) = field {
                    write!(f, " (field: {})", field)?;
                }
                if let Some(value) = value {
                    write!(f, " (value: {})", value)?;
                }
                Ok(())
            }
            LoadBalancerError::Network { message, address, error_code, .. } => {
                write!(f, "Network error: {}", message)?;
                if let Some(addr) = address {
                    write!(f, " (address: {})", addr)?;
                }
                if let Some(code) = error_code {
                    write!(f, " (code: {})", code)?;
                }
                Ok(())
            }
            LoadBalancerError::Backend { message, backend_address, status_code, error_type, .. } => {
                write!(f, "Backend error: {} (backend: {}, type: {:?})", 
                       message, backend_address, error_type)?;
                if let Some(status) = status_code {
                    write!(f, " (status: {})", status)?;
                }
                Ok(())
            }
            LoadBalancerError::Http { message, status_code, method, uri, .. } => {
                write!(f, "HTTP error: {} (status: {})", message, status_code)?;
                if let Some(method) = method {
                    write!(f, " (method: {})", method)?;
                }
                if let Some(uri) = uri {
                    write!(f, " (uri: {})", uri)?;
                }
                Ok(())
            }
            LoadBalancerError::Tls { message, error_type, .. } => {
                write!(f, "TLS error: {} (type: {:?})", message, error_type)
            }
            LoadBalancerError::RateLimit { message, client_ip, limit, window, .. } => {
                write!(f, "Rate limit error: {} (limit: {}/{:?})", message, limit, window)?;
                if let Some(ip) = client_ip {
                    write!(f, " (client: {})", ip)?;
                }
                Ok(())
            }
            LoadBalancerError::HealthCheck { message, backend_address, check_type, consecutive_failures } => {
                write!(f, "Health check error: {} (backend: {}, type: {}, failures: {})", 
                       message, backend_address, check_type, consecutive_failures)
            }
            LoadBalancerError::CircuitBreaker { message, backend_address, state, failure_count } => {
                write!(f, "Circuit breaker error: {} (backend: {}, state: {:?}, failures: {})", 
                       message, backend_address, state, failure_count)
            }
            LoadBalancerError::Resource { message, resource_type, current_usage, limit } => {
                write!(f, "Resource error: {} (type: {:?})", message, resource_type)?;
                if let (Some(usage), Some(limit)) = (current_usage, limit) {
                    write!(f, " (usage: {}/{} = {:.1}%)", usage, limit, 
                           (*usage as f64 / *limit as f64) * 100.0)?;
                }
                Ok(())
            }
            LoadBalancerError::Auth { message, auth_type, client_info } => {
                write!(f, "Auth error: {} (type: {:?})", message, auth_type)?;
                if let Some(client) = client_info {
                    write!(f, " (client: {})", client)?;
                }
                Ok(())
            }
            LoadBalancerError::Internal { message, component, error_id, .. } => {
                write!(f, "Internal error: {} (component: {}, id: {})", 
                       message, component, error_id)
            }
            LoadBalancerError::Timeout { message, operation, duration, timeout_limit } => {
                write!(f, "Timeout error: {} (operation: {}, took: {:?}, limit: {:?})", 
                       message, operation, duration, timeout_limit)
            }
        }
    }
}

impl StdError for LoadBalancerError {}

/// Result type alias for load balancer operations
pub type Result<T> = std::result::Result<T, LoadBalancerError>;

/// Error context for adding additional information to errors
#[derive(Debug, Clone)]
pub struct ErrorContext {
    pub request_id: Option<String>,
    pub client_ip: Option<String>,
    pub user_agent: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub component: String,
    pub operation: String,
}

impl ErrorContext {
    /// Create a new error context
    pub fn new(component: &str, operation: &str) -> Self {
        Self {
            request_id: None,
            client_ip: None,
            user_agent: None,
            timestamp: chrono::Utc::now(),
            component: component.to_string(),
            operation: operation.to_string(),
        }
    }
    
    /// Add request ID to context
    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }
    
    /// Add client IP to context
    pub fn with_client_ip(mut self, client_ip: String) -> Self {
        self.client_ip = Some(client_ip);
        self
    }
    
    /// Add user agent to context
    pub fn with_user_agent(mut self, user_agent: String) -> Self {
        self.user_agent = Some(user_agent);
        self
    }
}

/// Error logging and metrics integration
pub struct ErrorHandler {
    /// Component name for logging
    component: String,
    
    /// Enable detailed error logging
    detailed_logging: bool,
    
    /// Enable error metrics collection
    metrics_enabled: bool,
}

impl ErrorHandler {
    /// Create a new error handler
    pub fn new(component: &str) -> Self {
        Self {
            component: component.to_string(),
            detailed_logging: true,
            metrics_enabled: true,
        }
    }
    
    /// Handle an error with full logging and metrics
    pub fn handle_error(&self, error: &LoadBalancerError, context: Option<&ErrorContext>) {
        // Log the error with appropriate level
        self.log_error(error, context);
        
        // Update metrics
        if self.metrics_enabled {
            self.update_metrics(error);
        }
        
        // Trigger alerts if necessary
        self.check_alert_conditions(error);
    }
    
    /// Log error with structured information
    fn log_error(&self, error: &LoadBalancerError, context: Option<&ErrorContext>) {
        let error_level = self.determine_log_level(error);
        
        let mut fields = vec![
            ("component", self.component.as_str()),
            ("error_type", self.error_type_name(error)),
        ];
        
        if let Some(ctx) = context {
            if let Some(ref request_id) = ctx.request_id {
                fields.push(("request_id", request_id.as_str()));
            }
            if let Some(ref client_ip) = ctx.client_ip {
                fields.push(("client_ip", client_ip.as_str()));
            }
            fields.push(("operation", ctx.operation.as_str()));
        }
        
        match error_level {
            LogLevel::Error => {
                error!(
                    error = %error,
                    ?fields,
                    "Load balancer error occurred"
                );
            }
            LogLevel::Warn => {
                warn!(
                    error = %error,
                    ?fields,
                    "Load balancer warning"
                );
            }
            LogLevel::Info => {
                info!(
                    error = %error,
                    ?fields,
                    "Load balancer info"
                );
            }
        }
    }
    
    /// Determine appropriate log level for error
    fn determine_log_level(&self, error: &LoadBalancerError) -> LogLevel {
        match error {
            LoadBalancerError::Configuration { .. } => LogLevel::Error,
            LoadBalancerError::Network { .. } => LogLevel::Warn,
            LoadBalancerError::Backend { error_type, .. } => {
                match error_type {
                    BackendErrorType::ServiceUnavailable => LogLevel::Warn,
                    BackendErrorType::TooManyRequests => LogLevel::Info,
                    _ => LogLevel::Error,
                }
            }
            LoadBalancerError::Http { status_code, .. } => {
                if *status_code >= 500 {
                    LogLevel::Error
                } else if *status_code >= 400 {
                    LogLevel::Warn
                } else {
                    LogLevel::Info
                }
            }
            LoadBalancerError::Tls { .. } => LogLevel::Error,
            LoadBalancerError::RateLimit { .. } => LogLevel::Info,
            LoadBalancerError::HealthCheck { .. } => LogLevel::Warn,
            LoadBalancerError::CircuitBreaker { .. } => LogLevel::Warn,
            LoadBalancerError::Resource { .. } => LogLevel::Error,
            LoadBalancerError::Auth { .. } => LogLevel::Warn,
            LoadBalancerError::Internal { .. } => LogLevel::Error,
            LoadBalancerError::Timeout { .. } => LogLevel::Warn,
        }
    }
    
    /// Get error type name for metrics
    fn error_type_name(&self, error: &LoadBalancerError) -> &'static str {
        match error {
            LoadBalancerError::Configuration { .. } => "configuration",
            LoadBalancerError::Network { .. } => "network",
            LoadBalancerError::Backend { .. } => "backend",
            LoadBalancerError::Http { .. } => "http",
            LoadBalancerError::Tls { .. } => "tls",
            LoadBalancerError::RateLimit { .. } => "rate_limit",
            LoadBalancerError::HealthCheck { .. } => "health_check",
            LoadBalancerError::CircuitBreaker { .. } => "circuit_breaker",
            LoadBalancerError::Resource { .. } => "resource",
            LoadBalancerError::Auth { .. } => "auth",
            LoadBalancerError::Internal { .. } => "internal",
            LoadBalancerError::Timeout { .. } => "timeout",
        }
    }
    
    /// Update error metrics
    fn update_metrics(&self, error: &LoadBalancerError) {
        // In a real implementation, you'd update actual metrics here
        // For example, using prometheus or similar metrics library
        
        let error_type = self.error_type_name(error);
        tracing::debug!(
            component = %self.component,
            error_type = %error_type,
            "Error metrics updated"
        );
        
        // Example metrics that would be updated:
        // - error_count_total{component, error_type}
        // - error_rate{component, error_type}
        // - backend_error_count{backend_address, error_type}
        // - response_time_errors{percentile}
    }
    
    /// Check if error should trigger alerts
    fn check_alert_conditions(&self, error: &LoadBalancerError) {
        match error {
            LoadBalancerError::Resource { resource_type, current_usage, limit } => {
                if let (Some(usage), Some(limit)) = (current_usage, limit) {
                    let usage_percent = (*usage as f64 / *limit as f64) * 100.0;
                    if usage_percent > 90.0 {
                        tracing::error!(
                            resource_type = ?resource_type,
                            usage_percent = %usage_percent,
                            "ALERT: High resource usage detected"
                        );
                    }
                }
            }
            LoadBalancerError::Backend { consecutive_failures, .. } => {
                // This would be handled by health check system
            }
            LoadBalancerError::Internal { .. } => {
                tracing::error!("ALERT: Internal error detected - requires investigation");
            }
            _ => {
                // No special alerting for other error types
            }
        }
    }
}

/// Log levels for error classification
#[derive(Debug, Clone, Copy)]
enum LogLevel {
    Error,
    Warn,
    Info,
}

/// Convenience macros for error creation
#[macro_export]
macro_rules! config_error {
    ($msg:expr) => {
        LoadBalancerError::Configuration {
            message: $msg.to_string(),
            field: None,
            value: None,
        }
    };
    ($msg:expr, $field:expr) => {
        LoadBalancerError::Configuration {
            message: $msg.to_string(),
            field: Some($field.to_string()),
            value: None,
        }
    };
    ($msg:expr, $field:expr, $value:expr) => {
        LoadBalancerError::Configuration {
            message: $msg.to_string(),
            field: Some($field.to_string()),
            value: Some($value.to_string()),
        }
    };
}

#[macro_export]
macro_rules! backend_error {
    ($msg:expr, $addr:expr, $error_type:expr) => {
        LoadBalancerError::Backend {
            message: $msg.to_string(),
            backend_address: $addr,
            status_code: None,
            response_time: None,
            error_type: $error_type,
        }
    };
}

#[macro_export]
macro_rules! timeout_error {
    ($msg:expr, $operation:expr, $duration:expr, $limit:expr) => {
        LoadBalancerError::Timeout {
            message: $msg.to_string(),
            operation: $operation.to_string(),
            duration: $duration,
            timeout_limit: $limit,
        }
    };
}
```

## Structured Logging Implementation

Create `src/logging/mod.rs`:

```rust
//! Structured logging system with contextual information
//!
//! This module provides comprehensive logging capabilities including:
//! - Structured logging with JSON output
//! - Request tracing and correlation IDs
//! - Performance metrics logging
//! - Security event logging
//! - Log aggregation and filtering

use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::time::{Duration, Instant};
use tracing::{info, warn, error, debug, Span};
use tracing_subscriber::{fmt, EnvFilter, Registry};
use tracing_subscriber::layer::SubscriberExt;
use uuid::Uuid;

/// Request context for distributed tracing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RequestContext {
    /// Unique request identifier
    pub request_id: String,

    /// Client IP address
    pub client_ip: Option<SocketAddr>,

    /// HTTP method
    pub method: Option<String>,

    /// Request URI
    pub uri: Option<String>,

    /// User agent string
    pub user_agent: Option<String>,

    /// Request headers (filtered for security)
    pub headers: HashMap<String, String>,

    /// Request start time
    pub start_time: Instant,

    /// Backend server that handled the request
    pub backend_address: Option<SocketAddr>,

    /// Response status code
    pub status_code: Option<u16>,

    /// Response size in bytes
    pub response_size: Option<u64>,

    /// Total request duration
    pub duration: Option<Duration>,
}

impl RequestContext {
    /// Create a new request context
    pub fn new() -> Self {
        Self {
            request_id: Uuid::new_v4().to_string(),
            client_ip: None,
            method: None,
            uri: None,
            user_agent: None,
            headers: HashMap::new(),
            start_time: Instant::now(),
            backend_address: None,
            status_code: None,
            response_size: None,
            duration: None,
        }
    }

    /// Set client information
    pub fn with_client_info(mut self, ip: SocketAddr, user_agent: Option<String>) -> Self {
        self.client_ip = Some(ip);
        self.user_agent = user_agent;
        self
    }

    /// Set HTTP request information
    pub fn with_http_info(mut self, method: String, uri: String) -> Self {
        self.method = Some(method);
        self.uri = Some(uri);
        self
    }

    /// Add a header (filtered for security)
    pub fn add_header(&mut self, name: String, value: String) {
        // Filter out sensitive headers
        let sensitive_headers = [
            "authorization", "cookie", "x-api-key", "x-auth-token",
            "proxy-authorization", "www-authenticate"
        ];

        if !sensitive_headers.contains(&name.to_lowercase().as_str()) {
            self.headers.insert(name, value);
        }
    }

    /// Set backend information
    pub fn with_backend(mut self, address: SocketAddr) -> Self {
        self.backend_address = Some(address);
        self
    }

    /// Set response information
    pub fn with_response(mut self, status_code: u16, size: Option<u64>) -> Self {
        self.status_code = Some(status_code);
        self.response_size = size;
        self.duration = Some(self.start_time.elapsed());
        self
    }

    /// Create a tracing span for this request
    pub fn create_span(&self) -> Span {
        tracing::info_span!(
            "request",
            request_id = %self.request_id,
            client_ip = ?self.client_ip,
            method = ?self.method,
            uri = ?self.uri,
            backend = ?self.backend_address
        )
    }
}

/// Performance metrics for logging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// Request processing time
    pub request_duration: Duration,

    /// Backend response time
    pub backend_duration: Option<Duration>,

    /// Queue wait time
    pub queue_duration: Option<Duration>,

    /// DNS resolution time
    pub dns_duration: Option<Duration>,

    /// TCP connection time
    pub connect_duration: Option<Duration>,

    /// TLS handshake time
    pub tls_duration: Option<Duration>,

    /// Request size in bytes
    pub request_size: Option<u64>,

    /// Response size in bytes
    pub response_size: Option<u64>,

    /// Number of retries
    pub retry_count: u32,
}

impl PerformanceMetrics {
    /// Create new performance metrics
    pub fn new(request_duration: Duration) -> Self {
        Self {
            request_duration,
            backend_duration: None,
            queue_duration: None,
            dns_duration: None,
            connect_duration: None,
            tls_duration: None,
            request_size: None,
            response_size: None,
            retry_count: 0,
        }
    }

    /// Log performance metrics
    pub fn log(&self, context: &RequestContext) {
        info!(
            request_id = %context.request_id,
            request_duration_ms = %self.request_duration.as_millis(),
            backend_duration_ms = ?self.backend_duration.map(|d| d.as_millis()),
            queue_duration_ms = ?self.queue_duration.map(|d| d.as_millis()),
            request_size = ?self.request_size,
            response_size = ?self.response_size,
            retry_count = %self.retry_count,
            "Request performance metrics"
        );

        // Log slow requests
        if self.request_duration > Duration::from_millis(1000) {
            warn!(
                request_id = %context.request_id,
                duration_ms = %self.request_duration.as_millis(),
                "Slow request detected"
            );
        }

        // Log high retry count
        if self.retry_count > 2 {
            warn!(
                request_id = %context.request_id,
                retry_count = %self.retry_count,
                "High retry count detected"
            );
        }
    }
}

/// Security event logging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityEvent {
    /// Event type
    pub event_type: SecurityEventType,

    /// Client IP address
    pub client_ip: Option<SocketAddr>,

    /// User agent
    pub user_agent: Option<String>,

    /// Request details
    pub request_details: Option<String>,

    /// Severity level
    pub severity: SecuritySeverity,

    /// Additional context
    pub context: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityEventType {
    RateLimitExceeded,
    InvalidAuthentication,
    SuspiciousRequest,
    TlsHandshakeFailure,
    AccessDenied,
    PotentialAttack,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecuritySeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl SecurityEvent {
    /// Create a new security event
    pub fn new(event_type: SecurityEventType, severity: SecuritySeverity) -> Self {
        Self {
            event_type,
            client_ip: None,
            user_agent: None,
            request_details: None,
            severity,
            context: HashMap::new(),
        }
    }

    /// Add client information
    pub fn with_client(mut self, ip: SocketAddr, user_agent: Option<String>) -> Self {
        self.client_ip = Some(ip);
        self.user_agent = user_agent;
        self
    }

    /// Add request details
    pub fn with_request_details(mut self, details: String) -> Self {
        self.request_details = Some(details);
        self
    }

    /// Add context information
    pub fn add_context(mut self, key: String, value: String) -> Self {
        self.context.insert(key, value);
        self
    }

    /// Log the security event
    pub fn log(&self) {
        match self.severity {
            SecuritySeverity::Critical => {
                error!(
                    event_type = ?self.event_type,
                    client_ip = ?self.client_ip,
                    user_agent = ?self.user_agent,
                    request_details = ?self.request_details,
                    context = ?self.context,
                    "CRITICAL SECURITY EVENT"
                );
            }
            SecuritySeverity::High => {
                error!(
                    event_type = ?self.event_type,
                    client_ip = ?self.client_ip,
                    user_agent = ?self.user_agent,
                    request_details = ?self.request_details,
                    context = ?self.context,
                    "High severity security event"
                );
            }
            SecuritySeverity::Medium => {
                warn!(
                    event_type = ?self.event_type,
                    client_ip = ?self.client_ip,
                    user_agent = ?self.user_agent,
                    request_details = ?self.request_details,
                    context = ?self.context,
                    "Medium severity security event"
                );
            }
            SecuritySeverity::Low => {
                info!(
                    event_type = ?self.event_type,
                    client_ip = ?self.client_ip,
                    user_agent = ?self.user_agent,
                    request_details = ?self.request_details,
                    context = ?self.context,
                    "Low severity security event"
                );
            }
        }
    }
}

/// Logger configuration and setup
pub struct LoggerConfig {
    /// Log level filter
    pub level: String,

    /// Output format (json, pretty, compact)
    pub format: LogFormat,

    /// Log file path (optional)
    pub file_path: Option<String>,

    /// Enable console output
    pub console_output: bool,

    /// Enable structured logging
    pub structured: bool,

    /// Include source code locations
    pub include_location: bool,

    /// Include thread information
    pub include_thread: bool,
}

#[derive(Debug, Clone)]
pub enum LogFormat {
    Json,
    Pretty,
    Compact,
}

impl LoggerConfig {
    /// Create default logger configuration
    pub fn default() -> Self {
        Self {
            level: "info".to_string(),
            format: LogFormat::Json,
            file_path: None,
            console_output: true,
            structured: true,
            include_location: false,
            include_thread: true,
        }
    }

    /// Initialize the logging system
    pub fn init(&self) -> Result<(), Box<dyn std::error::Error>> {
        let filter = EnvFilter::try_new(&self.level)
            .unwrap_or_else(|_| EnvFilter::new("info"));

        let registry = Registry::default().with(filter);

        match self.format {
            LogFormat::Json => {
                let fmt_layer = fmt::layer()
                    .json()
                    .with_target(true)
                    .with_thread_ids(self.include_thread)
                    .with_file(self.include_location)
                    .with_line_number(self.include_location);

                let subscriber = registry.with(fmt_layer);
                tracing::subscriber::set_global_default(subscriber)?;
            }
            LogFormat::Pretty => {
                let fmt_layer = fmt::layer()
                    .pretty()
                    .with_target(true)
                    .with_thread_ids(self.include_thread)
                    .with_file(self.include_location)
                    .with_line_number(self.include_location);

                let subscriber = registry.with(fmt_layer);
                tracing::subscriber::set_global_default(subscriber)?;
            }
            LogFormat::Compact => {
                let fmt_layer = fmt::layer()
                    .compact()
                    .with_target(true)
                    .with_thread_ids(self.include_thread)
                    .with_file(self.include_location)
                    .with_line_number(self.include_location);

                let subscriber = registry.with(fmt_layer);
                tracing::subscriber::set_global_default(subscriber)?;
            }
        }

        info!("Logging system initialized with level: {}", self.level);
        Ok(())
    }
}
```

## Integration with HTTP Proxy

Update `src/proxy/http_proxy.rs` to include comprehensive error handling and logging:

```rust
use crate::error::{LoadBalancerError, ErrorHandler, ErrorContext, Result};
use crate::logging::{RequestContext, PerformanceMetrics, SecurityEvent, SecurityEventType, SecuritySeverity};
use hyper::{Body, Request, Response, StatusCode};
use std::net::SocketAddr;
use std::time::Instant;
use tracing::{info, warn, error, debug, Instrument};

/// Enhanced HTTP proxy with comprehensive error handling and logging
pub struct HttpProxyWithLogging {
    backend_pool: Arc<BackendPool>,
    error_handler: ErrorHandler,
    config: ProxyConfig,
}

impl HttpProxyWithLogging {
    /// Create a new HTTP proxy with logging
    pub fn new(backend_pool: Arc<BackendPool>, config: ProxyConfig) -> Self {
        Self {
            backend_pool,
            error_handler: ErrorHandler::new("http_proxy"),
            config,
        }
    }

    /// Handle HTTP request with comprehensive logging and error handling
    pub async fn handle_request(
        &self,
        req: Request<Body>,
        client_addr: SocketAddr,
    ) -> Result<Response<Body>> {
        // Create request context for tracing
        let mut context = RequestContext::new()
            .with_client_info(client_addr,
                req.headers().get("user-agent")
                    .and_then(|h| h.to_str().ok())
                    .map(|s| s.to_string()))
            .with_http_info(req.method().to_string(), req.uri().to_string());

        // Add relevant headers to context
        for (name, value) in req.headers() {
            if let Ok(value_str) = value.to_str() {
                context.add_header(name.to_string(), value_str.to_string());
            }
        }

        let span = context.create_span();

        // Process request within the tracing span
        self.process_request_with_context(req, context)
            .instrument(span)
            .await
    }

    async fn process_request_with_context(
        &self,
        req: Request<Body>,
        mut context: RequestContext,
    ) -> Result<Response<Body>> {
        let start_time = Instant::now();

        // Validate request
        if let Err(validation_error) = self.validate_request(&req, &context) {
            self.error_handler.handle_error(&validation_error, Some(&ErrorContext::new("http_proxy", "request_validation")));
            return Ok(self.create_error_response(StatusCode::BAD_REQUEST, "Invalid request"));
        }

        // Check rate limiting
        if let Err(rate_limit_error) = self.check_rate_limit(&context) {
            // Log security event
            SecurityEvent::new(SecurityEventType::RateLimitExceeded, SecuritySeverity::Medium)
                .with_client(context.client_ip.unwrap(), context.user_agent.clone())
                .log();

            self.error_handler.handle_error(&rate_limit_error, Some(&ErrorContext::new("http_proxy", "rate_limiting")));
            return Ok(self.create_error_response(StatusCode::TOO_MANY_REQUESTS, "Rate limit exceeded"));
        }

        // Select backend server
        let backend_addr = match self.backend_pool.get_next_backend() {
            Some(addr) => {
                context = context.with_backend(addr);
                debug!(backend_address = %addr, "Selected backend server");
                addr
            }
            None => {
                let error = LoadBalancerError::Backend {
                    message: "No healthy backends available".to_string(),
                    backend_address: "0.0.0.0:0".parse().unwrap(),
                    status_code: None,
                    response_time: None,
                    error_type: crate::error::BackendErrorType::ServiceUnavailable,
                };

                self.error_handler.handle_error(&error, Some(&ErrorContext::new("http_proxy", "backend_selection")));
                return Ok(self.create_error_response(StatusCode::SERVICE_UNAVAILABLE, "No backends available"));
            }
        };

        // Forward request to backend with retry logic
        let mut retry_count = 0;
        let max_retries = self.config.max_retries;

        loop {
            let backend_start = Instant::now();

            match self.forward_to_backend(&req, backend_addr).await {
                Ok(response) => {
                    let backend_duration = backend_start.elapsed();
                    let total_duration = start_time.elapsed();

                    // Update context with response information
                    context = context.with_response(
                        response.status().as_u16(),
                        response.headers().get("content-length")
                            .and_then(|h| h.to_str().ok())
                            .and_then(|s| s.parse().ok())
                    );

                    // Log performance metrics
                    let mut metrics = PerformanceMetrics::new(total_duration);
                    metrics.backend_duration = Some(backend_duration);
                    metrics.retry_count = retry_count;
                    metrics.log(&context);

                    // Log successful request
                    info!(
                        request_id = %context.request_id,
                        method = ?context.method,
                        uri = ?context.uri,
                        status_code = %response.status().as_u16(),
                        backend_address = %backend_addr,
                        duration_ms = %total_duration.as_millis(),
                        retry_count = %retry_count,
                        "Request completed successfully"
                    );

                    return Ok(response);
                }
                Err(backend_error) => {
                    retry_count += 1;

                    // Log backend error
                    warn!(
                        request_id = %context.request_id,
                        backend_address = %backend_addr,
                        retry_count = %retry_count,
                        error = %backend_error,
                        "Backend request failed"
                    );

                    // Check if we should retry
                    if retry_count >= max_retries || !self.should_retry(&backend_error) {
                        // Log final failure
                        let error_context = ErrorContext::new("http_proxy", "backend_request")
                            .with_request_id(context.request_id.clone());

                        self.error_handler.handle_error(&backend_error, Some(&error_context));

                        // Log performance metrics for failed request
                        let total_duration = start_time.elapsed();
                        let mut metrics = PerformanceMetrics::new(total_duration);
                        metrics.retry_count = retry_count;
                        metrics.log(&context);

                        return Ok(self.create_error_response(
                            StatusCode::BAD_GATEWAY,
                            "Backend service unavailable"
                        ));
                    }

                    // Wait before retry (exponential backoff)
                    let backoff_duration = std::time::Duration::from_millis(100 * (1 << retry_count));
                    tokio::time::sleep(backoff_duration).await;

                    // Try to select a different backend for retry
                    if let Some(new_backend) = self.backend_pool.get_next_backend() {
                        if new_backend != backend_addr {
                            debug!(
                                old_backend = %backend_addr,
                                new_backend = %new_backend,
                                "Switching to different backend for retry"
                            );
                            // Update backend_addr for next iteration
                            // Note: In a real implementation, you'd need to make backend_addr mutable
                        }
                    }
                }
            }
        }
    }

    /// Validate incoming request
    fn validate_request(&self, req: &Request<Body>, context: &RequestContext) -> Result<()> {
        // Check HTTP method
        match req.method() {
            &hyper::Method::GET | &hyper::Method::POST | &hyper::Method::PUT |
            &hyper::Method::DELETE | &hyper::Method::HEAD | &hyper::Method::OPTIONS => {
                // Valid methods
            }
            _ => {
                return Err(LoadBalancerError::Http {
                    message: format!("Unsupported HTTP method: {}", req.method()),
                    status_code: 405,
                    method: Some(req.method().to_string()),
                    uri: Some(req.uri().to_string()),
                    headers: None,
                });
            }
        }

        // Check URI length
        if req.uri().to_string().len() > 8192 {
            return Err(LoadBalancerError::Http {
                message: "URI too long".to_string(),
                status_code: 414,
                method: Some(req.method().to_string()),
                uri: Some(req.uri().to_string()),
                headers: None,
            });
        }

        // Check for suspicious patterns
        let uri_str = req.uri().to_string();
        let suspicious_patterns = [
            "../", "..\\", "<script", "javascript:", "data:", "vbscript:",
            "onload=", "onerror=", "eval(", "alert(", "document.cookie"
        ];

        for pattern in &suspicious_patterns {
            if uri_str.to_lowercase().contains(pattern) {
                // Log security event
                SecurityEvent::new(SecurityEventType::SuspiciousRequest, SecuritySeverity::High)
                    .with_client(context.client_ip.unwrap(), context.user_agent.clone())
                    .with_request_details(format!("Suspicious pattern '{}' in URI: {}", pattern, uri_str))
                    .log();

                return Err(LoadBalancerError::Http {
                    message: "Suspicious request pattern detected".to_string(),
                    status_code: 400,
                    method: Some(req.method().to_string()),
                    uri: Some(req.uri().to_string()),
                    headers: None,
                });
            }
        }

        Ok(())
    }

    /// Check rate limiting for the request
    fn check_rate_limit(&self, context: &RequestContext) -> Result<()> {
        // In a real implementation, you'd check against a rate limiter
        // For now, we'll simulate rate limiting logic

        if let Some(client_ip) = context.client_ip {
            // Simulate rate limit check
            // This would typically involve checking a distributed cache or database
            debug!(client_ip = %client_ip, "Checking rate limit");

            // For demonstration, we'll allow all requests
            // In production, you'd implement actual rate limiting logic here
        }

        Ok(())
    }

    /// Forward request to backend server
    async fn forward_to_backend(
        &self,
        req: &Request<Body>,
        backend_addr: SocketAddr,
    ) -> Result<Response<Body>> {
        // In a real implementation, you'd use an HTTP client to forward the request
        // For now, we'll simulate the backend response

        // Simulate network delay
        tokio::time::sleep(std::time::Duration::from_millis(50)).await;

        // Simulate occasional backend errors for testing
        use rand::Rng;
        let mut rng = rand::thread_rng();
        if rng.gen_range(0..100) < 5 { // 5% error rate
            return Err(LoadBalancerError::Backend {
                message: "Simulated backend error".to_string(),
                backend_address: backend_addr,
                status_code: Some(500),
                response_time: Some(std::time::Duration::from_millis(50)),
                error_type: crate::error::BackendErrorType::ServerError,
            });
        }

        // Create successful response
        Ok(Response::builder()
            .status(StatusCode::OK)
            .header("content-type", "application/json")
            .header("x-backend-server", backend_addr.to_string())
            .body(Body::from(r#"{"status": "success", "message": "Hello from backend!"}"#))
            .unwrap())
    }

    /// Determine if an error should trigger a retry
    fn should_retry(&self, error: &LoadBalancerError) -> bool {
        match error {
            LoadBalancerError::Backend { error_type, .. } => {
                match error_type {
                    crate::error::BackendErrorType::ConnectionRefused => true,
                    crate::error::BackendErrorType::ConnectionTimeout => true,
                    crate::error::BackendErrorType::ReadTimeout => true,
                    crate::error::BackendErrorType::ServiceUnavailable => true,
                    crate::error::BackendErrorType::ServerError => false, // Don't retry 5xx errors
                    crate::error::BackendErrorType::InvalidResponse => false,
                    crate::error::BackendErrorType::WriteTimeout => true,
                    crate::error::BackendErrorType::TooManyRequests => false,
                }
            }
            LoadBalancerError::Network { .. } => true,
            LoadBalancerError::Timeout { .. } => true,
            _ => false,
        }
    }

    /// Create an error response
    fn create_error_response(&self, status: StatusCode, message: &str) -> Response<Body> {
        let error_body = serde_json::json!({
            "error": {
                "code": status.as_u16(),
                "message": message,
                "timestamp": chrono::Utc::now().to_rfc3339()
            }
        });

        Response::builder()
            .status(status)
            .header("content-type", "application/json")
            .header("x-error-source", "rusty-load-balancer")
            .body(Body::from(error_body.to_string()))
            .unwrap()
    }
}
```

## Testing Error Handling and Logging

Create `tests/error_handling_tests.rs`:

```rust
use rusty_balancer::error::{LoadBalancerError, ErrorHandler, ErrorContext};
use rusty_balancer::logging::{RequestContext, SecurityEvent, SecurityEventType, SecuritySeverity};
use std::net::SocketAddr;
use std::time::Duration;

#[test]
fn test_error_creation_and_display() {
    let backend_addr: SocketAddr = "127.0.0.1:8080".parse().unwrap();

    let error = LoadBalancerError::Backend {
        message: "Connection refused".to_string(),
        backend_address: backend_addr,
        status_code: None,
        response_time: Some(Duration::from_millis(100)),
        error_type: rusty_balancer::error::BackendErrorType::ConnectionRefused,
    };

    let error_string = error.to_string();
    assert!(error_string.contains("Backend error"));
    assert!(error_string.contains("127.0.0.1:8080"));
    assert!(error_string.contains("ConnectionRefused"));
}

#[test]
fn test_error_macros() {
    let config_error = rusty_balancer::config_error!("Invalid port number", "server.port", "99999");

    match config_error {
        LoadBalancerError::Configuration { message, field, value } => {
            assert_eq!(message, "Invalid port number");
            assert_eq!(field, Some("server.port".to_string()));
            assert_eq!(value, Some("99999".to_string()));
        }
        _ => panic!("Expected configuration error"),
    }

    let backend_addr: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    let backend_error = rusty_balancer::backend_error!(
        "Connection timeout",
        backend_addr,
        rusty_balancer::error::BackendErrorType::ConnectionTimeout
    );

    match backend_error {
        LoadBalancerError::Backend { message, backend_address, error_type, .. } => {
            assert_eq!(message, "Connection timeout");
            assert_eq!(backend_address, backend_addr);
            assert!(matches!(error_type, rusty_balancer::error::BackendErrorType::ConnectionTimeout));
        }
        _ => panic!("Expected backend error"),
    }
}

#[test]
fn test_error_context() {
    let context = ErrorContext::new("http_proxy", "request_handling")
        .with_request_id("req-123".to_string())
        .with_client_ip("*************".to_string());

    assert_eq!(context.component, "http_proxy");
    assert_eq!(context.operation, "request_handling");
    assert_eq!(context.request_id, Some("req-123".to_string()));
    assert_eq!(context.client_ip, Some("*************".to_string()));
}

#[test]
fn test_request_context() {
    let mut context = RequestContext::new();

    // Test that request ID is generated
    assert!(!context.request_id.is_empty());

    // Test adding client info
    let client_addr: SocketAddr = "*************:12345".parse().unwrap();
    context = context.with_client_info(client_addr, Some("test-agent/1.0".to_string()));

    assert_eq!(context.client_ip, Some(client_addr));
    assert_eq!(context.user_agent, Some("test-agent/1.0".to_string()));

    // Test adding HTTP info
    context = context.with_http_info("GET".to_string(), "/api/test".to_string());

    assert_eq!(context.method, Some("GET".to_string()));
    assert_eq!(context.uri, Some("/api/test".to_string()));

    // Test header filtering
    context.add_header("content-type".to_string(), "application/json".to_string());
    context.add_header("authorization".to_string(), "Bearer secret-token".to_string());

    assert!(context.headers.contains_key("content-type"));
    assert!(!context.headers.contains_key("authorization")); // Should be filtered
}

#[test]
fn test_security_event_logging() {
    let client_addr: SocketAddr = "*************:12345".parse().unwrap();

    let event = SecurityEvent::new(
        SecurityEventType::RateLimitExceeded,
        SecuritySeverity::Medium
    )
    .with_client(client_addr, Some("suspicious-bot/1.0".to_string()))
    .with_request_details("Exceeded 100 requests per minute".to_string())
    .add_context("rate_limit".to_string(), "100/min".to_string());

    // Test that event is properly constructed
    assert!(matches!(event.event_type, SecurityEventType::RateLimitExceeded));
    assert!(matches!(event.severity, SecuritySeverity::Medium));
    assert_eq!(event.client_ip, Some(client_addr));
    assert_eq!(event.user_agent, Some("suspicious-bot/1.0".to_string()));
    assert!(event.context.contains_key("rate_limit"));

    // In a real test, you'd capture the log output and verify it
    event.log();
}

#[tokio::test]
async fn test_error_handler() {
    let error_handler = ErrorHandler::new("test_component");

    let backend_addr: SocketAddr = "127.0.0.1:8080".parse().unwrap();
    let error = LoadBalancerError::Backend {
        message: "Test backend error".to_string(),
        backend_address: backend_addr,
        status_code: Some(500),
        response_time: Some(Duration::from_millis(200)),
        error_type: rusty_balancer::error::BackendErrorType::ServerError,
    };

    let context = ErrorContext::new("test_component", "test_operation")
        .with_request_id("test-req-123".to_string());

    // This should log the error and update metrics
    error_handler.handle_error(&error, Some(&context));

    // In a real test, you'd verify that:
    // 1. The error was logged with correct level
    // 2. Metrics were updated
    // 3. Alerts were triggered if necessary
}

#[test]
fn test_performance_metrics() {
    let context = RequestContext::new();
    let mut metrics = rusty_balancer::logging::PerformanceMetrics::new(Duration::from_millis(150));

    metrics.backend_duration = Some(Duration::from_millis(100));
    metrics.queue_duration = Some(Duration::from_millis(20));
    metrics.retry_count = 1;
    metrics.request_size = Some(1024);
    metrics.response_size = Some(2048);

    // Test logging (in real test, you'd capture and verify log output)
    metrics.log(&context);

    // Verify metrics are properly set
    assert_eq!(metrics.request_duration, Duration::from_millis(150));
    assert_eq!(metrics.backend_duration, Some(Duration::from_millis(100)));
    assert_eq!(metrics.retry_count, 1);
}

#[test]
fn test_logger_config() {
    let config = rusty_balancer::logging::LoggerConfig::default();

    assert_eq!(config.level, "info");
    assert!(matches!(config.format, rusty_balancer::logging::LogFormat::Json));
    assert!(config.console_output);
    assert!(config.structured);

    // Test initialization (in a real test environment)
    // config.init().expect("Failed to initialize logger");
}

#[test]
fn test_error_serialization() {
    let error = LoadBalancerError::Configuration {
        message: "Test error".to_string(),
        field: Some("test_field".to_string()),
        value: Some("test_value".to_string()),
    };

    // Test that errors can be serialized (useful for logging and metrics)
    let serialized = serde_json::to_string(&error).expect("Failed to serialize error");
    assert!(serialized.contains("Test error"));
    assert!(serialized.contains("test_field"));

    // Test deserialization
    let deserialized: LoadBalancerError = serde_json::from_str(&serialized)
        .expect("Failed to deserialize error");

    match deserialized {
        LoadBalancerError::Configuration { message, field, value } => {
            assert_eq!(message, "Test error");
            assert_eq!(field, Some("test_field".to_string()));
            assert_eq!(value, Some("test_value".to_string()));
        }
        _ => panic!("Deserialized to wrong error type"),
    }
}
```

## Example Usage in Main Application

Update `src/main.rs` to demonstrate comprehensive error handling:

```rust
use rusty_balancer::error::{LoadBalancerError, ErrorHandler};
use rusty_balancer::logging::{LoggerConfig, LogFormat, SecurityEvent, SecurityEventType, SecuritySeverity};
use rusty_balancer::proxy::HttpProxyWithLogging;
use std::net::SocketAddr;
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize comprehensive logging
    let logger_config = LoggerConfig {
        level: "info".to_string(),
        format: LogFormat::Json,
        file_path: Some("/var/log/rusty-balancer/app.log".to_string()),
        console_output: true,
        structured: true,
        include_location: true,
        include_thread: true,
    };

    logger_config.init()?;
    info!("🚀 Rusty Load Balancer starting with comprehensive logging");

    // Create error handler for main application
    let error_handler = ErrorHandler::new("main");

    // Example of handling configuration errors
    match load_configuration() {
        Ok(config) => {
            info!("✅ Configuration loaded successfully");

            // Start the load balancer with the configuration
            if let Err(e) = start_load_balancer(config).await {
                error_handler.handle_error(&e, None);
                return Err(e.into());
            }
        }
        Err(config_error) => {
            error_handler.handle_error(&config_error, None);
            return Err(config_error.into());
        }
    }

    Ok(())
}

fn load_configuration() -> rusty_balancer::error::Result<rusty_balancer::config::Config> {
    // Simulate configuration loading with potential errors
    use rand::Rng;
    let mut rng = rand::thread_rng();

    if rng.gen_range(0..10) < 2 { // 20% chance of config error
        return Err(rusty_balancer::config_error!(
            "Invalid backend configuration",
            "backends.servers",
            "empty array"
        ));
    }

    Ok(rusty_balancer::config::Config::default())
}

async fn start_load_balancer(config: rusty_balancer::config::Config) -> rusty_balancer::error::Result<()> {
    let bind_addr = config.server.bind_address;

    info!("🌐 Starting load balancer on {}", bind_addr);

    // Example of logging security events
    SecurityEvent::new(SecurityEventType::AccessDenied, SecuritySeverity::Low)
        .with_request_details("Load balancer startup".to_string())
        .add_context("event".to_string(), "startup".to_string())
        .log();

    // Simulate running the load balancer
    tokio::time::sleep(std::time::Duration::from_secs(1)).await;

    info!("✅ Load balancer started successfully");
    Ok(())
}
```

## Key Design Decisions Explained

### 1. Structured Error Types
**Decision**: Use enums with rich context instead of simple string errors

**Rationale**:
- **Programmatic Handling**: Code can react differently to different error types
- **Debugging**: Rich context information helps with troubleshooting
- **Monitoring**: Structured data enables automated analysis and alerting
- **Consistency**: Standardized error format across the entire system

### 2. Error Propagation Strategy
**Decision**: Use `Result<T, LoadBalancerError>` throughout the codebase

**Rationale**:
- **Explicit Error Handling**: Forces developers to handle errors explicitly
- **Type Safety**: Compile-time guarantees about error handling
- **Performance**: Zero-cost abstractions for error handling
- **Composability**: Easy to chain operations with `?` operator

### 3. Contextual Logging
**Decision**: Include request context and correlation IDs in all logs

**Rationale**:
- **Distributed Tracing**: Track requests across multiple services
- **Debugging**: Correlate logs from different components
- **Performance Analysis**: Understand request flow and bottlenecks
- **Security**: Track suspicious activity across requests

### 4. Security Event Logging
**Decision**: Separate security events from general application logs

**Rationale**:
- **Security Monitoring**: Dedicated security information and event management (SIEM)
- **Compliance**: Meet regulatory requirements for security logging
- **Alerting**: Different alerting rules for security vs operational events
- **Analysis**: Specialized tools for security event analysis

## Performance Considerations

1. **Logging Overhead**: Structured logging with JSON formatting has minimal overhead
2. **Error Creation**: Rich error types are created only when errors occur
3. **Context Propagation**: Request context is passed by reference to avoid cloning
4. **Async Logging**: Non-blocking logging to avoid impacting request processing

## Security Considerations

1. **Information Disclosure**: Error messages to clients are sanitized
2. **Sensitive Data**: Headers and request data are filtered before logging
3. **Audit Trail**: All security events are logged with sufficient detail
4. **Rate Limiting**: Security events include rate limiting to prevent log flooding

## Navigation
- [Previous: Configuration System](08-configuration-system.md)
- [Next: Metrics and Monitoring](10-metrics-monitoring.md)
