# Module 12: Hot Reloading

## Learning Objectives
- Implement zero-downtime configuration updates and hot reloading
- Design graceful configuration transitions without dropping connections
- Build configuration validation and rollback mechanisms
- Create real-time configuration management and monitoring
- Understand hot reloading patterns and best practices for production systems

## Why Hot Reloading is Critical

Hot reloading enables production systems to adapt without service interruption:

1. **Zero Downtime**: Update configuration without stopping the service
2. **Operational Agility**: Respond quickly to changing requirements
3. **Cost Efficiency**: Avoid downtime costs and maintenance windows
4. **User Experience**: Seamless service for end users
5. **DevOps Integration**: Enable continuous deployment and GitOps workflows

### Hot Reloading Architecture

```mermaid
graph TD
    Config[Configuration Source] -->|File Watch| Watcher[Configuration Watcher]
    Config -->|API Update| API[Management API]
    
    Watcher -->|Change Detected| Validator[Configuration Validator]
    API -->|New Config| Validator
    
    Validator -->|Valid| Applier[Configuration Applier]
    Validator -->|Invalid| Reject[Reject & Log Error]
    
    Applier -->|Gradual Update| LB[Load Balancer]
    Applier -->|Rollback on Error| Rollback[Rollback Manager]
    
    subgraph "Hot Reload Components"
        Watcher
        Validator
        Applier
        Rollback
    end
    
    subgraph "Load Balancer State"
        Current[Current Config]
        Pending[Pending Config]
        Backup[Backup Config]
    end
    
    LB --> Current
    Applier --> Pending
    Rollback --> Backup
    
    subgraph "Monitoring"
        Metrics[Reload Metrics]
        Alerts[Reload Alerts]
        Audit[Audit Log]
    end
    
    Applier --> Metrics
    Validator --> Alerts
    Applier --> Audit
```

## Hot Reloading Implementation

### Design Decisions

**Why gradual configuration updates?**
- **Safety**: Validate changes before full application
- **Rollback**: Easy to revert if issues are detected
- **Monitoring**: Observe impact before complete transition
- **Risk Mitigation**: Minimize blast radius of configuration errors

**Why configuration validation pipeline?**
- **Reliability**: Prevent invalid configurations from being applied
- **Consistency**: Ensure configuration integrity and dependencies
- **Security**: Validate security-related configuration changes
- **Compliance**: Enforce organizational policies and standards

Create `src/reload/mod.rs`:

```rust
//! Hot reloading system for zero-downtime configuration updates
//!
//! This module provides comprehensive hot reloading capabilities including:
//! - Configuration file watching and change detection
//! - Gradual configuration application with validation
//! - Automatic rollback on errors
//! - Configuration versioning and audit trails
//! - Real-time monitoring and alerting

pub mod watcher;
pub mod validator;
pub mod applier;
pub mod rollback;

use crate::config::Config;
use crate::error::{LoadBalancerError, Result};
use crate::metrics::LoadBalancerMetrics;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, mpsc};
use tracing::{info, warn, error, debug};
use uuid::Uuid;

/// Hot reload manager for coordinating configuration updates
pub struct HotReloadManager {
    /// Current active configuration
    current_config: Arc<RwLock<Config>>,
    
    /// Configuration validator
    validator: Arc<ConfigValidator>,
    
    /// Configuration applier
    applier: Arc<ConfigApplier>,
    
    /// Rollback manager
    rollback_manager: Arc<RollbackManager>,
    
    /// Configuration change channel
    change_sender: mpsc::UnboundedSender<ConfigChange>,
    change_receiver: Arc<RwLock<mpsc::UnboundedReceiver<ConfigChange>>>,
    
    /// Metrics collector
    metrics: Option<Arc<LoadBalancerMetrics>>,
    
    /// Hot reload configuration
    config: HotReloadConfig,
}

/// Hot reload configuration
#[derive(Debug, Clone)]
pub struct HotReloadConfig {
    /// Enable hot reloading
    pub enabled: bool,
    
    /// Configuration file paths to watch
    pub watch_paths: Vec<PathBuf>,
    
    /// Validation timeout
    pub validation_timeout: Duration,
    
    /// Application timeout
    pub application_timeout: Duration,
    
    /// Rollback timeout
    pub rollback_timeout: Duration,
    
    /// Maximum rollback attempts
    pub max_rollback_attempts: u32,
    
    /// Enable gradual rollout
    pub enable_gradual_rollout: bool,
    
    /// Gradual rollout percentage steps
    pub rollout_steps: Vec<u8>,
    
    /// Step duration for gradual rollout
    pub step_duration: Duration,
}

/// Configuration change event
#[derive(Debug, Clone)]
pub struct ConfigChange {
    /// Unique change ID
    pub id: String,
    
    /// New configuration
    pub new_config: Config,
    
    /// Change source
    pub source: ChangeSource,
    
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    
    /// Change metadata
    pub metadata: HashMap<String, String>,
}

/// Configuration change source
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChangeSource {
    FileWatch { path: PathBuf },
    ManagementApi { user: String, ip: String },
    AutomaticRenewal { component: String },
    HealthCheck { reason: String },
}

/// Configuration validation result
#[derive(Debug, Clone)]
pub struct ValidationResult {
    /// Validation success
    pub success: bool,
    
    /// Validation errors
    pub errors: Vec<String>,
    
    /// Validation warnings
    pub warnings: Vec<String>,
    
    /// Validation duration
    pub duration: Duration,
    
    /// Validated configuration
    pub validated_config: Option<Config>,
}

/// Configuration application result
#[derive(Debug, Clone)]
pub struct ApplicationResult {
    /// Application success
    pub success: bool,
    
    /// Application errors
    pub errors: Vec<String>,
    
    /// Application duration
    pub duration: Duration,
    
    /// Rollback required
    pub rollback_required: bool,
}

impl HotReloadManager {
    /// Create a new hot reload manager
    pub fn new(
        initial_config: Config,
        config: HotReloadConfig,
    ) -> Result<Self> {
        let (change_sender, change_receiver) = mpsc::unbounded_channel();
        
        let current_config = Arc::new(RwLock::new(initial_config.clone()));
        let validator = Arc::new(ConfigValidator::new());
        let applier = Arc::new(ConfigApplier::new());
        let rollback_manager = Arc::new(RollbackManager::new(initial_config));
        
        info!("🔄 Hot reload manager initialized");
        
        Ok(Self {
            current_config,
            validator,
            applier,
            rollback_manager,
            change_sender,
            change_receiver: Arc::new(RwLock::new(change_receiver)),
            metrics: None,
            config,
        })
    }
    
    /// Set metrics collector
    pub fn with_metrics(mut self, metrics: Arc<LoadBalancerMetrics>) -> Self {
        self.metrics = Some(metrics);
        self
    }
    
    /// Start hot reloading system
    pub async fn start(&self) -> Result<()> {
        if !self.config.enabled {
            info!("Hot reloading is disabled");
            return Ok(());
        }
        
        info!("🚀 Starting hot reload system");
        
        // Start file watchers
        for path in &self.config.watch_paths {
            self.start_file_watcher(path.clone()).await?;
        }
        
        // Start change processor
        self.start_change_processor().await;
        
        info!("✅ Hot reload system started successfully");
        Ok(())
    }
    
    /// Start file watcher for a configuration path
    async fn start_file_watcher(&self, path: PathBuf) -> Result<()> {
        let change_sender = self.change_sender.clone();
        
        tokio::spawn(async move {
            use notify::{Watcher, RecursiveMode, watcher, DebouncedEvent};
            use std::sync::mpsc;
            
            let (tx, rx) = mpsc::channel();
            
            let mut watcher = match watcher(tx, Duration::from_secs(1)) {
                Ok(w) => w,
                Err(e) => {
                    error!("Failed to create file watcher for {:?}: {}", path, e);
                    return;
                }
            };
            
            if let Err(e) = watcher.watch(&path, RecursiveMode::NonRecursive) {
                error!("Failed to watch file {:?}: {}", path, e);
                return;
            }
            
            info!("👁️ Watching configuration file: {:?}", path);
            
            loop {
                match rx.recv() {
                    Ok(DebouncedEvent::Write(_)) | Ok(DebouncedEvent::Create(_)) => {
                        debug!("Configuration file changed: {:?}", path);
                        
                        // Load new configuration
                        match Self::load_config_from_file(&path).await {
                            Ok(new_config) => {
                                let change = ConfigChange {
                                    id: Uuid::new_v4().to_string(),
                                    new_config,
                                    source: ChangeSource::FileWatch { path: path.clone() },
                                    timestamp: chrono::Utc::now(),
                                    metadata: HashMap::new(),
                                };
                                
                                if let Err(e) = change_sender.send(change) {
                                    error!("Failed to send configuration change: {}", e);
                                }
                            }
                            Err(e) => {
                                error!("Failed to load configuration from {:?}: {}", path, e);
                            }
                        }
                    }
                    Ok(DebouncedEvent::Remove(_)) => {
                        warn!("Configuration file removed: {:?}", path);
                    }
                    Ok(_) => {
                        // Ignore other events
                    }
                    Err(e) => {
                        error!("File watcher error for {:?}: {}", path, e);
                        break;
                    }
                }
            }
        });
        
        Ok(())
    }
    
    /// Start configuration change processor
    async fn start_change_processor(&self) {
        let current_config = self.current_config.clone();
        let validator = self.validator.clone();
        let applier = self.applier.clone();
        let rollback_manager = self.rollback_manager.clone();
        let metrics = self.metrics.clone();
        let config = self.config.clone();
        
        let mut receiver = self.change_receiver.write().await;
        
        tokio::spawn(async move {
            while let Some(change) = receiver.recv().await {
                info!("📝 Processing configuration change: {}", change.id);
                
                let start_time = Instant::now();
                
                // Validate configuration
                let validation_result = validator.validate(&change.new_config, &config).await;
                
                if !validation_result.success {
                    error!("❌ Configuration validation failed for change {}: {:?}", 
                           change.id, validation_result.errors);
                    
                    if let Some(ref metrics) = metrics {
                        metrics.record_error("config_validation_failed", "hot_reload");
                    }
                    continue;
                }
                
                if !validation_result.warnings.is_empty() {
                    warn!("⚠️ Configuration validation warnings for change {}: {:?}", 
                          change.id, validation_result.warnings);
                }
                
                // Apply configuration
                let application_result = applier.apply(
                    &change.new_config,
                    &current_config,
                    &config,
                ).await;
                
                if application_result.success {
                    // Update current configuration
                    {
                        let mut current = current_config.write().await;
                        *current = change.new_config.clone();
                    }
                    
                    // Save backup for rollback
                    rollback_manager.save_backup(&change.new_config).await;
                    
                    let total_duration = start_time.elapsed();
                    
                    info!("✅ Configuration change {} applied successfully in {:?}", 
                          change.id, total_duration);
                    
                    if let Some(ref metrics) = metrics {
                        // Record successful reload metrics
                        metrics.record_request(
                            "CONFIG_RELOAD",
                            200,
                            "hot_reload",
                            total_duration,
                            None,
                            None,
                        );
                    }
                } else {
                    error!("❌ Configuration application failed for change {}: {:?}", 
                           change.id, application_result.errors);
                    
                    if application_result.rollback_required {
                        warn!("🔄 Initiating rollback for change {}", change.id);
                        
                        if let Err(e) = rollback_manager.rollback(&current_config, &config).await {
                            error!("💥 Rollback failed for change {}: {}", change.id, e);
                        } else {
                            info!("✅ Rollback completed for change {}", change.id);
                        }
                    }
                    
                    if let Some(ref metrics) = metrics {
                        metrics.record_error("config_application_failed", "hot_reload");
                    }
                }
            }
        });
    }
    
    /// Load configuration from file
    async fn load_config_from_file(path: &PathBuf) -> Result<Config> {
        let content = tokio::fs::read_to_string(path).await
            .map_err(|e| LoadBalancerError::Configuration {
                message: format!("Failed to read config file: {}", e),
                field: Some("file_path".to_string()),
                value: Some(path.to_string_lossy().to_string()),
            })?;
        
        let config: Config = if path.extension().and_then(|s| s.to_str()) == Some("json") {
            serde_json::from_str(&content)
        } else {
            serde_yaml::from_str(&content)
        }.map_err(|e| LoadBalancerError::Configuration {
            message: format!("Failed to parse config file: {}", e),
            field: Some("file_content".to_string()),
            value: None,
        })?;
        
        Ok(config)
    }
    
    /// Trigger manual configuration reload
    pub async fn reload_config(&self, new_config: Config, source: ChangeSource) -> Result<()> {
        let change = ConfigChange {
            id: Uuid::new_v4().to_string(),
            new_config,
            source,
            timestamp: chrono::Utc::now(),
            metadata: HashMap::new(),
        };
        
        self.change_sender.send(change)
            .map_err(|e| LoadBalancerError::Internal {
                message: format!("Failed to trigger configuration reload: {}", e),
                component: "hot_reload".to_string(),
                error_id: Uuid::new_v4().to_string(),
                context: None,
            })?;
        
        Ok(())
    }
    
    /// Get current configuration
    pub async fn get_current_config(&self) -> Config {
        self.current_config.read().await.clone()
    }
    
    /// Get reload statistics
    pub async fn get_reload_stats(&self) -> ReloadStats {
        // In a real implementation, you'd track these statistics
        ReloadStats {
            total_reloads: 0,
            successful_reloads: 0,
            failed_reloads: 0,
            rollbacks: 0,
            last_reload: None,
            average_reload_time: Duration::from_secs(0),
        }
    }
}

/// Reload statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReloadStats {
    pub total_reloads: u64,
    pub successful_reloads: u64,
    pub failed_reloads: u64,
    pub rollbacks: u64,
    pub last_reload: Option<chrono::DateTime<chrono::Utc>>,
    pub average_reload_time: Duration,
}

/// Configuration validator
pub struct ConfigValidator {
    // Validation rules and dependencies
}

impl ConfigValidator {
    fn new() -> Self {
        Self {}
    }
    
    async fn validate(&self, config: &Config, _reload_config: &HotReloadConfig) -> ValidationResult {
        let start_time = Instant::now();
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        
        // Validate server configuration
        if config.server.max_connections == 0 {
            errors.push("server.max_connections must be greater than 0".to_string());
        }
        
        if config.server.max_connections > 100000 {
            warnings.push("server.max_connections is very high, consider performance implications".to_string());
        }
        
        // Validate backend configuration
        if config.backends.servers.is_empty() {
            warnings.push("No backend servers configured".to_string());
        }
        
        for (i, server) in config.backends.servers.iter().enumerate() {
            if server.weight == 0 {
                errors.push(format!("Backend server {} weight must be greater than 0", i));
            }
            
            if server.weight > 100 {
                warnings.push(format!("Backend server {} weight is very high", i));
            }
        }
        
        // Validate health check configuration
        if config.health_checks.enabled {
            if config.health_checks.timeout >= config.health_checks.interval {
                errors.push("health_check_timeout must be less than health_check_interval".to_string());
            }
        }
        
        let duration = start_time.elapsed();
        let success = errors.is_empty();
        
        ValidationResult {
            success,
            errors,
            warnings,
            duration,
            validated_config: if success { Some(config.clone()) } else { None },
        }
    }
}

/// Configuration applier
pub struct ConfigApplier {
    // Application state and dependencies
}

impl ConfigApplier {
    fn new() -> Self {
        Self {}
    }
    
    async fn apply(
        &self,
        new_config: &Config,
        current_config: &Arc<RwLock<Config>>,
        reload_config: &HotReloadConfig,
    ) -> ApplicationResult {
        let start_time = Instant::now();
        let mut errors = Vec::new();
        
        // Simulate configuration application
        if reload_config.enable_gradual_rollout {
            // Gradual rollout implementation
            for &percentage in &reload_config.rollout_steps {
                debug!("Applying configuration to {}% of traffic", percentage);
                
                // Simulate gradual application
                tokio::time::sleep(reload_config.step_duration).await;
                
                // Check for errors during gradual rollout
                if percentage == 50 && new_config.server.max_connections > 50000 {
                    errors.push("High connection limit detected during rollout".to_string());
                    break;
                }
            }
        } else {
            // Immediate application
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
        
        let duration = start_time.elapsed();
        let success = errors.is_empty();
        
        ApplicationResult {
            success,
            errors,
            duration,
            rollback_required: !success,
        }
    }
}

/// Rollback manager
pub struct RollbackManager {
    backup_configs: Arc<RwLock<Vec<Config>>>,
}

impl RollbackManager {
    fn new(initial_config: Config) -> Self {
        Self {
            backup_configs: Arc::new(RwLock::new(vec![initial_config])),
        }
    }
    
    async fn save_backup(&self, config: &Config) {
        let mut backups = self.backup_configs.write().await;
        backups.push(config.clone());
        
        // Keep only last 10 backups
        if backups.len() > 10 {
            backups.remove(0);
        }
    }
    
    async fn rollback(
        &self,
        current_config: &Arc<RwLock<Config>>,
        _reload_config: &HotReloadConfig,
    ) -> Result<()> {
        let backups = self.backup_configs.read().await;
        
        if backups.len() < 2 {
            return Err(LoadBalancerError::Internal {
                message: "No backup configuration available for rollback".to_string(),
                component: "rollback_manager".to_string(),
                error_id: Uuid::new_v4().to_string(),
                context: None,
            });
        }
        
        // Get the previous configuration (second to last)
        let previous_config = backups[backups.len() - 2].clone();
        
        // Apply rollback
        {
            let mut current = current_config.write().await;
            *current = previous_config;
        }
        
        info!("🔄 Configuration rolled back successfully");
        Ok(())
    }
}

impl Default for HotReloadConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            watch_paths: vec![PathBuf::from("config.yaml")],
            validation_timeout: Duration::from_secs(30),
            application_timeout: Duration::from_secs(60),
            rollback_timeout: Duration::from_secs(30),
            max_rollback_attempts: 3,
            enable_gradual_rollout: true,
            rollout_steps: vec![10, 25, 50, 75, 100],
            step_duration: Duration::from_secs(30),
        }
    }
}
```

## Integration with Load Balancer

Update `src/main.rs` to include hot reloading:

```rust
use rusty_balancer::reload::{HotReloadManager, HotReloadConfig, ChangeSource};
use rusty_balancer::config::{Config, ConfigLoader};
use std::sync::Arc;
use std::path::PathBuf;
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("🚀 Starting Rusty Load Balancer with Hot Reloading");

    // Load initial configuration
    let config_loader = ConfigLoader::new()
        .add_config_path("config.yaml");

    let initial_config = config_loader.load()?;
    info!("✅ Initial configuration loaded");

    // Create hot reload configuration
    let hot_reload_config = HotReloadConfig {
        enabled: true,
        watch_paths: vec![
            PathBuf::from("config.yaml"),
            PathBuf::from("backends.yaml"),
        ],
        enable_gradual_rollout: true,
        rollout_steps: vec![10, 25, 50, 100],
        step_duration: std::time::Duration::from_secs(30),
        ..Default::default()
    };

    // Create hot reload manager
    let hot_reload_manager = Arc::new(
        HotReloadManager::new(initial_config.clone(), hot_reload_config)?
    );

    // Start hot reloading system
    hot_reload_manager.start().await?;

    // Create load balancer components with hot reload integration
    let load_balancer = create_load_balancer_with_hot_reload(
        hot_reload_manager.clone(),
        initial_config,
    ).await?;

    // Start management API for manual configuration updates
    let management_api = create_management_api(hot_reload_manager.clone());
    tokio::spawn(async move {
        if let Err(e) = management_api.start().await {
            error!("Management API error: {}", e);
        }
    });

    // Start the load balancer
    load_balancer.start().await?;

    Ok(())
}

async fn create_load_balancer_with_hot_reload(
    hot_reload_manager: Arc<HotReloadManager>,
    initial_config: Config,
) -> Result<HotReloadAwareLoadBalancer, Box<dyn std::error::Error>> {
    Ok(HotReloadAwareLoadBalancer::new(hot_reload_manager, initial_config))
}

/// Load balancer with hot reload integration
pub struct HotReloadAwareLoadBalancer {
    hot_reload_manager: Arc<HotReloadManager>,
    current_config: Config,
}

impl HotReloadAwareLoadBalancer {
    pub fn new(hot_reload_manager: Arc<HotReloadManager>, initial_config: Config) -> Self {
        Self {
            hot_reload_manager,
            current_config: initial_config,
        }
    }

    pub async fn start(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let bind_addr = self.current_config.server.bind_address;

        info!("🌐 Starting load balancer with hot reload on {}", bind_addr);

        // Start configuration monitoring task
        let hot_reload_manager = self.hot_reload_manager.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(5));

            loop {
                interval.tick().await;

                // Check for configuration updates
                let current_config = hot_reload_manager.get_current_config().await;

                // In a real implementation, you'd apply configuration changes
                // to the running load balancer components
                debug!("Configuration monitoring active");
            }
        });

        // Simulate running the load balancer
        loop {
            // Get current configuration (may have been updated)
            let config = self.hot_reload_manager.get_current_config().await;

            // Update internal state if configuration changed
            if config.server.bind_address != self.current_config.server.bind_address {
                info!("🔄 Bind address changed, would restart server in production");
                self.current_config = config;
            }

            tokio::time::sleep(std::time::Duration::from_secs(1)).await;
        }
    }
}

/// Management API for manual configuration updates
pub struct ManagementApi {
    hot_reload_manager: Arc<HotReloadManager>,
}

impl ManagementApi {
    pub fn new(hot_reload_manager: Arc<HotReloadManager>) -> Self {
        Self { hot_reload_manager }
    }

    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error>> {
        use warp::Filter;

        let hot_reload_manager = self.hot_reload_manager.clone();

        // GET /api/config - Get current configuration
        let get_config = warp::path!("api" / "config")
            .and(warp::get())
            .and_then({
                let hot_reload_manager = hot_reload_manager.clone();
                move || {
                    let hot_reload_manager = hot_reload_manager.clone();
                    async move {
                        let config = hot_reload_manager.get_current_config().await;
                        let json = serde_json::to_string(&config)
                            .map_err(|_| warp::reject::custom(ApiError::SerializationError))?;
                        Ok::<_, warp::Rejection>(warp::reply::json(&json))
                    }
                }
            });

        // POST /api/config - Update configuration
        let update_config = warp::path!("api" / "config")
            .and(warp::post())
            .and(warp::body::json())
            .and_then({
                let hot_reload_manager = hot_reload_manager.clone();
                move |new_config: Config| {
                    let hot_reload_manager = hot_reload_manager.clone();
                    async move {
                        let source = ChangeSource::ManagementApi {
                            user: "admin".to_string(),
                            ip: "127.0.0.1".to_string(),
                        };

                        match hot_reload_manager.reload_config(new_config, source).await {
                            Ok(_) => Ok(warp::reply::with_status(
                                "Configuration update triggered",
                                warp::http::StatusCode::ACCEPTED,
                            )),
                            Err(e) => {
                                error!("Failed to trigger config reload: {}", e);
                                Err(warp::reject::custom(ApiError::ReloadError))
                            }
                        }
                    }
                }
            });

        // GET /api/reload/stats - Get reload statistics
        let get_stats = warp::path!("api" / "reload" / "stats")
            .and(warp::get())
            .and_then({
                let hot_reload_manager = hot_reload_manager.clone();
                move || {
                    let hot_reload_manager = hot_reload_manager.clone();
                    async move {
                        let stats = hot_reload_manager.get_reload_stats().await;
                        Ok::<_, warp::Rejection>(warp::reply::json(&stats))
                    }
                }
            });

        let routes = get_config
            .or(update_config)
            .or(get_stats)
            .with(warp::cors().allow_any_origin());

        info!("🔧 Management API listening on :8090");
        warp::serve(routes)
            .run(([0, 0, 0, 0], 8090))
            .await;

        Ok(())
    }
}

#[derive(Debug)]
enum ApiError {
    SerializationError,
    ReloadError,
}

impl warp::reject::Reject for ApiError {}

fn create_management_api(hot_reload_manager: Arc<HotReloadManager>) -> ManagementApi {
    ManagementApi::new(hot_reload_manager)
}
```

## Testing Hot Reloading

Create `tests/hot_reload_tests.rs`:

```rust
use rusty_balancer::reload::{HotReloadManager, HotReloadConfig, ChangeSource};
use rusty_balancer::config::Config;
use std::path::PathBuf;
use tempfile::TempDir;
use tokio::fs;

#[tokio::test]
async fn test_hot_reload_manager_creation() {
    let config = Config::default();
    let hot_reload_config = HotReloadConfig::default();

    let manager = HotReloadManager::new(config, hot_reload_config)
        .expect("Failed to create hot reload manager");

    let current_config = manager.get_current_config().await;
    assert_eq!(current_config.server.bind_address.to_string(), "0.0.0.0:8080");
}

#[tokio::test]
async fn test_manual_configuration_reload() {
    let initial_config = Config::default();
    let hot_reload_config = HotReloadConfig {
        enabled: true,
        ..Default::default()
    };

    let manager = HotReloadManager::new(initial_config, hot_reload_config)
        .expect("Failed to create hot reload manager");

    // Start the hot reload system
    manager.start().await.expect("Failed to start hot reload system");

    // Create a new configuration
    let mut new_config = Config::default();
    new_config.server.max_connections = 5000;

    // Trigger manual reload
    let source = ChangeSource::ManagementApi {
        user: "test_user".to_string(),
        ip: "127.0.0.1".to_string(),
    };

    manager.reload_config(new_config, source).await
        .expect("Failed to trigger configuration reload");

    // Give some time for the reload to process
    tokio::time::sleep(std::time::Duration::from_millis(100)).await;

    // Verify configuration was updated
    let current_config = manager.get_current_config().await;
    assert_eq!(current_config.server.max_connections, 5000);
}

#[tokio::test]
async fn test_file_watcher_integration() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("test_config.yaml");

    // Write initial configuration
    let initial_config_yaml = r#"
server:
  bind_address: "127.0.0.1:8080"
  max_connections: 1000

backends:
  servers: []
  default_timeout: "30s"
  max_retries: 3

load_balancing:
  algorithm: "round_robin"

health_checks:
  enabled: true
  interval: "30s"
  timeout: "5s"
  unhealthy_threshold: 3
  healthy_threshold: 2
  path: "/health"
  healthy_status_codes: [200, 204]

observability:
  logging:
    level: "info"
    format: "json"
    structured: true
  metrics:
    enabled: true
    address: "0.0.0.0:9090"
    interval: "15s"
  tracing:
    enabled: false
    sampling_rate: 0.1

security: {}
performance:
  socket_buffers:
    recv_buffer_size: 65536
    send_buffer_size: 65536
    tcp_nodelay: true
  memory:
    buffer_pool_size: 1024
    use_mmap: false
"#;

    fs::write(&config_path, initial_config_yaml).await.unwrap();

    let initial_config = Config::default();
    let hot_reload_config = HotReloadConfig {
        enabled: true,
        watch_paths: vec![config_path.clone()],
        ..Default::default()
    };

    let manager = HotReloadManager::new(initial_config, hot_reload_config)
        .expect("Failed to create hot reload manager");

    // Start the hot reload system
    manager.start().await.expect("Failed to start hot reload system");

    // Give the file watcher time to initialize
    tokio::time::sleep(std::time::Duration::from_millis(500)).await;

    // Update the configuration file
    let updated_config_yaml = r#"
server:
  bind_address: "127.0.0.1:8080"
  max_connections: 2000

backends:
  servers: []
  default_timeout: "30s"
  max_retries: 3

load_balancing:
  algorithm: "round_robin"

health_checks:
  enabled: true
  interval: "30s"
  timeout: "5s"
  unhealthy_threshold: 3
  healthy_threshold: 2
  path: "/health"
  healthy_status_codes: [200, 204]

observability:
  logging:
    level: "info"
    format: "json"
    structured: true
  metrics:
    enabled: true
    address: "0.0.0.0:9090"
    interval: "15s"
  tracing:
    enabled: false
    sampling_rate: 0.1

security: {}
performance:
  socket_buffers:
    recv_buffer_size: 65536
    send_buffer_size: 65536
    tcp_nodelay: true
  memory:
    buffer_pool_size: 1024
    use_mmap: false
"#;

    fs::write(&config_path, updated_config_yaml).await.unwrap();

    // Give time for file watcher to detect change and process it
    tokio::time::sleep(std::time::Duration::from_secs(2)).await;

    // Verify configuration was updated
    let current_config = manager.get_current_config().await;
    assert_eq!(current_config.server.max_connections, 2000);
}

#[tokio::test]
async fn test_reload_statistics() {
    let config = Config::default();
    let hot_reload_config = HotReloadConfig::default();

    let manager = HotReloadManager::new(config, hot_reload_config)
        .expect("Failed to create hot reload manager");

    let stats = manager.get_reload_stats().await;

    // Initially, no reloads should have occurred
    assert_eq!(stats.total_reloads, 0);
    assert_eq!(stats.successful_reloads, 0);
    assert_eq!(stats.failed_reloads, 0);
    assert_eq!(stats.rollbacks, 0);
    assert!(stats.last_reload.is_none());
}
```

## Key Design Decisions Explained

### 1. Gradual Configuration Rollout
**Decision**: Apply configuration changes gradually rather than all at once

**Rationale**:
- **Risk Mitigation**: Limit blast radius of configuration errors
- **Monitoring**: Observe impact before full deployment
- **Rollback**: Easy to revert if issues are detected
- **Production Safety**: Minimize service disruption

### 2. Configuration Validation Pipeline
**Decision**: Comprehensive validation before applying changes

**Rationale**:
- **Reliability**: Prevent invalid configurations from being applied
- **Consistency**: Ensure configuration integrity and dependencies
- **Security**: Validate security-related configuration changes
- **Compliance**: Enforce organizational policies and standards

### 3. Automatic Rollback Mechanism
**Decision**: Automatic rollback on configuration application failures

**Rationale**:
- **Service Continuity**: Maintain service availability during failures
- **Operational Safety**: Reduce manual intervention requirements
- **Recovery Time**: Minimize time to recover from bad configurations
- **Confidence**: Enable more frequent configuration updates

## Performance Considerations

1. **File Watching Overhead**: Minimal impact with debounced file watching
2. **Validation Performance**: Fast validation to minimize reload time
3. **Memory Usage**: Efficient configuration storage and backup management
4. **Gradual Rollout**: Controlled resource usage during configuration changes

## Security Considerations

1. **Configuration Validation**: Prevent malicious configuration changes
2. **Access Control**: Secure management API endpoints
3. **Audit Trail**: Log all configuration changes with metadata
4. **Rollback Security**: Ensure rollback configurations are secure

## Navigation
- [Previous: TLS/SSL Support](11-tls-support.md)
- [Next: Weighted Load Balancing](13-weighted-load-balancing.md)
