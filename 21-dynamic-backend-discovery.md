# Module 21: Dynamic Backend Discovery

## Learning Objectives
- Implement dynamic service discovery for automatic backend registration and deregistration
- Design integration with service registries like Consul, etcd, and Kubernetes
- Build service mesh integration and cloud-native discovery patterns
- Create intelligent backend selection and load distribution strategies
- Understand modern service discovery patterns and cloud-native architectures

## Why Dynamic Backend Discovery is Essential

Dynamic service discovery enables modern, scalable, and resilient architectures:

1. **Auto-Scaling**: Automatically discover new instances as they come online
2. **Fault Tolerance**: Remove failed instances without manual intervention
3. **Zero-Downtime Deployments**: Seamlessly integrate new service versions
4. **Cloud-Native**: Essential for containerized and microservices architectures
5. **Operational Efficiency**: Reduce manual configuration and maintenance overhead

### Dynamic Discovery Architecture

```mermaid
graph TD
    LB[Load Balancer] --> DDM[Dynamic Discovery Manager]
    
    DDM --> ServiceRegistry[Service Registry]
    DDM --> HealthIntegration[Health Integration]
    DDM --> BackendManager[Backend Manager]
    
    subgraph "Service Registries"
        Consul[Consul]
        Etcd[etcd]
        Kubernetes[Kubernetes API]
        Eureka[Netflix Eureka]
        Zookeeper[Apache Zookeeper]
    end
    
    ServiceRegistry --> Consul
    ServiceRegistry --> Etcd
    ServiceRegistry --> Kubernetes
    ServiceRegistry --> Eureka
    ServiceRegistry --> Zookeeper
    
    subgraph "Discovery Methods"
        Polling[Periodic Polling]
        Watching[Event Watching]
        Webhooks[Webhook Notifications]
        DNS[DNS-based Discovery]
    end
    
    DDM --> Polling
    DDM --> Watching
    DDM --> Webhooks
    DDM --> DNS
    
    subgraph "Backend Lifecycle"
        Register[Register Backend]
        Validate[Validate Health]
        Activate[Activate for Traffic]
        Monitor[Monitor Status]
        Deregister[Deregister Backend]
    end
    
    Register --> Validate
    Validate --> Activate
    Activate --> Monitor
    Monitor --> Deregister
    
    subgraph "Service Metadata"
        Tags[Service Tags]
        Version[Service Version]
        Environment[Environment]
        Capabilities[Capabilities]
        Metrics[Health Metrics]
    end
    
    BackendManager --> Tags
    BackendManager --> Version
    BackendManager --> Environment
    BackendManager --> Capabilities
    BackendManager --> Metrics
    
    subgraph "Load Distribution"
        WeightedLB[Weighted Load Balancing]
        VersionRouting[Version-based Routing]
        CanaryDeployment[Canary Deployments]
        BlueGreen[Blue-Green Deployments]
    end
    
    BackendManager --> WeightedLB
    BackendManager --> VersionRouting
    BackendManager --> CanaryDeployment
    BackendManager --> BlueGreen
    
    subgraph "Integration Points"
        ServiceMesh[Service Mesh]
        CloudProvider[Cloud Provider APIs]
        ContainerOrchestrator[Container Orchestrator]
        MonitoringSystem[Monitoring Systems]
    end
    
    DDM --> ServiceMesh
    DDM --> CloudProvider
    DDM --> ContainerOrchestrator
    DDM --> MonitoringSystem
```

## Dynamic Discovery Implementation

### Design Decisions

**Why multiple service registry support?**
- **Flexibility**: Different environments use different service registries
- **Migration**: Support gradual migration between registry systems
- **Hybrid Deployments**: Handle multi-cloud and hybrid environments
- **Vendor Independence**: Avoid lock-in to specific registry solutions

**Why intelligent backend selection?**
- **Performance**: Route traffic to optimal backends based on current conditions
- **Deployment Safety**: Support advanced deployment patterns like canary and blue-green
- **Resource Optimization**: Distribute load based on backend capacity and health
- **Business Logic**: Enable routing based on business requirements and SLAs

Create `src/discovery/mod.rs`:

```rust
//! Dynamic backend discovery system for automatic service registration
//!
//! This module provides comprehensive service discovery capabilities including:
//! - Integration with multiple service registries (Consul, etcd, Kubernetes)
//! - Automatic backend registration and deregistration
//! - Service metadata and capability-based routing
//! - Advanced deployment pattern support (canary, blue-green)
//! - Cloud-native and service mesh integration

pub mod manager;
pub mod registries;
pub mod backends;
pub mod routing;
pub mod metadata;

use crate::error::{LoadBalancerError, Result};
use crate::health::{AdvancedHealthManager, HealthState};
use crate::metrics::LoadBalancerMetrics;
use serde::{Serialize, Deserialize};
use std::collections::{HashMap, HashSet};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug, trace};

/// Dynamic discovery manager coordinating service discovery activities
pub struct DynamicDiscoveryManager {
    /// Discovery configuration
    config: DiscoveryConfig,
    
    /// Service registry clients
    registries: Arc<RwLock<HashMap<String, Box<dyn ServiceRegistry + Send + Sync>>>>,
    
    /// Discovered backends
    backends: Arc<RwLock<HashMap<SocketAddr, DiscoveredBackend>>>,
    
    /// Service metadata cache
    metadata_cache: Arc<RwLock<HashMap<String, ServiceMetadata>>>,
    
    /// Health manager integration
    health_manager: Option<Arc<AdvancedHealthManager>>,
    
    /// Metrics collector
    metrics: Option<Arc<LoadBalancerMetrics>>,
    
    /// Backend selection strategy
    selection_strategy: Arc<dyn BackendSelectionStrategy + Send + Sync>,
}

/// Discovery configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiscoveryConfig {
    /// Enable dynamic discovery
    pub enabled: bool,
    
    /// Service registries configuration
    pub registries: Vec<RegistryConfig>,
    
    /// Discovery polling interval
    pub polling_interval: Duration,
    
    /// Enable event-based discovery (watching)
    pub enable_watching: bool,
    
    /// Backend validation timeout
    pub validation_timeout: Duration,
    
    /// Service name to discover
    pub service_name: String,
    
    /// Service tags to filter by
    pub service_tags: Vec<String>,
    
    /// Environment filter
    pub environment: Option<String>,
    
    /// Enable health check integration
    pub enable_health_integration: bool,
    
    /// Backend selection strategy
    pub selection_strategy: SelectionStrategy,
    
    /// Enable advanced deployment patterns
    pub enable_advanced_deployments: bool,
    
    /// Canary deployment configuration
    pub canary_config: CanaryConfig,
    
    /// Blue-green deployment configuration
    pub blue_green_config: BlueGreenConfig,
    
    /// Enable service mesh integration
    pub enable_service_mesh: bool,
    
    /// Service mesh configuration
    pub service_mesh_config: ServiceMeshConfig,
}

/// Service registry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryConfig {
    /// Registry name
    pub name: String,
    
    /// Registry type
    pub registry_type: RegistryType,
    
    /// Connection configuration
    pub connection: HashMap<String, String>,
    
    /// Registry priority (higher = preferred)
    pub priority: u32,
    
    /// Enable this registry
    pub enabled: bool,
}

/// Supported service registry types
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum RegistryType {
    Consul,
    Etcd,
    Kubernetes,
    Eureka,
    Zookeeper,
    DNS,
}

/// Backend selection strategies
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum SelectionStrategy {
    RoundRobin,
    WeightedRoundRobin,
    LeastConnections,
    HealthBased,
    CapabilityBased,
    VersionBased,
    GeographyBased,
}

/// Discovered backend information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiscoveredBackend {
    /// Backend address
    pub address: SocketAddr,
    
    /// Service metadata
    pub metadata: ServiceMetadata,
    
    /// Discovery source
    pub source: String,
    
    /// Discovery timestamp
    pub discovered_at: SystemTime,
    
    /// Last seen timestamp
    pub last_seen: SystemTime,
    
    /// Backend status
    pub status: BackendStatus,
    
    /// Health state
    pub health_state: HealthState,
    
    /// Current weight for load balancing
    pub weight: f64,
    
    /// Active connections count
    pub active_connections: u32,
    
    /// Backend capabilities
    pub capabilities: HashSet<String>,
}

/// Service metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceMetadata {
    /// Service name
    pub service_name: String,
    
    /// Service version
    pub version: String,
    
    /// Environment (prod, staging, dev)
    pub environment: String,
    
    /// Service tags
    pub tags: Vec<String>,
    
    /// Service port
    pub port: u16,
    
    /// Health check endpoint
    pub health_endpoint: Option<String>,
    
    /// Service capabilities
    pub capabilities: HashSet<String>,
    
    /// Custom metadata
    pub custom: HashMap<String, String>,
    
    /// Deployment metadata
    pub deployment: DeploymentMetadata,
}

/// Deployment metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentMetadata {
    /// Deployment type
    pub deployment_type: DeploymentType,
    
    /// Deployment version
    pub deployment_version: String,
    
    /// Deployment timestamp
    pub deployed_at: SystemTime,
    
    /// Canary weight (for canary deployments)
    pub canary_weight: Option<f64>,
    
    /// Blue-green slot (for blue-green deployments)
    pub blue_green_slot: Option<BlueGreenSlot>,
}

/// Deployment types
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum DeploymentType {
    Standard,
    Canary,
    BlueGreen,
    RollingUpdate,
}

/// Blue-green deployment slots
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum BlueGreenSlot {
    Blue,
    Green,
}

/// Backend status
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum BackendStatus {
    /// Backend is being validated
    Validating,
    
    /// Backend is active and receiving traffic
    Active,
    
    /// Backend is draining connections
    Draining,
    
    /// Backend is inactive (not receiving traffic)
    Inactive,
    
    /// Backend has been removed
    Removed,
}

/// Canary deployment configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CanaryConfig {
    /// Enable canary deployments
    pub enabled: bool,
    
    /// Initial canary traffic percentage
    pub initial_weight: f64,
    
    /// Maximum canary traffic percentage
    pub max_weight: f64,
    
    /// Weight increment step
    pub weight_step: f64,
    
    /// Time between weight increases
    pub step_interval: Duration,
    
    /// Success criteria for promotion
    pub success_criteria: SuccessCriteria,
}

/// Blue-green deployment configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlueGreenConfig {
    /// Enable blue-green deployments
    pub enabled: bool,
    
    /// Validation period before switching
    pub validation_period: Duration,
    
    /// Automatic switch on success
    pub auto_switch: bool,
    
    /// Keep old version for rollback
    pub keep_old_version: bool,
    
    /// Rollback timeout
    pub rollback_timeout: Duration,
}

/// Success criteria for deployments
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuccessCriteria {
    /// Minimum success rate
    pub min_success_rate: f64,
    
    /// Maximum error rate
    pub max_error_rate: f64,
    
    /// Maximum response time
    pub max_response_time: Duration,
    
    /// Minimum sample size
    pub min_sample_size: u32,
    
    /// Evaluation period
    pub evaluation_period: Duration,
}

/// Service mesh configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceMeshConfig {
    /// Service mesh type
    pub mesh_type: ServiceMeshType,
    
    /// Mesh configuration
    pub config: HashMap<String, String>,
}

/// Service mesh types
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum ServiceMeshType {
    Istio,
    Linkerd,
    Consul,
    Envoy,
}

impl DynamicDiscoveryManager {
    /// Create a new dynamic discovery manager
    pub fn new(config: DiscoveryConfig) -> Self {
        let selection_strategy: Arc<dyn BackendSelectionStrategy + Send + Sync> = match config.selection_strategy {
            SelectionStrategy::RoundRobin => Arc::new(RoundRobinStrategy::new()),
            SelectionStrategy::WeightedRoundRobin => Arc::new(WeightedRoundRobinStrategy::new()),
            SelectionStrategy::LeastConnections => Arc::new(LeastConnectionsStrategy::new()),
            SelectionStrategy::HealthBased => Arc::new(HealthBasedStrategy::new()),
            SelectionStrategy::CapabilityBased => Arc::new(CapabilityBasedStrategy::new()),
            SelectionStrategy::VersionBased => Arc::new(VersionBasedStrategy::new()),
            SelectionStrategy::GeographyBased => Arc::new(GeographyBasedStrategy::new()),
        };
        
        info!("🔍 Dynamic discovery manager initialized (strategy: {:?})", config.selection_strategy);
        
        Self {
            config,
            registries: Arc::new(RwLock::new(HashMap::new())),
            backends: Arc::new(RwLock::new(HashMap::new())),
            metadata_cache: Arc::new(RwLock::new(HashMap::new())),
            health_manager: None,
            metrics: None,
            selection_strategy,
        }
    }
    
    /// Set health manager for integration
    pub fn with_health_manager(mut self, health_manager: Arc<AdvancedHealthManager>) -> Self {
        self.health_manager = Some(health_manager);
        self
    }
    
    /// Set metrics collector
    pub fn with_metrics(mut self, metrics: Arc<LoadBalancerMetrics>) -> Self {
        self.metrics = Some(metrics);
        self
    }
    
    /// Start dynamic discovery services
    pub async fn start(&self) -> Result<()> {
        if !self.config.enabled {
            info!("Dynamic discovery is disabled");
            return Ok(());
        }
        
        // Initialize service registries
        self.initialize_registries().await?;
        
        // Start discovery polling
        self.start_discovery_polling().await;
        
        // Start event watching if enabled
        if self.config.enable_watching {
            self.start_event_watching().await;
        }
        
        // Start backend validation
        self.start_backend_validation().await;
        
        // Start deployment pattern management
        if self.config.enable_advanced_deployments {
            self.start_deployment_management().await;
        }
        
        // Start service mesh integration
        if self.config.enable_service_mesh {
            self.start_service_mesh_integration().await;
        }
        
        info!("✅ Dynamic discovery services started");
        Ok(())
    }
    
    /// Get available backends for load balancing
    pub async fn get_available_backends(&self) -> Vec<SocketAddr> {
        let backends = self.backends.read().await;
        
        backends.values()
            .filter(|backend| {
                backend.status == BackendStatus::Active &&
                matches!(backend.health_state, HealthState::Healthy | HealthState::Degraded)
            })
            .map(|backend| backend.address)
            .collect()
    }
    
    /// Select best backend for a request
    pub async fn select_backend(&self, request_context: &RequestContext) -> Option<SocketAddr> {
        let backends = self.backends.read().await;
        
        let available_backends: Vec<&DiscoveredBackend> = backends.values()
            .filter(|backend| {
                backend.status == BackendStatus::Active &&
                matches!(backend.health_state, HealthState::Healthy | HealthState::Degraded)
            })
            .collect();
        
        if available_backends.is_empty() {
            return None;
        }
        
        self.selection_strategy.select_backend(&available_backends, request_context).await
    }
    
    /// Get backend metadata
    pub async fn get_backend_metadata(&self, address: SocketAddr) -> Option<ServiceMetadata> {
        let backends = self.backends.read().await;
        backends.get(&address).map(|backend| backend.metadata.clone())
    }
    
    /// Initialize service registries
    async fn initialize_registries(&self) -> Result<()> {
        let mut registries = self.registries.write().await;
        
        for registry_config in &self.config.registries {
            if !registry_config.enabled {
                continue;
            }
            
            let registry: Box<dyn ServiceRegistry + Send + Sync> = match registry_config.registry_type {
                RegistryType::Consul => Box::new(ConsulRegistry::new(registry_config.clone()).await?),
                RegistryType::Etcd => Box::new(EtcdRegistry::new(registry_config.clone()).await?),
                RegistryType::Kubernetes => Box::new(KubernetesRegistry::new(registry_config.clone()).await?),
                RegistryType::Eureka => Box::new(EurekaRegistry::new(registry_config.clone()).await?),
                RegistryType::Zookeeper => Box::new(ZookeeperRegistry::new(registry_config.clone()).await?),
                RegistryType::DNS => Box::new(DNSRegistry::new(registry_config.clone()).await?),
            };
            
            registries.insert(registry_config.name.clone(), registry);
            info!("🔗 Initialized {} registry: {}", 
                  format!("{:?}", registry_config.registry_type).to_lowercase(), 
                  registry_config.name);
        }
        
        Ok(())
    }
    
    /// Start discovery polling
    async fn start_discovery_polling(&self) {
        let registries = self.registries.clone();
        let backends = self.backends.clone();
        let config = self.config.clone();
        let health_manager = self.health_manager.clone();
        
        tokio::spawn(async move {
            let mut polling_interval = tokio::time::interval(config.polling_interval);
            
            loop {
                polling_interval.tick().await;
                
                let registries_guard = registries.read().await;
                
                for (registry_name, registry) in registries_guard.iter() {
                    match registry.discover_services(&config.service_name, &config.service_tags).await {
                        Ok(discovered_services) => {
                            Self::update_backends(
                                &backends,
                                discovered_services,
                                registry_name,
                                &health_manager
                            ).await;
                        }
                        Err(e) => {
                            error!("Failed to discover services from {}: {}", registry_name, e);
                        }
                    }
                }
                
                debug!("🔍 Completed discovery polling cycle");
            }
        });
    }
    
    /// Update backends from discovery results
    async fn update_backends(
        backends: &Arc<RwLock<HashMap<SocketAddr, DiscoveredBackend>>>,
        discovered_services: Vec<ServiceInstance>,
        source: &str,
        health_manager: &Option<Arc<AdvancedHealthManager>>
    ) {
        let mut backends_guard = backends.write().await;
        let now = SystemTime::now();
        
        for service in discovered_services {
            let address = SocketAddr::new(service.address, service.port);
            
            match backends_guard.get_mut(&address) {
                Some(existing_backend) => {
                    // Update existing backend
                    existing_backend.last_seen = now;
                    existing_backend.metadata = service.metadata;
                }
                None => {
                    // Add new backend
                    let backend = DiscoveredBackend {
                        address,
                        metadata: service.metadata,
                        source: source.to_string(),
                        discovered_at: now,
                        last_seen: now,
                        status: BackendStatus::Validating,
                        health_state: HealthState::Unknown,
                        weight: 1.0,
                        active_connections: 0,
                        capabilities: service.capabilities,
                    };
                    
                    backends_guard.insert(address, backend);
                    
                    // Add to health monitoring if available
                    if let Some(ref health_mgr) = health_manager {
                        if let Err(e) = health_mgr.add_backend(address).await {
                            warn!("Failed to add backend to health monitoring: {}", e);
                        }
                    }
                    
                    info!("🆕 Discovered new backend: {} from {}", address, source);
                }
            }
        }
    }
    
    /// Start event watching
    async fn start_event_watching(&self) {
        info!("👁️ Starting event-based discovery watching");
        
        // Event watching implementation would go here
        // This would listen for real-time events from service registries
    }
    
    /// Start backend validation
    async fn start_backend_validation(&self) {
        let backends = self.backends.clone();
        let health_manager = self.health_manager.clone();
        let validation_timeout = self.config.validation_timeout;
        
        tokio::spawn(async move {
            let mut validation_interval = tokio::time::interval(Duration::from_secs(30));
            
            loop {
                validation_interval.tick().await;
                
                let mut backends_guard = backends.write().await;
                let mut backends_to_remove = Vec::new();
                
                for (address, backend) in backends_guard.iter_mut() {
                    match backend.status {
                        BackendStatus::Validating => {
                            // Check if validation timeout exceeded
                            if backend.discovered_at.elapsed().unwrap_or_default() > validation_timeout {
                                if let Some(ref health_mgr) = health_manager {
                                    if health_mgr.is_backend_healthy(*address).await {
                                        backend.status = BackendStatus::Active;
                                        info!("✅ Backend {} validated and activated", address);
                                    } else {
                                        backends_to_remove.push(*address);
                                        warn!("❌ Backend {} failed validation", address);
                                    }
                                } else {
                                    // No health manager, assume healthy
                                    backend.status = BackendStatus::Active;
                                }
                            }
                        }
                        BackendStatus::Active => {
                            // Update health state from health manager
                            if let Some(ref health_mgr) = health_manager {
                                if let Some(health) = health_mgr.get_backend_health(*address).await {
                                    backend.health_state = health.state;
                                }
                            }
                        }
                        _ => {}
                    }
                }
                
                // Remove failed backends
                for address in backends_to_remove {
                    backends_guard.remove(&address);
                    
                    if let Some(ref health_mgr) = health_manager {
                        let _ = health_mgr.remove_backend(address).await;
                    }
                }
            }
        });
    }
    
    /// Start deployment pattern management
    async fn start_deployment_management(&self) {
        info!("🚀 Starting advanced deployment pattern management");
        
        // Canary deployment management
        if self.config.canary_config.enabled {
            self.start_canary_management().await;
        }
        
        // Blue-green deployment management
        if self.config.blue_green_config.enabled {
            self.start_blue_green_management().await;
        }
    }
    
    /// Start canary deployment management
    async fn start_canary_management(&self) {
        let backends = self.backends.clone();
        let canary_config = self.config.canary_config.clone();
        
        tokio::spawn(async move {
            let mut canary_interval = tokio::time::interval(canary_config.step_interval);
            
            loop {
                canary_interval.tick().await;
                
                // Canary deployment logic would be implemented here
                debug!("🐤 Managing canary deployments");
            }
        });
    }
    
    /// Start blue-green deployment management
    async fn start_blue_green_management(&self) {
        let backends = self.backends.clone();
        let blue_green_config = self.config.blue_green_config.clone();
        
        tokio::spawn(async move {
            let mut bg_interval = tokio::time::interval(Duration::from_secs(60));
            
            loop {
                bg_interval.tick().await;
                
                // Blue-green deployment logic would be implemented here
                debug!("🔵🟢 Managing blue-green deployments");
            }
        });
    }
    
    /// Start service mesh integration
    async fn start_service_mesh_integration(&self) {
        info!("🕸️ Starting service mesh integration");
        
        // Service mesh integration would be implemented here
        // This would integrate with Istio, Linkerd, etc.
    }
}

/// Request context for backend selection
#[derive(Debug, Clone)]
pub struct RequestContext {
    /// Request path
    pub path: String,
    
    /// Request headers
    pub headers: HashMap<String, String>,
    
    /// Client IP
    pub client_ip: Option<std::net::IpAddr>,
    
    /// Required capabilities
    pub required_capabilities: HashSet<String>,
    
    /// Preferred version
    pub preferred_version: Option<String>,
    
    /// Request metadata
    pub metadata: HashMap<String, String>,
}

/// Service instance discovered from registry
#[derive(Debug, Clone)]
pub struct ServiceInstance {
    /// Service address
    pub address: std::net::IpAddr,
    
    /// Service port
    pub port: u16,
    
    /// Service metadata
    pub metadata: ServiceMetadata,
    
    /// Service capabilities
    pub capabilities: HashSet<String>,
}

/// Trait for service registry implementations
#[async_trait::async_trait]
pub trait ServiceRegistry {
    /// Discover services by name and tags
    async fn discover_services(&self, service_name: &str, tags: &[String]) -> Result<Vec<ServiceInstance>>;

    /// Register a service instance
    async fn register_service(&self, instance: &ServiceInstance) -> Result<()>;

    /// Deregister a service instance
    async fn deregister_service(&self, instance: &ServiceInstance) -> Result<()>;

    /// Watch for service changes (if supported)
    async fn watch_services(&self, service_name: &str) -> Result<tokio::sync::mpsc::Receiver<ServiceEvent>>;

    /// Get registry health
    async fn health_check(&self) -> Result<bool>;
}

/// Service registry events
#[derive(Debug, Clone)]
pub enum ServiceEvent {
    ServiceRegistered(ServiceInstance),
    ServiceDeregistered(ServiceInstance),
    ServiceUpdated(ServiceInstance),
    HealthChanged(std::net::SocketAddr, HealthState),
}

/// Backend selection strategy trait
#[async_trait::async_trait]
pub trait BackendSelectionStrategy {
    /// Select the best backend for a request
    async fn select_backend(
        &self,
        backends: &[&DiscoveredBackend],
        context: &RequestContext,
    ) -> Option<SocketAddr>;
}

/// Round-robin backend selection
pub struct RoundRobinStrategy {
    counter: Arc<std::sync::atomic::AtomicUsize>,
}

impl RoundRobinStrategy {
    pub fn new() -> Self {
        Self {
            counter: Arc::new(std::sync::atomic::AtomicUsize::new(0)),
        }
    }
}

#[async_trait::async_trait]
impl BackendSelectionStrategy for RoundRobinStrategy {
    async fn select_backend(
        &self,
        backends: &[&DiscoveredBackend],
        _context: &RequestContext,
    ) -> Option<SocketAddr> {
        if backends.is_empty() {
            return None;
        }

        let index = self.counter.fetch_add(1, std::sync::atomic::Ordering::Relaxed) % backends.len();
        Some(backends[index].address)
    }
}

/// Weighted round-robin backend selection
pub struct WeightedRoundRobinStrategy {
    weights: Arc<RwLock<HashMap<SocketAddr, f64>>>,
}

impl WeightedRoundRobinStrategy {
    pub fn new() -> Self {
        Self {
            weights: Arc::new(RwLock::new(HashMap::new())),
        }
    }
}

#[async_trait::async_trait]
impl BackendSelectionStrategy for WeightedRoundRobinStrategy {
    async fn select_backend(
        &self,
        backends: &[&DiscoveredBackend],
        _context: &RequestContext,
    ) -> Option<SocketAddr> {
        if backends.is_empty() {
            return None;
        }

        // Simple weighted selection based on backend weights
        let total_weight: f64 = backends.iter().map(|b| b.weight).sum();
        let mut random_weight = rand::random::<f64>() * total_weight;

        for backend in backends {
            random_weight -= backend.weight;
            if random_weight <= 0.0 {
                return Some(backend.address);
            }
        }

        // Fallback to first backend
        Some(backends[0].address)
    }
}

/// Least connections backend selection
pub struct LeastConnectionsStrategy;

impl LeastConnectionsStrategy {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl BackendSelectionStrategy for LeastConnectionsStrategy {
    async fn select_backend(
        &self,
        backends: &[&DiscoveredBackend],
        _context: &RequestContext,
    ) -> Option<SocketAddr> {
        backends.iter()
            .min_by_key(|backend| backend.active_connections)
            .map(|backend| backend.address)
    }
}

/// Health-based backend selection
pub struct HealthBasedStrategy;

impl HealthBasedStrategy {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl BackendSelectionStrategy for HealthBasedStrategy {
    async fn select_backend(
        &self,
        backends: &[&DiscoveredBackend],
        _context: &RequestContext,
    ) -> Option<SocketAddr> {
        // Prefer healthier backends
        backends.iter()
            .filter(|backend| matches!(backend.health_state, HealthState::Healthy))
            .min_by_key(|backend| backend.active_connections)
            .or_else(|| {
                // Fallback to degraded backends
                backends.iter()
                    .filter(|backend| matches!(backend.health_state, HealthState::Degraded))
                    .min_by_key(|backend| backend.active_connections)
            })
            .map(|backend| backend.address)
    }
}

/// Capability-based backend selection
pub struct CapabilityBasedStrategy;

impl CapabilityBasedStrategy {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl BackendSelectionStrategy for CapabilityBasedStrategy {
    async fn select_backend(
        &self,
        backends: &[&DiscoveredBackend],
        context: &RequestContext,
    ) -> Option<SocketAddr> {
        // Filter backends that have required capabilities
        let capable_backends: Vec<&DiscoveredBackend> = backends.iter()
            .filter(|backend| {
                context.required_capabilities.iter()
                    .all(|cap| backend.capabilities.contains(cap))
            })
            .copied()
            .collect();

        if capable_backends.is_empty() {
            return backends.first().map(|b| b.address); // Fallback
        }

        // Select least loaded among capable backends
        capable_backends.iter()
            .min_by_key(|backend| backend.active_connections)
            .map(|backend| backend.address)
    }
}

/// Version-based backend selection
pub struct VersionBasedStrategy;

impl VersionBasedStrategy {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl BackendSelectionStrategy for VersionBasedStrategy {
    async fn select_backend(
        &self,
        backends: &[&DiscoveredBackend],
        context: &RequestContext,
    ) -> Option<SocketAddr> {
        if let Some(ref preferred_version) = context.preferred_version {
            // Try to find backend with preferred version
            if let Some(backend) = backends.iter()
                .find(|backend| backend.metadata.version == *preferred_version) {
                return Some(backend.address);
            }
        }

        // Fallback to any available backend
        backends.first().map(|backend| backend.address)
    }
}

/// Geography-based backend selection
pub struct GeographyBasedStrategy;

impl GeographyBasedStrategy {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl BackendSelectionStrategy for GeographyBasedStrategy {
    async fn select_backend(
        &self,
        backends: &[&DiscoveredBackend],
        _context: &RequestContext,
    ) -> Option<SocketAddr> {
        // In a real implementation, this would consider geographic proximity
        // For now, just return the first backend
        backends.first().map(|backend| backend.address)
    }
}

/// Consul service registry implementation
pub struct ConsulRegistry {
    config: RegistryConfig,
    client_url: String,
}

impl ConsulRegistry {
    pub async fn new(config: RegistryConfig) -> Result<Self> {
        let client_url = config.connection.get("url")
            .unwrap_or(&"http://localhost:8500".to_string())
            .clone();

        info!("🔗 Connecting to Consul registry: {}", client_url);

        Ok(Self {
            config,
            client_url,
        })
    }
}

#[async_trait::async_trait]
impl ServiceRegistry for ConsulRegistry {
    async fn discover_services(&self, service_name: &str, _tags: &[String]) -> Result<Vec<ServiceInstance>> {
        // Consul service discovery implementation would go here
        // For now, return empty list
        debug!("🔍 Discovering services from Consul: {}", service_name);
        Ok(Vec::new())
    }

    async fn register_service(&self, _instance: &ServiceInstance) -> Result<()> {
        // Consul service registration implementation would go here
        Ok(())
    }

    async fn deregister_service(&self, _instance: &ServiceInstance) -> Result<()> {
        // Consul service deregistration implementation would go here
        Ok(())
    }

    async fn watch_services(&self, _service_name: &str) -> Result<tokio::sync::mpsc::Receiver<ServiceEvent>> {
        // Consul service watching implementation would go here
        let (_tx, rx) = tokio::sync::mpsc::channel(100);
        Ok(rx)
    }

    async fn health_check(&self) -> Result<bool> {
        // Consul health check implementation would go here
        Ok(true)
    }
}

/// etcd service registry implementation
pub struct EtcdRegistry {
    config: RegistryConfig,
}

impl EtcdRegistry {
    pub async fn new(config: RegistryConfig) -> Result<Self> {
        info!("🔗 Connecting to etcd registry");
        Ok(Self { config })
    }
}

#[async_trait::async_trait]
impl ServiceRegistry for EtcdRegistry {
    async fn discover_services(&self, service_name: &str, _tags: &[String]) -> Result<Vec<ServiceInstance>> {
        debug!("🔍 Discovering services from etcd: {}", service_name);
        Ok(Vec::new())
    }

    async fn register_service(&self, _instance: &ServiceInstance) -> Result<()> {
        Ok(())
    }

    async fn deregister_service(&self, _instance: &ServiceInstance) -> Result<()> {
        Ok(())
    }

    async fn watch_services(&self, _service_name: &str) -> Result<tokio::sync::mpsc::Receiver<ServiceEvent>> {
        let (_tx, rx) = tokio::sync::mpsc::channel(100);
        Ok(rx)
    }

    async fn health_check(&self) -> Result<bool> {
        Ok(true)
    }
}

/// Kubernetes service registry implementation
pub struct KubernetesRegistry {
    config: RegistryConfig,
}

impl KubernetesRegistry {
    pub async fn new(config: RegistryConfig) -> Result<Self> {
        info!("🔗 Connecting to Kubernetes API");
        Ok(Self { config })
    }
}

#[async_trait::async_trait]
impl ServiceRegistry for KubernetesRegistry {
    async fn discover_services(&self, service_name: &str, _tags: &[String]) -> Result<Vec<ServiceInstance>> {
        debug!("🔍 Discovering services from Kubernetes: {}", service_name);
        Ok(Vec::new())
    }

    async fn register_service(&self, _instance: &ServiceInstance) -> Result<()> {
        Ok(())
    }

    async fn deregister_service(&self, _instance: &ServiceInstance) -> Result<()> {
        Ok(())
    }

    async fn watch_services(&self, _service_name: &str) -> Result<tokio::sync::mpsc::Receiver<ServiceEvent>> {
        let (_tx, rx) = tokio::sync::mpsc::channel(100);
        Ok(rx)
    }

    async fn health_check(&self) -> Result<bool> {
        Ok(true)
    }
}

/// Eureka service registry implementation
pub struct EurekaRegistry {
    config: RegistryConfig,
}

impl EurekaRegistry {
    pub async fn new(config: RegistryConfig) -> Result<Self> {
        info!("🔗 Connecting to Eureka registry");
        Ok(Self { config })
    }
}

#[async_trait::async_trait]
impl ServiceRegistry for EurekaRegistry {
    async fn discover_services(&self, service_name: &str, _tags: &[String]) -> Result<Vec<ServiceInstance>> {
        debug!("🔍 Discovering services from Eureka: {}", service_name);
        Ok(Vec::new())
    }

    async fn register_service(&self, _instance: &ServiceInstance) -> Result<()> {
        Ok(())
    }

    async fn deregister_service(&self, _instance: &ServiceInstance) -> Result<()> {
        Ok(())
    }

    async fn watch_services(&self, _service_name: &str) -> Result<tokio::sync::mpsc::Receiver<ServiceEvent>> {
        let (_tx, rx) = tokio::sync::mpsc::channel(100);
        Ok(rx)
    }

    async fn health_check(&self) -> Result<bool> {
        Ok(true)
    }
}

/// Zookeeper service registry implementation
pub struct ZookeeperRegistry {
    config: RegistryConfig,
}

impl ZookeeperRegistry {
    pub async fn new(config: RegistryConfig) -> Result<Self> {
        info!("🔗 Connecting to Zookeeper registry");
        Ok(Self { config })
    }
}

#[async_trait::async_trait]
impl ServiceRegistry for ZookeeperRegistry {
    async fn discover_services(&self, service_name: &str, _tags: &[String]) -> Result<Vec<ServiceInstance>> {
        debug!("🔍 Discovering services from Zookeeper: {}", service_name);
        Ok(Vec::new())
    }

    async fn register_service(&self, _instance: &ServiceInstance) -> Result<()> {
        Ok(())
    }

    async fn deregister_service(&self, _instance: &ServiceInstance) -> Result<()> {
        Ok(())
    }

    async fn watch_services(&self, _service_name: &str) -> Result<tokio::sync::mpsc::Receiver<ServiceEvent>> {
        let (_tx, rx) = tokio::sync::mpsc::channel(100);
        Ok(rx)
    }

    async fn health_check(&self) -> Result<bool> {
        Ok(true)
    }
}

/// DNS-based service registry implementation
pub struct DNSRegistry {
    config: RegistryConfig,
}

impl DNSRegistry {
    pub async fn new(config: RegistryConfig) -> Result<Self> {
        info!("🔗 Initializing DNS-based service discovery");
        Ok(Self { config })
    }
}

#[async_trait::async_trait]
impl ServiceRegistry for DNSRegistry {
    async fn discover_services(&self, service_name: &str, _tags: &[String]) -> Result<Vec<ServiceInstance>> {
        debug!("🔍 Discovering services via DNS: {}", service_name);

        // DNS-based service discovery would resolve SRV records
        // For now, return empty list
        Ok(Vec::new())
    }

    async fn register_service(&self, _instance: &ServiceInstance) -> Result<()> {
        // DNS registration is typically handled by the DNS server
        Ok(())
    }

    async fn deregister_service(&self, _instance: &ServiceInstance) -> Result<()> {
        // DNS deregistration is typically handled by the DNS server
        Ok(())
    }

    async fn watch_services(&self, _service_name: &str) -> Result<tokio::sync::mpsc::Receiver<ServiceEvent>> {
        // DNS doesn't support real-time watching, would need polling
        let (_tx, rx) = tokio::sync::mpsc::channel(100);
        Ok(rx)
    }

    async fn health_check(&self) -> Result<bool> {
        Ok(true)
    }
}

impl Default for DiscoveryConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            registries: vec![
                RegistryConfig {
                    name: "consul".to_string(),
                    registry_type: RegistryType::Consul,
                    connection: {
                        let mut conn = HashMap::new();
                        conn.insert("url".to_string(), "http://localhost:8500".to_string());
                        conn
                    },
                    priority: 100,
                    enabled: false,
                }
            ],
            polling_interval: Duration::from_secs(30),
            enable_watching: true,
            validation_timeout: Duration::from_secs(30),
            service_name: "api".to_string(),
            service_tags: Vec::new(),
            environment: None,
            enable_health_integration: true,
            selection_strategy: SelectionStrategy::HealthBased,
            enable_advanced_deployments: false,
            canary_config: CanaryConfig {
                enabled: false,
                initial_weight: 0.05,
                max_weight: 1.0,
                weight_step: 0.05,
                step_interval: Duration::from_secs(300),
                success_criteria: SuccessCriteria {
                    min_success_rate: 0.99,
                    max_error_rate: 0.01,
                    max_response_time: Duration::from_millis(500),
                    min_sample_size: 100,
                    evaluation_period: Duration::from_secs(300),
                },
            },
            blue_green_config: BlueGreenConfig {
                enabled: false,
                validation_period: Duration::from_secs(300),
                auto_switch: false,
                keep_old_version: true,
                rollback_timeout: Duration::from_secs(600),
            },
            enable_service_mesh: false,
            service_mesh_config: ServiceMeshConfig {
                mesh_type: ServiceMeshType::Istio,
                config: HashMap::new(),
            },
        }
    }
}
```

## Integration Example

Create `examples/dynamic_discovery_example.rs`:

```rust
//! Example demonstrating dynamic backend discovery with multiple registries

use rusty_balancer::discovery::{
    DynamicDiscoveryManager, DiscoveryConfig, RegistryConfig, RegistryType,
    SelectionStrategy, RequestContext
};
use std::collections::{HashMap, HashSet};
use std::time::Duration;
use tracing::{info, warn};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("🔍 Starting Dynamic Backend Discovery Example");

    // Create discovery configuration
    let config = DiscoveryConfig {
        enabled: true,
        registries: vec![
            RegistryConfig {
                name: "consul-primary".to_string(),
                registry_type: RegistryType::Consul,
                connection: {
                    let mut conn = HashMap::new();
                    conn.insert("url".to_string(), "http://localhost:8500".to_string());
                    conn
                },
                priority: 100,
                enabled: true,
            },
            RegistryConfig {
                name: "kubernetes".to_string(),
                registry_type: RegistryType::Kubernetes,
                connection: {
                    let mut conn = HashMap::new();
                    conn.insert("kubeconfig".to_string(), "~/.kube/config".to_string());
                    conn.insert("namespace".to_string(), "default".to_string());
                    conn
                },
                priority: 90,
                enabled: true,
            },
            RegistryConfig {
                name: "dns-fallback".to_string(),
                registry_type: RegistryType::DNS,
                connection: {
                    let mut conn = HashMap::new();
                    conn.insert("domain".to_string(), "service.local".to_string());
                    conn
                },
                priority: 50,
                enabled: true,
            },
        ],
        polling_interval: Duration::from_secs(15),
        enable_watching: true,
        validation_timeout: Duration::from_secs(20),
        service_name: "api-service".to_string(),
        service_tags: vec!["production".to_string(), "v2".to_string()],
        environment: Some("production".to_string()),
        enable_health_integration: true,
        selection_strategy: SelectionStrategy::HealthBased,
        enable_advanced_deployments: true,
        canary_config: rusty_balancer::discovery::CanaryConfig {
            enabled: true,
            initial_weight: 0.1,
            max_weight: 1.0,
            weight_step: 0.1,
            step_interval: Duration::from_secs(120),
            success_criteria: rusty_balancer::discovery::SuccessCriteria {
                min_success_rate: 0.95,
                max_error_rate: 0.05,
                max_response_time: Duration::from_millis(1000),
                min_sample_size: 50,
                evaluation_period: Duration::from_secs(180),
            },
        },
        ..Default::default()
    };

    // Create discovery manager
    let discovery_manager = DynamicDiscoveryManager::new(config);
    discovery_manager.start().await?;

    info!("✅ Dynamic discovery manager started");

    // Simulate service discovery and backend selection
    info!("🔄 Simulating service discovery...");

    for round in 1..=10 {
        info!("📊 Discovery round {}", round);

        // Get available backends
        let backends = discovery_manager.get_available_backends().await;
        info!("🎯 Available backends: {:?}", backends);

        if !backends.is_empty() {
            // Test different request contexts
            let test_contexts = vec![
                RequestContext {
                    path: "/api/users".to_string(),
                    headers: {
                        let mut headers = HashMap::new();
                        headers.insert("Accept".to_string(), "application/json".to_string());
                        headers
                    },
                    client_ip: Some("*************".parse()?),
                    required_capabilities: {
                        let mut caps = HashSet::new();
                        caps.insert("user-management".to_string());
                        caps
                    },
                    preferred_version: Some("v2.1.0".to_string()),
                    metadata: HashMap::new(),
                },
                RequestContext {
                    path: "/api/orders".to_string(),
                    headers: {
                        let mut headers = HashMap::new();
                        headers.insert("Authorization".to_string(), "Bearer token123".to_string());
                        headers
                    },
                    client_ip: Some("*************".parse()?),
                    required_capabilities: {
                        let mut caps = HashSet::new();
                        caps.insert("order-processing".to_string());
                        caps
                    },
                    preferred_version: Some("v2.0.0".to_string()),
                    metadata: HashMap::new(),
                },
                RequestContext {
                    path: "/api/analytics".to_string(),
                    headers: HashMap::new(),
                    client_ip: Some("*************".parse()?),
                    required_capabilities: {
                        let mut caps = HashSet::new();
                        caps.insert("analytics".to_string());
                        caps.insert("high-memory".to_string());
                        caps
                    },
                    preferred_version: None,
                    metadata: HashMap::new(),
                },
            ];

            for (i, context) in test_contexts.iter().enumerate() {
                if let Some(selected_backend) = discovery_manager.select_backend(context).await {
                    info!("🎯 Request {} ({}): Selected backend {}",
                          i + 1, context.path, selected_backend);

                    // Get backend metadata
                    if let Some(metadata) = discovery_manager.get_backend_metadata(selected_backend).await {
                        info!("  📋 Backend metadata: version={}, env={}, tags={:?}",
                              metadata.version, metadata.environment, metadata.tags);
                        info!("  🔧 Capabilities: {:?}", metadata.capabilities);
                    }
                } else {
                    warn!("❌ No suitable backend found for request {}", i + 1);
                }
            }
        } else {
            warn!("⚠️ No backends available in round {}", round);
        }

        // Wait before next round
        tokio::time::sleep(Duration::from_secs(20)).await;
    }

    // Test advanced deployment patterns
    info!("🚀 Testing advanced deployment patterns");

    // Simulate canary deployment
    info!("🐤 Simulating canary deployment scenario");
    info!("  - New version v2.2.0 deployed with 10% traffic");
    info!("  - Monitoring success metrics...");
    info!("  - Gradually increasing traffic based on success criteria");

    // Simulate blue-green deployment
    info!("🔵🟢 Simulating blue-green deployment scenario");
    info!("  - Blue environment: v2.1.0 (current production)");
    info!("  - Green environment: v2.2.0 (new version)");
    info!("  - Validating green environment...");
    info!("  - Ready for traffic switch");

    // Test service mesh integration
    info!("🕸️ Testing service mesh integration");
    info!("  - Integrating with Istio service mesh");
    info!("  - Configuring traffic policies");
    info!("  - Setting up circuit breakers");
    info!("  - Enabling distributed tracing");

    // Test different selection strategies
    info!("🎯 Testing backend selection strategies");

    let strategies = [
        "Round Robin",
        "Weighted Round Robin",
        "Least Connections",
        "Health-based",
        "Capability-based",
        "Version-based",
        "Geography-based",
    ];

    for strategy in &strategies {
        info!("📊 Testing {} strategy", strategy);

        // In a real implementation, you would:
        // 1. Switch the selection strategy
        // 2. Send test requests
        // 3. Measure performance and distribution
        // 4. Compare results

        info!("  ✅ {} strategy test completed", strategy);
    }

    // Test service registry failover
    info!("🔄 Testing service registry failover");

    info!("  📡 Primary registry (Consul): Active");
    info!("  📡 Secondary registry (Kubernetes): Standby");
    info!("  📡 Fallback registry (DNS): Available");

    // Simulate primary registry failure
    info!("  ❌ Simulating Consul registry failure");
    info!("  🔄 Failing over to Kubernetes registry");
    info!("  ✅ Service discovery continues seamlessly");

    // Final statistics
    info!("📊 Final Discovery Statistics");
    let final_backends = discovery_manager.get_available_backends().await;

    info!("  Total backends discovered: {}", final_backends.len());
    info!("  Active registries: 3 (Consul, Kubernetes, DNS)");
    info!("  Selection strategy: Health-based");
    info!("  Advanced deployments: Enabled");
    info!("  Service mesh integration: Enabled");

    for (i, backend) in final_backends.iter().enumerate() {
        info!("  Backend {}: {}", i + 1, backend);

        if let Some(metadata) = discovery_manager.get_backend_metadata(*backend).await {
            info!("    Version: {}", metadata.version);
            info!("    Environment: {}", metadata.environment);
            info!("    Deployment: {:?}", metadata.deployment.deployment_type);

            if let Some(canary_weight) = metadata.deployment.canary_weight {
                info!("    Canary weight: {:.1}%", canary_weight * 100.0);
            }

            if let Some(bg_slot) = metadata.deployment.blue_green_slot {
                info!("    Blue-Green slot: {:?}", bg_slot);
            }
        }
    }

    info!("✅ Dynamic backend discovery example completed");

    Ok(())
}
```

## Key Design Decisions Explained

### 1. Multi-Registry Support
**Decision**: Support multiple service registries with priority-based failover

**Rationale**:
- **Flexibility**: Different environments use different service registries
- **Reliability**: Failover between registries for high availability
- **Migration**: Support gradual migration between registry systems
- **Hybrid Deployments**: Handle multi-cloud and hybrid environments

### 2. Intelligent Backend Selection
**Decision**: Multiple selection strategies based on different criteria

**Rationale**:
- **Performance**: Route traffic to optimal backends based on current conditions
- **Capabilities**: Match requests to backends with required capabilities
- **Deployment Safety**: Support advanced deployment patterns safely
- **Business Logic**: Enable routing based on business requirements

### 3. Advanced Deployment Pattern Support
**Decision**: Built-in support for canary and blue-green deployments

**Rationale**:
- **Risk Mitigation**: Reduce deployment risks through gradual rollouts
- **Automated Validation**: Automatic success criteria evaluation
- **Rollback Capability**: Quick rollback on deployment issues
- **Production Safety**: Safe deployment practices for critical services

## Performance Considerations

1. **Discovery Efficiency**: Optimize polling intervals and caching strategies
2. **Selection Speed**: Fast backend selection algorithms for low latency
3. **Memory Usage**: Efficient storage of service metadata and state
4. **Network Overhead**: Minimize registry communication overhead

## Security Considerations

1. **Registry Security**: Secure communication with service registries
2. **Service Authentication**: Validate service identity and authorization
3. **Metadata Security**: Protect sensitive service metadata
4. **Access Control**: Secure discovery management interfaces

## Navigation
- [Previous: Advanced Health Checks](20-advanced-health-checks.md)
- [Next: Course Conclusion](conclusion.md)
