# Module 07: Health Checking System

## Learning Objectives
- Master comprehensive health checking strategies for production load balancers
- Implement sophisticated active and passive health checks with circuit breaker patterns
- Build intelligent health monitoring with adaptive thresholds and recovery detection
- Integrate health checking with load balancing algorithms and failover mechanisms
- Understand health check performance optimization and resource management
- Implement health check observability, alerting, and operational dashboards

## Prerequisites
- Completion of Module 06: HTTP Protocol Support
- Deep understanding of Rust's async programming and tokio runtime
- Familiarity with HTTP protocol details and error handling patterns
- Knowledge of distributed systems concepts and failure modes

## Navigation
- [Previous: HTTP Protocol Support](06-http-protocol.md)
- [Next: Configuration System](08-configuration-system.md)
- [Table of Contents](01-introduction.md#table-of-contents)

---

## Why Health Checking is Critical

Health checking is the cornerstone of reliable load balancing. Without proper health monitoring, load balancers become single points of failure that can cascade problems throughout your infrastructure.

### The Cost of Unhealthy Backends

**Without Health Checks**:
```
Client Request → Load Balancer → Failed Backend → Timeout (30s)
Result: Poor user experience, wasted resources, cascading failures
```

**With Health Checks**:
```
Client Request → Load Balancer → Healthy Backend → Fast Response (100ms)
Failed Backend: Detected and removed from rotation in 5s
Result: Reliable service, optimal performance, fault isolation
```

### Real-World Failure Scenarios

1. **Application Crashes**: Process dies but port remains bound
2. **Database Connection Loss**: App runs but can't serve requests
3. **Memory Exhaustion**: Slow responses leading to timeouts
4. **Network Partitions**: Backend unreachable from load balancer
5. **Dependency Failures**: External service outages affecting backend health
6. **Resource Exhaustion**: CPU/disk saturation causing degraded performance

### Health Check Strategy Matrix

| Check Type | Detection Speed | Resource Usage | Accuracy | Use Case |
|------------|----------------|----------------|----------|----------|
| **Active HTTP** | Fast (1-5s) | Medium | High | Web applications |
| **Active TCP** | Very Fast (<1s) | Low | Medium | Any TCP service |
| **Passive** | Variable | Very Low | High | Real traffic analysis |
| **Application-Specific** | Fast | Medium | Very High | Custom protocols |
| **Dependency Checks** | Medium (5-10s) | High | Very High | Complex applications |

## Comprehensive Health Checking Architecture

### Health Check Types and Patterns

```mermaid
graph TD
    subgraph "Health Check Types"
        Active[Active Health Checks<br/>Proactive Monitoring]
        Passive[Passive Health Checks<br/>Traffic-Based Detection]
        Hybrid[Hybrid Approach<br/>Combined Strategy]
    end

    subgraph "Active Checks"
        HTTP[HTTP Health Endpoint]
        TCP[TCP Connection Test]
        Custom[Custom Protocol Check]
        Deep[Deep Health Check]
    end

    subgraph "Passive Checks"
        ErrorRate[Error Rate Monitoring]
        ResponseTime[Response Time Analysis]
        CircuitBreaker[Circuit Breaker Pattern]
    end

    Active --> HTTP
    Active --> TCP
    Active --> Custom
    Active --> Deep

    Passive --> ErrorRate
    Passive --> ResponseTime
    Passive --> CircuitBreaker

    Hybrid --> Active
    Hybrid --> Passive
```

### Advanced Health Check Implementation

Let's build a comprehensive health checking system with multiple strategies:

```rust
use std::sync::{Arc, RwLock};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::time::{Duration, Instant};
use tokio::time::{interval, timeout};
use hyper::{Client, Body, Request, Method, StatusCode};
use hyper::client::HttpConnector;
use tracing::{info, warn, error, debug, instrument};
use serde::{Serialize, Deserialize};

/// Comprehensive health monitoring system
pub struct AdvancedHealthMonitor {
    /// Backend health states
    backend_health: Arc<RwLock<HashMap<SocketAddr, BackendHealth>>>,

    /// HTTP client for health checks
    client: Client<HttpConnector>,

    /// Health check configuration
    config: HealthCheckConfig,

    /// Health check statistics
    stats: Arc<RwLock<HealthCheckStats>>,
}

/// Detailed health state for each backend
#[derive(Debug, Clone)]
pub struct BackendHealth {
    /// Current health status
    pub status: HealthStatus,

    /// Last successful health check
    pub last_success: Option<Instant>,

    /// Last failed health check
    pub last_failure: Option<Instant>,

    /// Consecutive failure count
    pub consecutive_failures: u32,

    /// Consecutive success count (for recovery)
    pub consecutive_successes: u32,

    /// Response time history (for performance-based health)
    pub response_times: Vec<Duration>,

    /// Error rate over time window
    pub error_rate: f64,

    /// Circuit breaker state
    pub circuit_state: CircuitBreakerState,

    /// Health check metadata
    pub metadata: HealthMetadata,
}

/// Health status with detailed states
#[derive(Debug, Clone, PartialEq)]
pub enum HealthStatus {
    /// Backend is healthy and accepting traffic
    Healthy,

    /// Backend is unhealthy, no traffic
    Unhealthy,

    /// Backend is recovering, limited traffic
    Recovering,

    /// Backend is being drained
    Draining,

    /// Backend is disabled by administrator
    Disabled,

    /// Health check is in progress
    Checking,
}

/// Circuit breaker states for advanced failure handling
#[derive(Debug, Clone, PartialEq)]
pub enum CircuitBreakerState {
    /// Normal operation
    Closed,

    /// Failure threshold exceeded, no requests allowed
    Open { opened_at: Instant },

    /// Testing if backend has recovered
    HalfOpen,
}

/// Health check metadata and diagnostics
#[derive(Debug, Clone)]
pub struct HealthMetadata {
    /// Total health checks performed
    pub checks_performed: u64,

    /// Health check success rate
    pub success_rate: f64,

    /// Average response time
    pub avg_response_time: Duration,

    /// Last health check details
    pub last_check_details: Option<HealthCheckResult>,

    /// Backend-specific configuration
    pub backend_config: BackendHealthConfig,
}

/// Configuration for health checking behavior
#[derive(Debug, Clone)]
pub struct HealthCheckConfig {
    /// Interval between health checks
    pub check_interval: Duration,

    /// Timeout for each health check
    pub check_timeout: Duration,

    /// Number of consecutive failures before marking unhealthy
    pub failure_threshold: u32,

    /// Number of consecutive successes before marking healthy
    pub success_threshold: u32,

    /// HTTP health check configuration
    pub http_config: HttpHealthConfig,

    /// Circuit breaker configuration
    pub circuit_breaker_config: CircuitBreakerConfig,

    /// Performance-based health thresholds
    pub performance_thresholds: PerformanceThresholds,
}

/// HTTP-specific health check configuration
#[derive(Debug, Clone)]
pub struct HttpHealthConfig {
    /// Health check endpoint path
    pub path: String,

    /// HTTP method to use
    pub method: Method,

    /// Expected status codes for healthy response
    pub expected_status_codes: Vec<StatusCode>,

    /// Expected response body patterns
    pub expected_body_patterns: Vec<String>,

    /// Custom headers to send
    pub headers: HashMap<String, String>,

    /// Follow redirects
    pub follow_redirects: bool,
}

/// Circuit breaker configuration
#[derive(Debug, Clone)]
pub struct CircuitBreakerConfig {
    /// Failure rate threshold to open circuit (0.0-1.0)
    pub failure_rate_threshold: f64,

    /// Minimum number of requests before circuit can open
    pub minimum_request_threshold: u32,

    /// Time to wait before attempting recovery
    pub recovery_timeout: Duration,

    /// Number of test requests in half-open state
    pub half_open_max_requests: u32,
}

/// Performance-based health thresholds
#[derive(Debug, Clone)]
pub struct PerformanceThresholds {
    /// Maximum acceptable response time
    pub max_response_time: Duration,

    /// Maximum acceptable error rate (0.0-1.0)
    pub max_error_rate: f64,

    /// Time window for performance calculations
    pub performance_window: Duration,

    /// Minimum requests in window for valid calculation
    pub min_requests_for_calculation: u32,
}

/// Per-backend health configuration
#[derive(Debug, Clone)]
pub struct BackendHealthConfig {
    /// Override global check interval for this backend
    pub check_interval_override: Option<Duration>,

    /// Backend-specific health endpoint
    pub health_endpoint_override: Option<String>,

    /// Backend weight for weighted health checks
    pub health_check_weight: f64,

    /// Enable/disable specific check types
    pub enabled_check_types: Vec<HealthCheckType>,
}

/// Types of health checks available
#[derive(Debug, Clone, PartialEq)]
pub enum HealthCheckType {
    HttpGet,
    HttpPost,
    TcpConnect,
    CustomScript,
    DependencyCheck,
}

/// Result of a health check operation
#[derive(Debug, Clone)]
pub struct HealthCheckResult {
    /// Backend that was checked
    pub backend: SocketAddr,

    /// Type of check performed
    pub check_type: HealthCheckType,

    /// Whether the check succeeded
    pub success: bool,

    /// Response time
    pub response_time: Duration,

    /// HTTP status code (if applicable)
    pub status_code: Option<StatusCode>,

    /// Error message (if failed)
    pub error_message: Option<String>,

    /// Timestamp of the check
    pub timestamp: Instant,

    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// Health check statistics for monitoring
#[derive(Debug, Clone, Default)]
pub struct HealthCheckStats {
    /// Total checks performed
    pub total_checks: u64,

    /// Total successful checks
    pub successful_checks: u64,

    /// Total failed checks
    pub failed_checks: u64,

    /// Average check duration
    pub avg_check_duration: Duration,

    /// Checks per second
    pub checks_per_second: f64,

    /// Backend status distribution
    pub status_distribution: HashMap<HealthStatus, u32>,
}

    pub fn is_healthy(&self, addr: &SocketAddr) -> bool {
        *self.health.lock().unwrap().get(addr).unwrap_or(&false)
    }

    pub fn health_map(&self) -> Arc<Mutex<HashMap<SocketAddr, bool>>> {
        Arc::clone(&self.health)
    }

    pub async fn start(self, backends: Vec<SocketAddr>) {
        let health = self.health;
        tokio::spawn(async move {
            let client = Client::new();
            let mut interval = interval(Duration::from_secs(5));
            loop {
                interval.tick().await;
                for &backend in &backends {
                    let req = Request::builder()
                        .uri(format!("http://{}/health", backend))
                        .body(Body::empty())
                        .unwrap();
                    let healthy = client.request(req).await.is_ok();
                    health.lock().unwrap().insert(backend, healthy);
                }
            }
        });
    }
}
```

---

## Integrating Health Checks with Round-Robin

Update your round-robin selector to skip unhealthy backends:

```rust
pub fn next_healthy(&self, health: &HealthMonitor) -> Option<SocketAddr> {
    let mut idx = self.index.lock().unwrap();
    for _ in 0..self.backends.len() {
        let addr = self.backends[*idx];
        *idx = (*idx + 1) % self.backends.len();
        if health.is_healthy(&addr) {
            return Some(addr);
        }
    }
    None // No healthy backends
}
```

---

## Passive Health Checks

If a real request to a backend fails, mark it as unhealthy for a cooldown period. This can be combined with active checks for better reliability.

---

## Summary
- Health checks prevent routing to failed servers
- Combine active and passive checks for robustness
- Integrate health status with backend selection

---

## Next Steps
Continue to [Module 06: Configuration System](06-configuration-system.md) to make your load balancer flexible and user-configurable.
