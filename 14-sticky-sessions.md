# Module 14: Sticky Sessions

## Learning Objectives
- Implement session affinity and sticky sessions for stateful applications
- Design cookie-based and IP-based session persistence mechanisms
- Build session failover and migration strategies for high availability
- Create session monitoring and analytics for operational insights
- Understand sticky session patterns and trade-offs in distributed systems

## Why Sticky Sessions Matter

Sticky sessions ensure client requests are consistently routed to the same backend server:

1. **Stateful Applications**: Maintain server-side session state without external storage
2. **Performance**: Avoid session data synchronization overhead between servers
3. **User Experience**: Consistent application behavior and cached data access
4. **Legacy Support**: Enable load balancing for applications not designed for statelessness
5. **Gradual Migration**: Support transition from stateful to stateless architectures

### Sticky Sessions Architecture

```mermaid
graph TD
    Client[Client Browser] --> LB[Load Balancer]
    LB --> SM[Session Manager]
    
    SM --> CB[Cookie-Based Affinity]
    SM --> IB[IP-Based Affinity]
    SM --> HB[Header-Based Affinity]
    
    subgraph "Session Storage"
        ST[Session Table]
        Cache[Session Cache]
        DB[Session Database]
    end
    
    SM --> ST
    ST --> Cache
    Cache --> DB
    
    subgraph "Backend Servers"
        B1[Backend 1<br/>Sessions: A, D, G]
        B2[Backend 2<br/>Sessions: B, E, H]
        B3[Backend 3<br/>Sessions: C, F, I]
    end
    
    CB --> B1
    CB --> B2
    CB --> B3
    
    IB --> B1
    IB --> B2
    IB --> B3
    
    HB --> B1
    HB --> B2
    HB --> B3
    
    subgraph "Failover & Migration"
        Monitor[Health Monitor]
        Migrate[Session Migration]
        Backup[Session Backup]
    end
    
    Monitor --> B1
    Monitor --> B2
    Monitor --> B3
    
    Monitor --> Migrate
    Migrate --> Backup
    Backup --> ST
    
    subgraph "Analytics"
        Metrics[Session Metrics]
        Reports[Session Reports]
        Alerts[Session Alerts]
    end
    
    SM --> Metrics
    Metrics --> Reports
    Metrics --> Alerts
```

## Sticky Sessions Implementation

### Design Decisions

**Why multiple affinity methods?**
- **Flexibility**: Different applications require different session identification methods
- **Compatibility**: Support various client types and network configurations
- **Reliability**: Fallback mechanisms when primary method fails
- **Security**: Choose appropriate method based on security requirements

**Why session failover and migration?**
- **High Availability**: Maintain sessions when servers fail or are taken offline
- **Load Balancing**: Redistribute sessions during server maintenance
- **Performance**: Move sessions from overloaded to underutilized servers
- **Scalability**: Support dynamic server pool changes

Create `src/session/mod.rs`:

```rust
//! Sticky sessions and session affinity management
//!
//! This module provides comprehensive session affinity capabilities including:
//! - Cookie-based, IP-based, and header-based session persistence
//! - Session failover and migration for high availability
//! - Session analytics and monitoring
//! - Configurable session timeout and cleanup
//! - Integration with health checking and load balancing

pub mod affinity;
pub mod storage;
pub mod failover;
pub mod analytics;

use crate::error::{LoadBalancerError, Result};
use crate::metrics::LoadBalancerMetrics;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::RwLock;
use tracing::{info, warn, debug, trace};
use uuid::Uuid;

/// Session manager for handling sticky sessions
pub struct SessionManager {
    /// Session storage
    storage: Arc<SessionStorage>,
    
    /// Session configuration
    config: SessionConfig,
    
    /// Metrics collector
    metrics: Option<Arc<LoadBalancerMetrics>>,
    
    /// Session analytics
    analytics: Arc<SessionAnalytics>,
}

/// Session configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionConfig {
    /// Enable sticky sessions
    pub enabled: bool,
    
    /// Primary affinity method
    pub primary_affinity: AffinityMethod,
    
    /// Fallback affinity methods
    pub fallback_affinity: Vec<AffinityMethod>,
    
    /// Session timeout duration
    pub session_timeout: Duration,
    
    /// Cookie configuration
    pub cookie_config: CookieConfig,
    
    /// Session cleanup interval
    pub cleanup_interval: Duration,
    
    /// Maximum sessions per backend
    pub max_sessions_per_backend: usize,
    
    /// Enable session migration
    pub enable_session_migration: bool,
    
    /// Session migration threshold (load difference)
    pub migration_threshold: f64,
    
    /// Enable session backup
    pub enable_session_backup: bool,
    
    /// Session persistence mode
    pub persistence_mode: PersistenceMode,
}

/// Session affinity methods
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum AffinityMethod {
    Cookie,
    ClientIP,
    Header,
    URLParameter,
    ConsistentHash,
}

/// Cookie configuration for session affinity
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CookieConfig {
    /// Cookie name
    pub name: String,
    
    /// Cookie domain
    pub domain: Option<String>,
    
    /// Cookie path
    pub path: String,
    
    /// Cookie is secure (HTTPS only)
    pub secure: bool,
    
    /// Cookie is HTTP only
    pub http_only: bool,
    
    /// Cookie SameSite attribute
    pub same_site: SameSite,
    
    /// Cookie max age (None for session cookie)
    pub max_age: Option<Duration>,
}

/// SameSite cookie attribute
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum SameSite {
    Strict,
    Lax,
    None,
}

/// Session persistence modes
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum PersistenceMode {
    /// In-memory only (lost on restart)
    Memory,
    
    /// Persistent storage (survives restart)
    Persistent,
    
    /// Distributed storage (shared across load balancers)
    Distributed,
}

/// Session information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    /// Unique session ID
    pub id: String,
    
    /// Backend server handling this session
    pub backend_address: SocketAddr,
    
    /// Session creation time
    pub created_at: SystemTime,
    
    /// Last access time
    pub last_accessed: SystemTime,
    
    /// Session data (optional)
    pub data: HashMap<String, String>,
    
    /// Client information
    pub client_info: ClientInfo,
    
    /// Session statistics
    pub stats: SessionStats,
}

/// Client information for session tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientInfo {
    /// Client IP address
    pub ip_address: Option<String>,
    
    /// User agent string
    pub user_agent: Option<String>,
    
    /// Custom headers for affinity
    pub custom_headers: HashMap<String, String>,
}

/// Session statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionStats {
    /// Total requests in this session
    pub request_count: u64,
    
    /// Total bytes transferred
    pub bytes_transferred: u64,
    
    /// Average response time
    pub avg_response_time: Duration,
    
    /// Last request time
    pub last_request_time: SystemTime,
}

impl SessionManager {
    /// Create a new session manager
    pub fn new(config: SessionConfig) -> Self {
        let storage = Arc::new(SessionStorage::new(config.persistence_mode));
        let analytics = Arc::new(SessionAnalytics::new());
        
        info!("🔗 Session manager initialized (affinity: {:?})", config.primary_affinity);
        
        Self {
            storage,
            config,
            metrics: None,
            analytics,
        }
    }
    
    /// Set metrics collector
    pub fn with_metrics(mut self, metrics: Arc<LoadBalancerMetrics>) -> Self {
        self.metrics = Some(metrics);
        self
    }
    
    /// Start session management services
    pub async fn start(&self) -> Result<()> {
        if !self.config.enabled {
            info!("Sticky sessions are disabled");
            return Ok(());
        }
        
        // Start session cleanup task
        self.start_cleanup_task().await;
        
        // Start session migration task if enabled
        if self.config.enable_session_migration {
            self.start_migration_task().await;
        }
        
        // Start analytics collection
        self.start_analytics_task().await;
        
        info!("✅ Session management services started");
        Ok(())
    }
    
    /// Get or create session for a request
    pub async fn get_or_create_session(
        &self,
        request_info: &RequestInfo,
        available_backends: &[SocketAddr],
    ) -> Result<(String, SocketAddr)> {
        // Try to find existing session
        if let Some(session_id) = self.extract_session_id(request_info).await? {
            if let Some(session) = self.storage.get_session(&session_id).await? {
                // Verify backend is still available
                if available_backends.contains(&session.backend_address) {
                    // Update last accessed time
                    self.storage.update_last_accessed(&session_id).await?;
                    
                    debug!("📎 Using existing session: {} -> {}", session_id, session.backend_address);
                    return Ok((session_id, session.backend_address));
                } else {
                    warn!("🔄 Session backend {} unavailable, migrating session {}", 
                          session.backend_address, session_id);
                    
                    // Migrate session to available backend
                    if let Some(new_backend) = self.select_migration_target(available_backends, &session).await {
                        self.migrate_session(&session_id, new_backend).await?;
                        return Ok((session_id, new_backend));
                    } else {
                        // No suitable migration target, create new session
                        warn!("❌ No migration target available, creating new session");
                    }
                }
            }
        }
        
        // Create new session
        let session_id = self.generate_session_id();
        let backend = self.select_backend_for_new_session(available_backends).await?;
        
        let session = Session {
            id: session_id.clone(),
            backend_address: backend,
            created_at: SystemTime::now(),
            last_accessed: SystemTime::now(),
            data: HashMap::new(),
            client_info: ClientInfo {
                ip_address: request_info.client_ip.clone(),
                user_agent: request_info.user_agent.clone(),
                custom_headers: request_info.headers.clone(),
            },
            stats: SessionStats {
                request_count: 0,
                bytes_transferred: 0,
                avg_response_time: Duration::from_millis(0),
                last_request_time: SystemTime::now(),
            },
        };
        
        self.storage.store_session(session).await?;
        
        info!("🆕 Created new session: {} -> {}", session_id, backend);
        
        if let Some(ref metrics) = self.metrics {
            // Record new session metric
            debug!("Recording new session metric");
        }
        
        Ok((session_id, backend))
    }
    
    /// Extract session ID from request
    async fn extract_session_id(&self, request_info: &RequestInfo) -> Result<Option<String>> {
        match self.config.primary_affinity {
            AffinityMethod::Cookie => {
                self.extract_session_from_cookie(request_info).await
            }
            AffinityMethod::ClientIP => {
                self.extract_session_from_ip(request_info).await
            }
            AffinityMethod::Header => {
                self.extract_session_from_header(request_info).await
            }
            AffinityMethod::URLParameter => {
                self.extract_session_from_url(request_info).await
            }
            AffinityMethod::ConsistentHash => {
                self.extract_session_from_hash(request_info).await
            }
        }
    }
    
    /// Extract session ID from cookie
    async fn extract_session_from_cookie(&self, request_info: &RequestInfo) -> Result<Option<String>> {
        if let Some(cookie_header) = request_info.headers.get("cookie") {
            // Parse cookies and find session cookie
            for cookie_pair in cookie_header.split(';') {
                let cookie_pair = cookie_pair.trim();
                if let Some((name, value)) = cookie_pair.split_once('=') {
                    if name.trim() == self.config.cookie_config.name {
                        return Ok(Some(value.trim().to_string()));
                    }
                }
            }
        }
        Ok(None)
    }
    
    /// Extract session ID from client IP
    async fn extract_session_from_ip(&self, request_info: &RequestInfo) -> Result<Option<String>> {
        if let Some(ip) = &request_info.client_ip {
            // Use IP address as session identifier
            let session_id = format!("ip_{}", ip.replace('.', "_").replace(':', "_"));
            Ok(Some(session_id))
        } else {
            Ok(None)
        }
    }
    
    /// Extract session ID from custom header
    async fn extract_session_from_header(&self, request_info: &RequestInfo) -> Result<Option<String>> {
        // Look for session ID in X-Session-ID header
        Ok(request_info.headers.get("x-session-id").cloned())
    }
    
    /// Extract session ID from URL parameter
    async fn extract_session_from_url(&self, request_info: &RequestInfo) -> Result<Option<String>> {
        if let Some(query) = &request_info.query_string {
            // Parse query parameters and find session ID
            for param_pair in query.split('&') {
                if let Some((name, value)) = param_pair.split_once('=') {
                    if name == "session_id" || name == "jsessionid" {
                        return Ok(Some(value.to_string()));
                    }
                }
            }
        }
        Ok(None)
    }
    
    /// Extract session ID from consistent hash
    async fn extract_session_from_hash(&self, request_info: &RequestInfo) -> Result<Option<String>> {
        // Create deterministic session ID based on client characteristics
        let hash_input = format!(
            "{}:{}",
            request_info.client_ip.as_deref().unwrap_or("unknown"),
            request_info.user_agent.as_deref().unwrap_or("unknown")
        );
        
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        hash_input.hash(&mut hasher);
        let hash = hasher.finish();
        
        Ok(Some(format!("hash_{:x}", hash)))
    }
    
    /// Generate new session ID
    fn generate_session_id(&self) -> String {
        Uuid::new_v4().to_string()
    }
    
    /// Select backend for new session
    async fn select_backend_for_new_session(&self, available_backends: &[SocketAddr]) -> Result<SocketAddr> {
        if available_backends.is_empty() {
            return Err(LoadBalancerError::Backend {
                message: "No available backends for new session".to_string(),
                backend_address: "0.0.0.0:0".parse().unwrap(),
                status_code: None,
                response_time: None,
                error_type: crate::error::BackendErrorType::ServiceUnavailable,
            });
        }
        
        // Select backend with least sessions (simple load balancing)
        let session_counts = self.storage.get_session_counts_by_backend().await?;
        
        let mut best_backend = available_backends[0];
        let mut min_sessions = session_counts.get(&best_backend).copied().unwrap_or(0);
        
        for &backend in available_backends {
            let session_count = session_counts.get(&backend).copied().unwrap_or(0);
            if session_count < min_sessions {
                min_sessions = session_count;
                best_backend = backend;
            }
        }
        
        Ok(best_backend)
    }
    
    /// Select migration target for session
    async fn select_migration_target(&self, available_backends: &[SocketAddr], _session: &Session) -> Option<SocketAddr> {
        // Simple selection: choose backend with least sessions
        if let Ok(session_counts) = self.storage.get_session_counts_by_backend().await {
            available_backends.iter()
                .min_by_key(|&&backend| session_counts.get(&backend).copied().unwrap_or(0))
                .copied()
        } else {
            available_backends.first().copied()
        }
    }
    
    /// Migrate session to new backend
    async fn migrate_session(&self, session_id: &str, new_backend: SocketAddr) -> Result<()> {
        if let Some(mut session) = self.storage.get_session(session_id).await? {
            let old_backend = session.backend_address;
            session.backend_address = new_backend;
            
            self.storage.store_session(session).await?;
            
            info!("🔄 Migrated session {} from {} to {}", session_id, old_backend, new_backend);
            
            if let Some(ref metrics) = self.metrics {
                // Record session migration metric
                debug!("Recording session migration metric");
            }
        }
        
        Ok(())
    }
    
    /// Start session cleanup task
    async fn start_cleanup_task(&self) {
        let storage = self.storage.clone();
        let config = self.config.clone();
        let metrics = self.metrics.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.cleanup_interval);
            
            info!("🧹 Starting session cleanup task (interval: {:?})", config.cleanup_interval);
            
            loop {
                interval.tick().await;
                
                match storage.cleanup_expired_sessions(config.session_timeout).await {
                    Ok(cleaned_count) => {
                        if cleaned_count > 0 {
                            info!("🧹 Cleaned up {} expired sessions", cleaned_count);
                            
                            if let Some(ref metrics) = metrics {
                                // Record cleanup metrics
                                debug!("Recording session cleanup metrics");
                            }
                        }
                    }
                    Err(e) => {
                        warn!("Failed to cleanup expired sessions: {}", e);
                    }
                }
            }
        });
    }
    
    /// Start session migration task
    async fn start_migration_task(&self) {
        let storage = self.storage.clone();
        let config = self.config.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));
            
            info!("🔄 Starting session migration task");
            
            loop {
                interval.tick().await;
                
                // Check for load imbalance and migrate sessions if needed
                if let Ok(session_counts) = storage.get_session_counts_by_backend().await {
                    // Simple migration logic: move sessions from overloaded to underloaded backends
                    // In a real implementation, this would be more sophisticated
                    debug!("Session distribution: {:?}", session_counts);
                }
            }
        });
    }
    
    /// Start analytics collection task
    async fn start_analytics_task(&self) {
        let analytics = self.analytics.clone();
        let storage = self.storage.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(300)); // 5 minutes
            
            loop {
                interval.tick().await;
                
                if let Ok(sessions) = storage.get_all_sessions().await {
                    analytics.update_statistics(&sessions).await;
                }
            }
        });
    }
    
    /// Get session statistics
    pub async fn get_session_stats(&self) -> SessionManagerStats {
        let total_sessions = self.storage.get_total_session_count().await.unwrap_or(0);
        let session_counts = self.storage.get_session_counts_by_backend().await.unwrap_or_default();
        
        SessionManagerStats {
            total_sessions,
            sessions_per_backend: session_counts,
            affinity_method: self.config.primary_affinity,
            session_timeout: self.config.session_timeout,
        }
    }
}

/// Request information for session extraction
#[derive(Debug, Clone)]
pub struct RequestInfo {
    pub client_ip: Option<String>,
    pub user_agent: Option<String>,
    pub headers: HashMap<String, String>,
    pub query_string: Option<String>,
    pub uri: String,
}

/// Session manager statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionManagerStats {
    pub total_sessions: usize,
    pub sessions_per_backend: HashMap<SocketAddr, usize>,
    pub affinity_method: AffinityMethod,
    pub session_timeout: Duration,
}

/// Session storage implementation
pub struct SessionStorage {
    /// In-memory session store
    sessions: Arc<RwLock<HashMap<String, Session>>>,

    /// Persistence mode
    persistence_mode: PersistenceMode,
}

impl SessionStorage {
    fn new(persistence_mode: PersistenceMode) -> Self {
        Self {
            sessions: Arc::new(RwLock::new(HashMap::new())),
            persistence_mode,
        }
    }

    async fn store_session(&self, session: Session) -> Result<()> {
        let mut sessions = self.sessions.write().await;
        sessions.insert(session.id.clone(), session);

        // In a real implementation, you'd also persist to disk/database
        // based on persistence_mode

        Ok(())
    }

    async fn get_session(&self, session_id: &str) -> Result<Option<Session>> {
        let sessions = self.sessions.read().await;
        Ok(sessions.get(session_id).cloned())
    }

    async fn update_last_accessed(&self, session_id: &str) -> Result<()> {
        let mut sessions = self.sessions.write().await;
        if let Some(session) = sessions.get_mut(session_id) {
            session.last_accessed = SystemTime::now();
            session.stats.request_count += 1;
            session.stats.last_request_time = SystemTime::now();
        }
        Ok(())
    }

    async fn get_session_counts_by_backend(&self) -> Result<HashMap<SocketAddr, usize>> {
        let sessions = self.sessions.read().await;
        let mut counts = HashMap::new();

        for session in sessions.values() {
            *counts.entry(session.backend_address).or_insert(0) += 1;
        }

        Ok(counts)
    }

    async fn get_total_session_count(&self) -> Result<usize> {
        let sessions = self.sessions.read().await;
        Ok(sessions.len())
    }

    async fn get_all_sessions(&self) -> Result<Vec<Session>> {
        let sessions = self.sessions.read().await;
        Ok(sessions.values().cloned().collect())
    }

    async fn cleanup_expired_sessions(&self, timeout: Duration) -> Result<usize> {
        let mut sessions = self.sessions.write().await;
        let now = SystemTime::now();
        let mut expired_sessions = Vec::new();

        for (session_id, session) in sessions.iter() {
            if let Ok(elapsed) = now.duration_since(session.last_accessed) {
                if elapsed > timeout {
                    expired_sessions.push(session_id.clone());
                }
            }
        }

        let cleaned_count = expired_sessions.len();
        for session_id in expired_sessions {
            sessions.remove(&session_id);
        }

        Ok(cleaned_count)
    }
}

/// Session analytics for monitoring and reporting
pub struct SessionAnalytics {
    /// Analytics data
    data: Arc<RwLock<AnalyticsData>>,
}

#[derive(Debug, Default)]
struct AnalyticsData {
    total_sessions_created: u64,
    total_sessions_expired: u64,
    total_sessions_migrated: u64,
    avg_session_duration: Duration,
    session_distribution: HashMap<SocketAddr, u64>,
}

impl SessionAnalytics {
    fn new() -> Self {
        Self {
            data: Arc::new(RwLock::new(AnalyticsData::default())),
        }
    }

    async fn update_statistics(&self, sessions: &[Session]) {
        let mut data = self.data.write().await;

        // Update session distribution
        data.session_distribution.clear();
        for session in sessions {
            *data.session_distribution.entry(session.backend_address).or_insert(0) += 1;
        }

        // Calculate average session duration
        if !sessions.is_empty() {
            let total_duration: Duration = sessions.iter()
                .filter_map(|s| SystemTime::now().duration_since(s.created_at).ok())
                .sum();
            data.avg_session_duration = total_duration / sessions.len() as u32;
        }
    }

    async fn get_analytics(&self) -> AnalyticsData {
        self.data.read().await.clone()
    }
}

impl Default for SessionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            primary_affinity: AffinityMethod::Cookie,
            fallback_affinity: vec![AffinityMethod::ClientIP],
            session_timeout: Duration::from_secs(3600), // 1 hour
            cookie_config: CookieConfig::default(),
            cleanup_interval: Duration::from_secs(300), // 5 minutes
            max_sessions_per_backend: 10000,
            enable_session_migration: true,
            migration_threshold: 0.2, // 20% load difference
            enable_session_backup: false,
            persistence_mode: PersistenceMode::Memory,
        }
    }
}

impl Default for CookieConfig {
    fn default() -> Self {
        Self {
            name: "RUSTY_LB_SESSION".to_string(),
            domain: None,
            path: "/".to_string(),
            secure: true,
            http_only: true,
            same_site: SameSite::Lax,
            max_age: None, // Session cookie
        }
    }
}
```

## Integration Example

Create `examples/sticky_sessions_example.rs`:

```rust
//! Example demonstrating sticky sessions with different affinity methods

use rusty_balancer::session::{SessionManager, SessionConfig, AffinityMethod, RequestInfo};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::time::Duration;
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("🔗 Starting Sticky Sessions Example");

    // Test different affinity methods
    let affinity_methods = [
        AffinityMethod::Cookie,
        AffinityMethod::ClientIP,
        AffinityMethod::Header,
        AffinityMethod::URLParameter,
        AffinityMethod::ConsistentHash,
    ];

    let available_backends = vec![
        "127.0.0.1:8081".parse::<SocketAddr>()?,
        "127.0.0.1:8082".parse::<SocketAddr>()?,
        "127.0.0.1:8083".parse::<SocketAddr>()?,
    ];

    for affinity_method in &affinity_methods {
        info!("🧪 Testing affinity method: {:?}", affinity_method);

        let config = SessionConfig {
            enabled: true,
            primary_affinity: *affinity_method,
            session_timeout: Duration::from_secs(300),
            cleanup_interval: Duration::from_secs(60),
            enable_session_migration: true,
            ..Default::default()
        };

        let session_manager = SessionManager::new(config);
        session_manager.start().await?;

        // Simulate different clients
        let clients = [
            ("*************", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"),
            ("*************", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)"),
            ("*************", "Mozilla/5.0 (X11; Linux x86_64)"),
        ];

        for (client_ip, user_agent) in &clients {
            // Create request info based on affinity method
            let mut headers = HashMap::new();
            let mut query_string = None;

            match affinity_method {
                AffinityMethod::Cookie => {
                    // Simulate existing session cookie for some requests
                    if client_ip.ends_with("100") {
                        headers.insert("cookie".to_string(), "RUSTY_LB_SESSION=existing-session-123".to_string());
                    }
                }
                AffinityMethod::Header => {
                    headers.insert("x-session-id".to_string(), format!("session-{}", client_ip.replace('.', "-")));
                }
                AffinityMethod::URLParameter => {
                    query_string = Some(format!("session_id=url-session-{}", client_ip.replace('.', "-")));
                }
                _ => {} // IP and ConsistentHash don't need special headers
            }

            let request_info = RequestInfo {
                client_ip: Some(client_ip.to_string()),
                user_agent: Some(user_agent.to_string()),
                headers,
                query_string,
                uri: "/api/test".to_string(),
            };

            // Get or create session
            match session_manager.get_or_create_session(&request_info, &available_backends).await {
                Ok((session_id, backend)) => {
                    info!("Client {} -> Session {} -> Backend {}", client_ip, session_id, backend);

                    // Simulate multiple requests from same client
                    for i in 1..=3 {
                        match session_manager.get_or_create_session(&request_info, &available_backends).await {
                            Ok((repeat_session_id, repeat_backend)) => {
                                if session_id == repeat_session_id && backend == repeat_backend {
                                    info!("  Request {} -> ✅ Sticky to same session and backend", i);
                                } else {
                                    warn!("  Request {} -> ❌ Session or backend changed!", i);
                                }
                            }
                            Err(e) => error!("Failed to get session for repeat request: {}", e),
                        }
                    }
                }
                Err(e) => error!("Failed to get or create session: {}", e),
            }
        }

        // Show session statistics
        let stats = session_manager.get_session_stats().await;
        info!("📊 Session Statistics:");
        info!("  Total sessions: {}", stats.total_sessions);
        info!("  Sessions per backend: {:?}", stats.sessions_per_backend);
        info!("  Affinity method: {:?}", stats.affinity_method);
        info!("  Session timeout: {:?}", stats.session_timeout);

        info!("✅ Affinity method {:?} test completed\n", affinity_method);
    }

    // Test session migration
    info!("🔄 Testing session migration");

    let config = SessionConfig {
        enabled: true,
        primary_affinity: AffinityMethod::Cookie,
        enable_session_migration: true,
        ..Default::default()
    };

    let session_manager = SessionManager::new(config);
    session_manager.start().await?;

    // Create a session
    let request_info = RequestInfo {
        client_ip: Some("*************".to_string()),
        user_agent: Some("Test Client".to_string()),
        headers: HashMap::new(),
        query_string: None,
        uri: "/test".to_string(),
    };

    let (session_id, original_backend) = session_manager
        .get_or_create_session(&request_info, &available_backends)
        .await?;

    info!("Created session {} on backend {}", session_id, original_backend);

    // Simulate backend failure by removing it from available backends
    let reduced_backends: Vec<SocketAddr> = available_backends
        .into_iter()
        .filter(|&addr| addr != original_backend)
        .collect();

    info!("Simulating backend {} failure", original_backend);

    // Try to access session again with reduced backend list
    match session_manager.get_or_create_session(&request_info, &reduced_backends).await {
        Ok((migrated_session_id, new_backend)) => {
            if migrated_session_id == session_id {
                info!("✅ Session {} successfully migrated to backend {}", session_id, new_backend);
            } else {
                warn!("❌ New session created instead of migration");
            }
        }
        Err(e) => error!("Failed to migrate session: {}", e),
    }

    info!("✅ Session migration test completed");

    Ok(())
}
```

## Key Design Decisions Explained

### 1. Multiple Affinity Methods
**Decision**: Support multiple session affinity mechanisms

**Rationale**:
- **Flexibility**: Different applications and clients require different approaches
- **Compatibility**: Support various network configurations and client capabilities
- **Reliability**: Fallback mechanisms when primary method fails
- **Security**: Choose appropriate method based on security requirements

### 2. Session Migration and Failover
**Decision**: Automatic session migration when backends become unavailable

**Rationale**:
- **High Availability**: Maintain user sessions during server failures
- **Load Balancing**: Redistribute sessions during maintenance
- **User Experience**: Minimize session loss and re-authentication
- **Operational Flexibility**: Support dynamic server pool changes

### 3. Configurable Session Persistence
**Decision**: Support memory, persistent, and distributed storage modes

**Rationale**:
- **Scalability**: Choose storage mode based on deployment requirements
- **Performance**: Memory storage for high-performance scenarios
- **Reliability**: Persistent storage for session durability
- **Distribution**: Shared storage for multi-instance deployments

## Performance Considerations

1. **Session Lookup**: O(1) session retrieval with hash-based storage
2. **Memory Usage**: Efficient session data structures and cleanup
3. **Migration Overhead**: Minimal impact during session migration
4. **Cookie Processing**: Fast cookie parsing and generation

## Security Considerations

1. **Session ID Security**: Cryptographically secure session ID generation
2. **Cookie Security**: Secure, HttpOnly, and SameSite cookie attributes
3. **Session Hijacking**: Protection against session fixation and hijacking
4. **Data Privacy**: Minimal storage of sensitive client information

## Comprehensive Testing

Create `tests/sticky_sessions_tests.rs`:

```rust
use rusty_balancer::session::{
    StickySessionManager, SessionConfig, SessionAffinityMethod, SessionPersistenceMode,
    SessionRequest, SessionBackend
};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::time::Duration;
use tokio::time::Instant;

#[tokio::test]
async fn test_session_manager_creation() {
    let config = SessionConfig::default();
    let session_manager = StickySessionManager::new(config).await.unwrap();

    let stats = session_manager.get_session_stats().await;
    assert_eq!(stats.total_sessions, 0);
    assert_eq!(stats.active_sessions, 0);
}

#[tokio::test]
async fn test_cookie_based_affinity() {
    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::Cookie {
            name: "SESSIONID".to_string(),
            domain: Some(".example.com".to_string()),
            path: Some("/".to_string()),
            secure: false,
            http_only: true,
            same_site: None,
        },
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(1800),
        ..Default::default()
    };

    let session_manager = StickySessionManager::new(config).await.unwrap();
    session_manager.start().await.unwrap();

    // Add backends
    let backends = vec![
        "127.0.0.1:8081".parse::<SocketAddr>().unwrap(),
        "127.0.0.1:8082".parse::<SocketAddr>().unwrap(),
        "127.0.0.1:8083".parse::<SocketAddr>().unwrap(),
    ];

    for backend in &backends {
        session_manager.add_backend(*backend).await.unwrap();
    }

    // Test session creation and affinity
    let mut headers = HashMap::new();
    headers.insert("Host".to_string(), "example.com".to_string());

    let request = SessionRequest {
        client_ip: "*************".parse().unwrap(),
        headers: headers.clone(),
        cookies: HashMap::new(),
        url_params: HashMap::new(),
        user_agent: Some("Test Browser".to_string()),
        timestamp: Instant::now(),
    };

    // First request should create a new session
    let result = session_manager.get_backend_for_session(&request).await.unwrap();
    assert!(result.session_created);
    assert!(backends.contains(&result.backend_address));

    let session_id = result.session_id.clone();
    let first_backend = result.backend_address;

    // Second request with same session should go to same backend
    let mut cookies = HashMap::new();
    cookies.insert("SESSIONID".to_string(), session_id.clone());

    let request2 = SessionRequest {
        client_ip: "*************".parse().unwrap(),
        headers,
        cookies,
        url_params: HashMap::new(),
        user_agent: Some("Test Browser".to_string()),
        timestamp: Instant::now(),
    };

    let result2 = session_manager.get_backend_for_session(&request2).await.unwrap();
    assert!(!result2.session_created);
    assert_eq!(result2.backend_address, first_backend);
    assert_eq!(result2.session_id, session_id);
}

#[tokio::test]
async fn test_ip_based_affinity() {
    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::ClientIP,
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(1800),
        ..Default::default()
    };

    let session_manager = StickySessionManager::new(config).await.unwrap();
    session_manager.start().await.unwrap();

    // Add backends
    let backends = vec![
        "127.0.0.1:8081".parse::<SocketAddr>().unwrap(),
        "127.0.0.1:8082".parse::<SocketAddr>().unwrap(),
    ];

    for backend in &backends {
        session_manager.add_backend(*backend).await.unwrap();
    }

    let request1 = SessionRequest {
        client_ip: "*************".parse().unwrap(),
        headers: HashMap::new(),
        cookies: HashMap::new(),
        url_params: HashMap::new(),
        user_agent: None,
        timestamp: Instant::now(),
    };

    let request2 = SessionRequest {
        client_ip: "*************".parse().unwrap(),
        headers: HashMap::new(),
        cookies: HashMap::new(),
        url_params: HashMap::new(),
        user_agent: None,
        timestamp: Instant::now(),
    };

    // Same IP should get same backend
    let result1a = session_manager.get_backend_for_session(&request1).await.unwrap();
    let result1b = session_manager.get_backend_for_session(&request1).await.unwrap();
    assert_eq!(result1a.backend_address, result1b.backend_address);

    // Different IP should potentially get different backend
    let result2 = session_manager.get_backend_for_session(&request2).await.unwrap();
    assert!(backends.contains(&result2.backend_address));
}

#[tokio::test]
async fn test_header_based_affinity() {
    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::Header {
            name: "X-User-ID".to_string(),
        },
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(1800),
        ..Default::default()
    };

    let session_manager = StickySessionManager::new(config).await.unwrap();
    session_manager.start().await.unwrap();

    // Add backends
    let backends = vec![
        "127.0.0.1:8081".parse::<SocketAddr>().unwrap(),
        "127.0.0.1:8082".parse::<SocketAddr>().unwrap(),
    ];

    for backend in &backends {
        session_manager.add_backend(*backend).await.unwrap();
    }

    let mut headers1 = HashMap::new();
    headers1.insert("X-User-ID".to_string(), "user123".to_string());

    let mut headers2 = HashMap::new();
    headers2.insert("X-User-ID".to_string(), "user456".to_string());

    let request1 = SessionRequest {
        client_ip: "*************".parse().unwrap(),
        headers: headers1,
        cookies: HashMap::new(),
        url_params: HashMap::new(),
        user_agent: None,
        timestamp: Instant::now(),
    };

    let request2 = SessionRequest {
        client_ip: "*************".parse().unwrap(), // Same IP, different user
        headers: headers2,
        cookies: HashMap::new(),
        url_params: HashMap::new(),
        user_agent: None,
        timestamp: Instant::now(),
    };

    // Same user should get same backend
    let result1a = session_manager.get_backend_for_session(&request1).await.unwrap();
    let result1b = session_manager.get_backend_for_session(&request1).await.unwrap();
    assert_eq!(result1a.backend_address, result1b.backend_address);

    // Different user should potentially get different backend
    let result2 = session_manager.get_backend_for_session(&request2).await.unwrap();
    assert!(backends.contains(&result2.backend_address));
}

#[tokio::test]
async fn test_session_timeout() {
    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::Cookie {
            name: "SESSIONID".to_string(),
            domain: None,
            path: None,
            secure: false,
            http_only: true,
            same_site: None,
        },
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_millis(100), // Very short timeout for testing
        cleanup_interval: Duration::from_millis(50),
        ..Default::default()
    };

    let session_manager = StickySessionManager::new(config).await.unwrap();
    session_manager.start().await.unwrap();

    // Add backend
    let backend = "127.0.0.1:8081".parse::<SocketAddr>().unwrap();
    session_manager.add_backend(backend).await.unwrap();

    let request = SessionRequest {
        client_ip: "*************".parse().unwrap(),
        headers: HashMap::new(),
        cookies: HashMap::new(),
        url_params: HashMap::new(),
        user_agent: None,
        timestamp: Instant::now(),
    };

    // Create session
    let result = session_manager.get_backend_for_session(&request).await.unwrap();
    assert!(result.session_created);

    let session_id = result.session_id.clone();

    // Wait for session to expire
    tokio::time::sleep(Duration::from_millis(200)).await;

    // Session should be expired and cleaned up
    let mut cookies = HashMap::new();
    cookies.insert("SESSIONID".to_string(), session_id);

    let expired_request = SessionRequest {
        client_ip: "*************".parse().unwrap(),
        headers: HashMap::new(),
        cookies,
        url_params: HashMap::new(),
        user_agent: None,
        timestamp: Instant::now(),
    };

    let result2 = session_manager.get_backend_for_session(&expired_request).await.unwrap();
    assert!(result2.session_created); // Should create new session
    assert_ne!(result2.session_id, session_id); // Should be different session ID
}

#[tokio::test]
async fn test_session_failover() {
    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::Cookie {
            name: "SESSIONID".to_string(),
            domain: None,
            path: None,
            secure: false,
            http_only: true,
            same_site: None,
        },
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(1800),
        enable_failover: true,
        ..Default::default()
    };

    let session_manager = StickySessionManager::new(config).await.unwrap();
    session_manager.start().await.unwrap();

    // Add backends
    let backend1 = "127.0.0.1:8081".parse::<SocketAddr>().unwrap();
    let backend2 = "127.0.0.1:8082".parse::<SocketAddr>().unwrap();

    session_manager.add_backend(backend1).await.unwrap();
    session_manager.add_backend(backend2).await.unwrap();

    let request = SessionRequest {
        client_ip: "*************".parse().unwrap(),
        headers: HashMap::new(),
        cookies: HashMap::new(),
        url_params: HashMap::new(),
        user_agent: None,
        timestamp: Instant::now(),
    };

    // Create session
    let result = session_manager.get_backend_for_session(&request).await.unwrap();
    let original_backend = result.backend_address;
    let session_id = result.session_id.clone();

    // Remove the original backend (simulate failure)
    session_manager.remove_backend(original_backend).await.unwrap();

    // Request with same session should failover to different backend
    let mut cookies = HashMap::new();
    cookies.insert("SESSIONID".to_string(), session_id.clone());

    let failover_request = SessionRequest {
        client_ip: "*************".parse().unwrap(),
        headers: HashMap::new(),
        cookies,
        url_params: HashMap::new(),
        user_agent: None,
        timestamp: Instant::now(),
    };

    let failover_result = session_manager.get_backend_for_session(&failover_request).await.unwrap();
    assert_ne!(failover_result.backend_address, original_backend);
    assert_eq!(failover_result.session_id, session_id); // Same session ID
    assert!(failover_result.session_migrated); // Should indicate migration
}

#[tokio::test]
async fn test_session_statistics() {
    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::ClientIP,
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(1800),
        enable_analytics: true,
        ..Default::default()
    };

    let session_manager = StickySessionManager::new(config).await.unwrap();
    session_manager.start().await.unwrap();

    // Add backend
    let backend = "127.0.0.1:8081".parse::<SocketAddr>().unwrap();
    session_manager.add_backend(backend).await.unwrap();

    // Create multiple sessions
    for i in 0..5 {
        let request = SessionRequest {
            client_ip: format!("192.168.1.{}", 100 + i).parse().unwrap(),
            headers: HashMap::new(),
            cookies: HashMap::new(),
            url_params: HashMap::new(),
            user_agent: None,
            timestamp: Instant::now(),
        };

        session_manager.get_backend_for_session(&request).await.unwrap();
    }

    let stats = session_manager.get_session_stats().await;
    assert_eq!(stats.total_sessions, 5);
    assert_eq!(stats.active_sessions, 5);
    assert!(stats.session_hit_rate > 0.0);
}

#[tokio::test]
async fn test_concurrent_session_creation() {
    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::Cookie {
            name: "SESSIONID".to_string(),
            domain: None,
            path: None,
            secure: false,
            http_only: true,
            same_site: None,
        },
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(1800),
        ..Default::default()
    };

    let session_manager = StickySessionManager::new(config).await.unwrap();
    session_manager.start().await.unwrap();

    // Add backend
    let backend = "127.0.0.1:8081".parse::<SocketAddr>().unwrap();
    session_manager.add_backend(backend).await.unwrap();

    // Create multiple concurrent sessions
    let mut handles = Vec::new();

    for i in 0..10 {
        let session_manager_clone = session_manager.clone();
        let handle = tokio::spawn(async move {
            let request = SessionRequest {
                client_ip: format!("192.168.1.{}", 100 + i).parse().unwrap(),
                headers: HashMap::new(),
                cookies: HashMap::new(),
                url_params: HashMap::new(),
                user_agent: None,
                timestamp: Instant::now(),
            };

            session_manager_clone.get_backend_for_session(&request).await
        });

        handles.push(handle);
    }

    // Wait for all sessions to be created
    let mut results = Vec::new();
    for handle in handles {
        let result = handle.await.unwrap().unwrap();
        results.push(result);
    }

    // All sessions should be created successfully
    assert_eq!(results.len(), 10);
    for result in &results {
        assert!(result.session_created);
        assert_eq!(result.backend_address, backend);
    }

    // All session IDs should be unique
    let mut session_ids: Vec<String> = results.iter().map(|r| r.session_id.clone()).collect();
    session_ids.sort();
    session_ids.dedup();
    assert_eq!(session_ids.len(), 10);
}
```

## Performance Benchmarks

Create `benches/sticky_sessions_bench.rs`:

```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use rusty_balancer::session::{
    StickySessionManager, SessionConfig, SessionAffinityMethod, SessionPersistenceMode,
    SessionRequest
};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::time::Duration;
use tokio::time::Instant;
use tokio::runtime::Runtime;

fn bench_session_creation(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::Cookie {
            name: "SESSIONID".to_string(),
            domain: None,
            path: None,
            secure: false,
            http_only: true,
            same_site: None,
        },
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(1800),
        ..Default::default()
    };

    let session_manager = rt.block_on(async {
        let sm = StickySessionManager::new(config).await.unwrap();
        sm.start().await.unwrap();

        // Add backend
        let backend = "127.0.0.1:8081".parse::<SocketAddr>().unwrap();
        sm.add_backend(backend).await.unwrap();

        sm
    });

    c.bench_function("session_creation", |b| {
        b.to_async(&rt).iter(|| async {
            let request = SessionRequest {
                client_ip: "*************".parse().unwrap(),
                headers: HashMap::new(),
                cookies: HashMap::new(),
                url_params: HashMap::new(),
                user_agent: None,
                timestamp: Instant::now(),
            };

            black_box(session_manager.get_backend_for_session(&request).await.unwrap())
        })
    });
}

fn bench_session_lookup(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::Cookie {
            name: "SESSIONID".to_string(),
            domain: None,
            path: None,
            secure: false,
            http_only: true,
            same_site: None,
        },
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(1800),
        ..Default::default()
    };

    let (session_manager, session_id) = rt.block_on(async {
        let sm = StickySessionManager::new(config).await.unwrap();
        sm.start().await.unwrap();

        // Add backend
        let backend = "127.0.0.1:8081".parse::<SocketAddr>().unwrap();
        sm.add_backend(backend).await.unwrap();

        // Create a session
        let request = SessionRequest {
            client_ip: "*************".parse().unwrap(),
            headers: HashMap::new(),
            cookies: HashMap::new(),
            url_params: HashMap::new(),
            user_agent: None,
            timestamp: Instant::now(),
        };

        let result = sm.get_backend_for_session(&request).await.unwrap();
        (sm, result.session_id)
    });

    c.bench_function("session_lookup", |b| {
        b.to_async(&rt).iter(|| async {
            let mut cookies = HashMap::new();
            cookies.insert("SESSIONID".to_string(), session_id.clone());

            let request = SessionRequest {
                client_ip: "*************".parse().unwrap(),
                headers: HashMap::new(),
                cookies,
                url_params: HashMap::new(),
                user_agent: None,
                timestamp: Instant::now(),
            };

            black_box(session_manager.get_backend_for_session(&request).await.unwrap())
        })
    });
}

fn bench_different_affinity_methods(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let methods = vec![
        ("cookie", SessionAffinityMethod::Cookie {
            name: "SESSIONID".to_string(),
            domain: None,
            path: None,
            secure: false,
            http_only: true,
            same_site: None,
        }),
        ("client_ip", SessionAffinityMethod::ClientIP),
        ("header", SessionAffinityMethod::Header {
            name: "X-User-ID".to_string(),
        }),
        ("url_param", SessionAffinityMethod::URLParameter {
            name: "session".to_string(),
        }),
    ];

    let mut group = c.benchmark_group("affinity_methods");

    for (method_name, affinity_method) in methods {
        let config = SessionConfig {
            enabled: true,
            affinity_method: affinity_method.clone(),
            persistence_mode: SessionPersistenceMode::InMemory,
            session_timeout: Duration::from_secs(1800),
            ..Default::default()
        };

        let session_manager = rt.block_on(async {
            let sm = StickySessionManager::new(config).await.unwrap();
            sm.start().await.unwrap();

            // Add backend
            let backend = "127.0.0.1:8081".parse::<SocketAddr>().unwrap();
            sm.add_backend(backend).await.unwrap();

            sm
        });

        group.bench_with_input(
            BenchmarkId::new("session_creation", method_name),
            &method_name,
            |b, _| {
                b.to_async(&rt).iter(|| async {
                    let mut headers = HashMap::new();
                    headers.insert("X-User-ID".to_string(), "user123".to_string());

                    let mut url_params = HashMap::new();
                    url_params.insert("session".to_string(), "session123".to_string());

                    let request = SessionRequest {
                        client_ip: "*************".parse().unwrap(),
                        headers,
                        cookies: HashMap::new(),
                        url_params,
                        user_agent: None,
                        timestamp: Instant::now(),
                    };

                    black_box(session_manager.get_backend_for_session(&request).await.unwrap())
                })
            },
        );
    }

    group.finish();
}

fn bench_concurrent_sessions(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::ClientIP,
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(1800),
        ..Default::default()
    };

    let session_manager = rt.block_on(async {
        let sm = StickySessionManager::new(config).await.unwrap();
        sm.start().await.unwrap();

        // Add multiple backends
        for i in 1..=4 {
            let backend = format!("127.0.0.1:808{}", i).parse::<SocketAddr>().unwrap();
            sm.add_backend(backend).await.unwrap();
        }

        sm
    });

    let concurrency_levels = vec![1, 10, 50, 100];
    let mut group = c.benchmark_group("concurrent_sessions");

    for &concurrency in &concurrency_levels {
        group.bench_with_input(
            BenchmarkId::new("concurrent_requests", concurrency),
            &concurrency,
            |b, &concurrency| {
                b.to_async(&rt).iter(|| async {
                    let mut handles = Vec::new();

                    for i in 0..concurrency {
                        let session_manager_clone = session_manager.clone();
                        let handle = tokio::spawn(async move {
                            let request = SessionRequest {
                                client_ip: format!("192.168.1.{}", 100 + (i % 255)).parse().unwrap(),
                                headers: HashMap::new(),
                                cookies: HashMap::new(),
                                url_params: HashMap::new(),
                                user_agent: None,
                                timestamp: Instant::now(),
                            };

                            session_manager_clone.get_backend_for_session(&request).await.unwrap()
                        });

                        handles.push(handle);
                    }

                    // Wait for all requests to complete
                    for handle in handles {
                        black_box(handle.await.unwrap());
                    }
                })
            },
        );
    }

    group.finish();
}

fn bench_session_cleanup(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::ClientIP,
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_millis(1), // Very short timeout
        cleanup_interval: Duration::from_millis(1),
        ..Default::default()
    };

    c.bench_function("session_cleanup", |b| {
        b.to_async(&rt).iter(|| async {
            let session_manager = StickySessionManager::new(config.clone()).await.unwrap();
            session_manager.start().await.unwrap();

            // Add backend
            let backend = "127.0.0.1:8081".parse::<SocketAddr>().unwrap();
            session_manager.add_backend(backend).await.unwrap();

            // Create many sessions
            for i in 0..1000 {
                let request = SessionRequest {
                    client_ip: format!("192.168.{}.{}", (i / 255) + 1, (i % 255) + 1).parse().unwrap(),
                    headers: HashMap::new(),
                    cookies: HashMap::new(),
                    url_params: HashMap::new(),
                    user_agent: None,
                    timestamp: Instant::now(),
                };

                session_manager.get_backend_for_session(&request).await.unwrap();
            }

            // Wait for cleanup to occur
            tokio::time::sleep(Duration::from_millis(10)).await;

            black_box(session_manager.get_session_stats().await)
        })
    });
}

criterion_group!(
    benches,
    bench_session_creation,
    bench_session_lookup,
    bench_different_affinity_methods,
    bench_concurrent_sessions,
    bench_session_cleanup
);
criterion_main!(benches);
```

## Load Testing

Create `tests/load_tests.rs`:

```rust
use rusty_balancer::session::{
    StickySessionManager, SessionConfig, SessionAffinityMethod, SessionPersistenceMode,
    SessionRequest
};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;

#[tokio::test]
async fn test_high_load_session_creation() {
    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::ClientIP,
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(3600),
        ..Default::default()
    };

    let session_manager = StickySessionManager::new(config).await.unwrap();
    session_manager.start().await.unwrap();

    // Add multiple backends
    for i in 1..=8 {
        let backend = format!("127.0.0.1:808{}", i).parse::<SocketAddr>().unwrap();
        session_manager.add_backend(backend).await.unwrap();
    }

    let start_time = Instant::now();
    let num_requests = 10000;
    let concurrency = 100;

    // Use semaphore to limit concurrency
    let semaphore = Arc::new(Semaphore::new(concurrency));
    let mut handles = Vec::new();

    for i in 0..num_requests {
        let session_manager_clone = session_manager.clone();
        let semaphore_clone = semaphore.clone();

        let handle = tokio::spawn(async move {
            let _permit = semaphore_clone.acquire().await.unwrap();

            let request = SessionRequest {
                client_ip: format!("10.{}.{}.{}",
                    (i / 65536) % 256,
                    (i / 256) % 256,
                    i % 256
                ).parse().unwrap(),
                headers: HashMap::new(),
                cookies: HashMap::new(),
                url_params: HashMap::new(),
                user_agent: Some(format!("LoadTest-{}", i)),
                timestamp: Instant::now(),
            };

            session_manager_clone.get_backend_for_session(&request).await
        });

        handles.push(handle);
    }

    // Wait for all requests to complete
    let mut successful_requests = 0;
    let mut failed_requests = 0;

    for handle in handles {
        match handle.await.unwrap() {
            Ok(_) => successful_requests += 1,
            Err(_) => failed_requests += 1,
        }
    }

    let elapsed = start_time.elapsed();
    let requests_per_second = num_requests as f64 / elapsed.as_secs_f64();

    println!("Load test results:");
    println!("  Total requests: {}", num_requests);
    println!("  Successful: {}", successful_requests);
    println!("  Failed: {}", failed_requests);
    println!("  Duration: {:?}", elapsed);
    println!("  Requests/sec: {:.2}", requests_per_second);

    // Verify results
    assert!(successful_requests > num_requests * 95 / 100); // At least 95% success rate
    assert!(requests_per_second > 1000.0); // At least 1000 RPS

    // Check session statistics
    let stats = session_manager.get_session_stats().await;
    println!("  Active sessions: {}", stats.active_sessions);
    println!("  Total sessions: {}", stats.total_sessions);
    println!("  Hit rate: {:.2}%", stats.session_hit_rate * 100.0);

    assert!(stats.total_sessions > 0);
    assert!(stats.active_sessions <= stats.total_sessions);
}

#[tokio::test]
async fn test_memory_usage_under_load() {
    let config = SessionConfig {
        enabled: true,
        affinity_method: SessionAffinityMethod::Cookie {
            name: "SESSIONID".to_string(),
            domain: None,
            path: None,
            secure: false,
            http_only: true,
            same_site: None,
        },
        persistence_mode: SessionPersistenceMode::InMemory,
        session_timeout: Duration::from_secs(60), // Short timeout for memory test
        cleanup_interval: Duration::from_secs(5),
        ..Default::default()
    };

    let session_manager = StickySessionManager::new(config).await.unwrap();
    session_manager.start().await.unwrap();

    // Add backend
    let backend = "127.0.0.1:8081".parse::<SocketAddr>().unwrap();
    session_manager.add_backend(backend).await.unwrap();

    // Create many sessions in waves
    for wave in 0..5 {
        println!("Creating session wave {}", wave + 1);

        let mut handles = Vec::new();
        for i in 0..2000 {
            let session_manager_clone = session_manager.clone();
            let handle = tokio::spawn(async move {
                let request = SessionRequest {
                    client_ip: format!("172.16.{}.{}",
                        (wave * 2000 + i) / 256,
                        (wave * 2000 + i) % 256
                    ).parse().unwrap(),
                    headers: HashMap::new(),
                    cookies: HashMap::new(),
                    url_params: HashMap::new(),
                    user_agent: None,
                    timestamp: Instant::now(),
                };

                session_manager_clone.get_backend_for_session(&request).await
            });

            handles.push(handle);
        }

        // Wait for wave to complete
        for handle in handles {
            handle.await.unwrap().unwrap();
        }

        let stats = session_manager.get_session_stats().await;
        println!("  Wave {} complete - Active sessions: {}", wave + 1, stats.active_sessions);

        // Wait for some cleanup to occur
        tokio::time::sleep(Duration::from_secs(10)).await;
    }

    // Final statistics
    let final_stats = session_manager.get_session_stats().await;
    println!("Final session count: {}", final_stats.active_sessions);

    // Memory should be managed properly (sessions should be cleaned up)
    assert!(final_stats.active_sessions < 10000); // Should be much less due to cleanup
}
```

## Navigation
- [Previous: Weighted Load Balancing](13-weighted-load-balancing.md)
- [Next: Rate Limiting](15-rate-limiting.md)
