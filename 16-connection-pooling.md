# Module 16: Connection Pooling

## Learning Objectives
- Implement efficient connection pooling for backend servers to optimize performance
- Design connection lifecycle management with health checking and timeout handling
- Build connection pool monitoring and auto-scaling capabilities
- Create connection reuse strategies and connection warming techniques
- Understand connection pooling patterns and performance optimization strategies

## Why Connection Pooling is Critical

Connection pooling dramatically improves load balancer performance and resource utilization:

1. **Performance**: Eliminate connection establishment overhead for each request
2. **Resource Efficiency**: Reuse existing connections instead of creating new ones
3. **Scalability**: Handle high request volumes with fewer system resources
4. **Latency Reduction**: Avoid TCP handshake and TLS negotiation delays
5. **Backend Protection**: Limit concurrent connections to prevent backend overload

### Connection Pooling Architecture

```mermaid
graph TD
    Client[Client Requests] --> LB[Load Balancer]
    LB --> CPM[Connection Pool Manager]
    
    CPM --> Pool1[Backend 1 Pool]
    CPM --> Pool2[Backend 2 Pool]
    CPM --> Pool3[Backend 3 Pool]
    
    subgraph "Connection Pool Structure"
        Pool1 --> Active1[Active Connections]
        Pool1 --> Idle1[Idle Connections]
        Pool1 --> Warming1[Warming Connections]
        
        Pool2 --> Active2[Active Connections]
        Pool2 --> Idle2[Idle Connections]
        Pool2 --> Warming2[Warming Connections]
        
        Pool3 --> Active3[Active Connections]
        Pool3 --> Idle3[Idle Connections]
        Pool3 --> Warming3[Warming Connections]
    end
    
    subgraph "Connection Lifecycle"
        Create[Create Connection]
        Validate[Validate Connection]
        Use[Use Connection]
        Return[Return to Pool]
        Cleanup[Cleanup Expired]
    end
    
    Create --> Validate
    Validate --> Use
    Use --> Return
    Return --> Idle1
    Cleanup --> Create
    
    subgraph "Pool Management"
        Monitor[Health Monitor]
        Scaler[Auto Scaler]
        Metrics[Pool Metrics]
        Alerts[Pool Alerts]
    end
    
    CPM --> Monitor
    Monitor --> Scaler
    Monitor --> Metrics
    Metrics --> Alerts
    
    subgraph "Backend Servers"
        B1[Backend Server 1<br/>Max Connections: 100]
        B2[Backend Server 2<br/>Max Connections: 150]
        B3[Backend Server 3<br/>Max Connections: 200]
    end
    
    Active1 --> B1
    Active2 --> B2
    Active3 --> B3
```

## Connection Pooling Implementation

### Design Decisions

**Why per-backend connection pools?**
- **Isolation**: Failures in one backend don't affect others
- **Optimization**: Tune pool settings per backend characteristics
- **Monitoring**: Track performance metrics per backend
- **Scaling**: Scale pools independently based on backend capacity

**Why connection lifecycle management?**
- **Reliability**: Detect and replace broken connections automatically
- **Performance**: Maintain optimal pool size for current load
- **Resource Management**: Clean up idle connections to free resources
- **Health**: Validate connections before use to prevent errors

Create `src/pool/mod.rs`:

```rust
//! Advanced connection pooling for backend servers
//!
//! This module provides sophisticated connection pooling with:
//! - Per-backend connection pools with independent configuration
//! - Connection lifecycle management and health validation
//! - Auto-scaling based on load and performance metrics
//! - Connection warming and preemptive creation
//! - Comprehensive monitoring and alerting

pub mod connection;
pub mod manager;
pub mod health;
pub mod metrics;

use crate::error::{LoadBalancerError, Result};
use crate::metrics::LoadBalancerMetrics;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, Semaphore, Mutex};
use tokio::net::TcpStream;
use tracing::{info, warn, debug, trace};
use uuid::Uuid;

/// Connection pool manager for all backend servers
pub struct ConnectionPoolManager {
    /// Per-backend connection pools
    pools: Arc<RwLock<HashMap<SocketAddr, Arc<ConnectionPool>>>>,
    
    /// Global pool configuration
    config: ConnectionPoolConfig,
    
    /// Metrics collector
    metrics: Option<Arc<LoadBalancerMetrics>>,
    
    /// Pool health monitor
    health_monitor: Arc<PoolHealthMonitor>,
}

/// Configuration for connection pooling
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionPoolConfig {
    /// Minimum connections per pool
    pub min_connections: usize,
    
    /// Maximum connections per pool
    pub max_connections: usize,
    
    /// Connection timeout
    pub connection_timeout: Duration,
    
    /// Idle timeout (how long to keep idle connections)
    pub idle_timeout: Duration,
    
    /// Maximum connection lifetime
    pub max_lifetime: Duration,
    
    /// Connection validation interval
    pub validation_interval: Duration,
    
    /// Enable connection warming
    pub enable_warming: bool,
    
    /// Warming threshold (create new connections when pool drops below this)
    pub warming_threshold: f64,
    
    /// Enable auto-scaling
    pub enable_auto_scaling: bool,
    
    /// Auto-scaling check interval
    pub scaling_interval: Duration,
    
    /// Target utilization for auto-scaling
    pub target_utilization: f64,
    
    /// Enable connection health checking
    pub enable_health_check: bool,
    
    /// Health check interval
    pub health_check_interval: Duration,
    
    /// Maximum retries for failed connections
    pub max_retries: u32,
    
    /// Retry delay
    pub retry_delay: Duration,
}

/// Connection pool for a specific backend server
pub struct ConnectionPool {
    /// Backend server address
    backend_address: SocketAddr,
    
    /// Active connections (currently in use)
    active_connections: Arc<RwLock<HashMap<String, PooledConnection>>>,
    
    /// Idle connections (available for use)
    idle_connections: Arc<Mutex<Vec<PooledConnection>>>,
    
    /// Connection semaphore (limits total connections)
    connection_semaphore: Arc<Semaphore>,
    
    /// Pool configuration
    config: ConnectionPoolConfig,
    
    /// Pool statistics
    stats: Arc<RwLock<PoolStats>>,
    
    /// Connection factory
    connection_factory: Arc<ConnectionFactory>,
}

/// A pooled connection with metadata
#[derive(Debug, Clone)]
pub struct PooledConnection {
    /// Unique connection ID
    pub id: String,
    
    /// The actual TCP connection
    pub stream: Arc<Mutex<Option<TcpStream>>>,
    
    /// Connection creation time
    pub created_at: Instant,
    
    /// Last used time
    pub last_used: Instant,
    
    /// Connection state
    pub state: ConnectionState,
    
    /// Number of times this connection has been used
    pub use_count: u64,
    
    /// Backend address this connection is for
    pub backend_address: SocketAddr,
    
    /// Connection health status
    pub health_status: ConnectionHealth,
}

/// Connection states
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum ConnectionState {
    /// Connection is being established
    Connecting,
    
    /// Connection is ready for use
    Ready,
    
    /// Connection is currently in use
    InUse,
    
    /// Connection is idle and available
    Idle,
    
    /// Connection is being validated
    Validating,
    
    /// Connection is broken and should be discarded
    Broken,
    
    /// Connection is being closed
    Closing,
}

/// Connection health status
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum ConnectionHealth {
    /// Connection health is unknown
    Unknown,
    
    /// Connection is healthy
    Healthy,
    
    /// Connection is unhealthy
    Unhealthy,
    
    /// Connection health check is in progress
    Checking,
}

/// Pool statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStats {
    /// Total connections created
    pub total_created: u64,
    
    /// Total connections destroyed
    pub total_destroyed: u64,
    
    /// Current active connections
    pub active_count: usize,
    
    /// Current idle connections
    pub idle_count: usize,
    
    /// Total connection requests
    pub total_requests: u64,
    
    /// Total connection hits (reused connections)
    pub total_hits: u64,
    
    /// Total connection misses (new connections)
    pub total_misses: u64,
    
    /// Average connection lifetime
    pub avg_lifetime: Duration,
    
    /// Average connection use count
    pub avg_use_count: f64,
    
    /// Pool utilization (0.0 - 1.0)
    pub utilization: f64,
    
    /// Last updated timestamp
    pub last_updated: Instant,
}

impl ConnectionPoolManager {
    /// Create a new connection pool manager
    pub fn new(config: ConnectionPoolConfig) -> Self {
        let health_monitor = Arc::new(PoolHealthMonitor::new(config.clone()));
        
        info!("🏊 Connection pool manager initialized (max: {}, min: {})", 
              config.max_connections, config.min_connections);
        
        Self {
            pools: Arc::new(RwLock::new(HashMap::new())),
            config,
            metrics: None,
            health_monitor,
        }
    }
    
    /// Set metrics collector
    pub fn with_metrics(mut self, metrics: Arc<LoadBalancerMetrics>) -> Self {
        self.metrics = Some(metrics);
        self
    }
    
    /// Start connection pool management services
    pub async fn start(&self) -> Result<()> {
        // Start health monitoring
        self.health_monitor.start(self.pools.clone()).await?;
        
        // Start auto-scaling if enabled
        if self.config.enable_auto_scaling {
            self.start_auto_scaling().await;
        }
        
        // Start connection warming if enabled
        if self.config.enable_warming {
            self.start_connection_warming().await;
        }
        
        // Start metrics collection
        self.start_metrics_collection().await;
        
        info!("✅ Connection pool management services started");
        Ok(())
    }
    
    /// Add a backend server to the pool manager
    pub async fn add_backend(&self, backend_address: SocketAddr) -> Result<()> {
        let mut pools = self.pools.write().await;
        
        if pools.contains_key(&backend_address) {
            return Ok(()); // Pool already exists
        }
        
        let connection_factory = Arc::new(ConnectionFactory::new(backend_address, self.config.clone()));
        let pool = Arc::new(ConnectionPool::new(backend_address, self.config.clone(), connection_factory));
        
        // Initialize minimum connections
        pool.initialize_min_connections().await?;
        
        pools.insert(backend_address, pool);
        
        info!("➕ Added connection pool for backend: {}", backend_address);
        Ok(())
    }
    
    /// Remove a backend server from the pool manager
    pub async fn remove_backend(&self, backend_address: SocketAddr) -> Result<()> {
        let mut pools = self.pools.write().await;
        
        if let Some(pool) = pools.remove(&backend_address) {
            // Gracefully close all connections in the pool
            pool.close_all_connections().await?;
            
            info!("➖ Removed connection pool for backend: {}", backend_address);
        }
        
        Ok(())
    }
    
    /// Get a connection from the pool for a specific backend
    pub async fn get_connection(&self, backend_address: SocketAddr) -> Result<PooledConnection> {
        let pools = self.pools.read().await;
        
        if let Some(pool) = pools.get(&backend_address) {
            let connection = pool.get_connection().await?;
            
            // Record metrics
            if let Some(ref metrics) = self.metrics {
                metrics.update_connection_pool(
                    &backend_address.to_string(),
                    pool.get_active_count().await,
                    pool.get_idle_count().await,
                );
            }
            
            Ok(connection)
        } else {
            Err(LoadBalancerError::Backend {
                message: format!("No connection pool found for backend: {}", backend_address),
                backend_address,
                status_code: None,
                response_time: None,
                error_type: crate::error::BackendErrorType::ConnectionRefused,
            })
        }
    }
    
    /// Return a connection to the pool
    pub async fn return_connection(&self, connection: PooledConnection) -> Result<()> {
        let pools = self.pools.read().await;
        
        if let Some(pool) = pools.get(&connection.backend_address) {
            pool.return_connection(connection).await?;
        }
        
        Ok(())
    }
    
    /// Get pool statistics for all backends
    pub async fn get_pool_stats(&self) -> HashMap<SocketAddr, PoolStats> {
        let pools = self.pools.read().await;
        let mut stats = HashMap::new();
        
        for (addr, pool) in pools.iter() {
            stats.insert(*addr, pool.get_stats().await);
        }
        
        stats
    }
    
    /// Start auto-scaling task
    async fn start_auto_scaling(&self) {
        let pools = self.pools.clone();
        let config = self.config.clone();
        let metrics = self.metrics.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.scaling_interval);
            
            info!("📈 Starting connection pool auto-scaling");
            
            loop {
                interval.tick().await;
                
                let pools_guard = pools.read().await;
                for (addr, pool) in pools_guard.iter() {
                    if let Err(e) = Self::scale_pool(pool, &config, &metrics).await {
                        warn!("Failed to scale pool for {}: {}", addr, e);
                    }
                }
            }
        });
    }
    
    /// Scale a specific pool based on utilization
    async fn scale_pool(
        pool: &Arc<ConnectionPool>,
        config: &ConnectionPoolConfig,
        metrics: &Option<Arc<LoadBalancerMetrics>>,
    ) -> Result<()> {
        let stats = pool.get_stats().await;
        let current_total = stats.active_count + stats.idle_count;
        
        // Scale up if utilization is high
        if stats.utilization > config.target_utilization && current_total < config.max_connections {
            let scale_up_count = ((config.max_connections - current_total) / 4).max(1);
            pool.scale_up(scale_up_count).await?;
            
            debug!("📈 Scaled up pool by {} connections (utilization: {:.2})", 
                   scale_up_count, stats.utilization);
            
            if let Some(ref metrics) = metrics {
                // Record scaling metrics
                trace!("Recording pool scale-up metrics");
            }
        }
        // Scale down if utilization is low
        else if stats.utilization < config.target_utilization * 0.5 && current_total > config.min_connections {
            let scale_down_count = ((current_total - config.min_connections) / 4).max(1);
            pool.scale_down(scale_down_count).await?;
            
            debug!("📉 Scaled down pool by {} connections (utilization: {:.2})", 
                   scale_down_count, stats.utilization);
            
            if let Some(ref metrics) = metrics {
                // Record scaling metrics
                trace!("Recording pool scale-down metrics");
            }
        }
        
        Ok(())
    }
    
    /// Start connection warming task
    async fn start_connection_warming(&self) {
        let pools = self.pools.clone();
        let config = self.config.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30));
            
            info!("🔥 Starting connection pool warming");
            
            loop {
                interval.tick().await;
                
                let pools_guard = pools.read().await;
                for pool in pools_guard.values() {
                    if let Err(e) = pool.warm_connections().await {
                        debug!("Failed to warm connections: {}", e);
                    }
                }
            }
        });
    }
    
    /// Start metrics collection task
    async fn start_metrics_collection(&self) {
        let pools = self.pools.clone();
        let metrics = self.metrics.clone();
        
        if let Some(metrics) = metrics {
            tokio::spawn(async move {
                let mut interval = tokio::time::interval(Duration::from_secs(15));
                
                loop {
                    interval.tick().await;
                    
                    let pools_guard = pools.read().await;
                    for (addr, pool) in pools_guard.iter() {
                        let stats = pool.get_stats().await;
                        
                        metrics.update_connection_pool(
                            &addr.to_string(),
                            stats.active_count,
                            stats.idle_count,
                        );
                    }
                }
            });
        }
    }
}

impl ConnectionPool {
    /// Create a new connection pool
    pub fn new(
        backend_address: SocketAddr,
        config: ConnectionPoolConfig,
        connection_factory: Arc<ConnectionFactory>,
    ) -> Self {
        let connection_semaphore = Arc::new(Semaphore::new(config.max_connections));
        
        Self {
            backend_address,
            active_connections: Arc::new(RwLock::new(HashMap::new())),
            idle_connections: Arc::new(Mutex::new(Vec::new())),
            connection_semaphore,
            config,
            stats: Arc::new(RwLock::new(PoolStats::default())),
            connection_factory,
        }
    }
    
    /// Initialize minimum connections
    pub async fn initialize_min_connections(&self) -> Result<()> {
        for _ in 0..self.config.min_connections {
            let connection = self.connection_factory.create_connection().await?;
            let mut idle_connections = self.idle_connections.lock().await;
            idle_connections.push(connection);
        }
        
        debug!("🏊 Initialized {} minimum connections for {}", 
               self.config.min_connections, self.backend_address);
        
        Ok(())
    }
    
    /// Get a connection from the pool
    pub async fn get_connection(&self) -> Result<PooledConnection> {
        // Try to get an idle connection first
        {
            let mut idle_connections = self.idle_connections.lock().await;
            if let Some(mut connection) = idle_connections.pop() {
                // Validate connection before use
                if self.validate_connection(&connection).await? {
                    connection.state = ConnectionState::InUse;
                    connection.last_used = Instant::now();
                    connection.use_count += 1;
                    
                    // Move to active connections
                    {
                        let mut active_connections = self.active_connections.write().await;
                        active_connections.insert(connection.id.clone(), connection.clone());
                    }
                    
                    // Update stats
                    {
                        let mut stats = self.stats.write().await;
                        stats.total_requests += 1;
                        stats.total_hits += 1;
                        stats.active_count += 1;
                        stats.idle_count = idle_connections.len();
                        stats.utilization = stats.active_count as f64 / self.config.max_connections as f64;
                        stats.last_updated = Instant::now();
                    }
                    
                    trace!("♻️ Reused idle connection {} for {}", connection.id, self.backend_address);
                    return Ok(connection);
                }
            }
        }
        
        // No idle connections available, create a new one
        let _permit = self.connection_semaphore.acquire().await
            .map_err(|_| LoadBalancerError::Resource {
                message: "Failed to acquire connection semaphore".to_string(),
                resource_type: crate::error::ResourceType::Connections,
                current_usage: None,
                limit: Some(self.config.max_connections as u64),
            })?;
        
        let mut connection = self.connection_factory.create_connection().await?;
        connection.state = ConnectionState::InUse;
        connection.last_used = Instant::now();
        connection.use_count += 1;
        
        // Add to active connections
        {
            let mut active_connections = self.active_connections.write().await;
            active_connections.insert(connection.id.clone(), connection.clone());
        }
        
        // Update stats
        {
            let mut stats = self.stats.write().await;
            stats.total_requests += 1;
            stats.total_misses += 1;
            stats.total_created += 1;
            stats.active_count += 1;
            stats.utilization = stats.active_count as f64 / self.config.max_connections as f64;
            stats.last_updated = Instant::now();
        }
        
        debug!("🆕 Created new connection {} for {}", connection.id, self.backend_address);
        Ok(connection)
    }
    
    /// Return a connection to the pool
    pub async fn return_connection(&self, mut connection: PooledConnection) -> Result<()> {
        // Remove from active connections
        {
            let mut active_connections = self.active_connections.write().await;
            active_connections.remove(&connection.id);
        }
        
        // Check if connection should be discarded
        if self.should_discard_connection(&connection) {
            self.discard_connection(connection).await?;
            return Ok(());
        }
        
        // Return to idle pool
        connection.state = ConnectionState::Idle;
        {
            let mut idle_connections = self.idle_connections.lock().await;
            idle_connections.push(connection);
        }
        
        // Update stats
        {
            let mut stats = self.stats.write().await;
            stats.active_count -= 1;
            let idle_count = self.idle_connections.lock().await.len();
            stats.idle_count = idle_count;
            stats.utilization = stats.active_count as f64 / self.config.max_connections as f64;
            stats.last_updated = Instant::now();
        }
        
        Ok(())
    }
    
    /// Validate a connection before use
    async fn validate_connection(&self, connection: &PooledConnection) -> Result<bool> {
        // Check connection age
        if connection.created_at.elapsed() > self.config.max_lifetime {
            debug!("Connection {} expired (age: {:?})", connection.id, connection.created_at.elapsed());
            return Ok(false);
        }
        
        // Check idle time
        if connection.last_used.elapsed() > self.config.idle_timeout {
            debug!("Connection {} idle timeout (idle: {:?})", connection.id, connection.last_used.elapsed());
            return Ok(false);
        }
        
        // Check connection health if enabled
        if self.config.enable_health_check {
            // In a real implementation, you'd perform actual health check
            // For now, simulate health check
            if connection.health_status == ConnectionHealth::Unhealthy {
                debug!("Connection {} failed health check", connection.id);
                return Ok(false);
            }
        }
        
        Ok(true)
    }
    
    /// Check if a connection should be discarded
    fn should_discard_connection(&self, connection: &PooledConnection) -> bool {
        // Discard if connection is broken
        if connection.state == ConnectionState::Broken {
            return true;
        }
        
        // Discard if connection is too old
        if connection.created_at.elapsed() > self.config.max_lifetime {
            return true;
        }
        
        // Discard if connection has been used too many times
        if connection.use_count > 1000 {
            return true;
        }
        
        false
    }
    
    /// Discard a connection
    async fn discard_connection(&self, connection: PooledConnection) -> Result<()> {
        // Close the connection
        if let Some(stream) = connection.stream.lock().await.take() {
            drop(stream); // Close the TCP stream
        }
        
        // Update stats
        {
            let mut stats = self.stats.write().await;
            stats.total_destroyed += 1;
        }
        
        trace!("🗑️ Discarded connection {} for {}", connection.id, self.backend_address);
        Ok(())
    }
    
    /// Warm connections (create connections proactively)
    pub async fn warm_connections(&self) -> Result<()> {
        let current_idle = self.idle_connections.lock().await.len();
        let threshold = (self.config.max_connections as f64 * self.config.warming_threshold) as usize;
        
        if current_idle < threshold {
            let connections_to_create = threshold - current_idle;
            
            for _ in 0..connections_to_create {
                if let Ok(connection) = self.connection_factory.create_connection().await {
                    let mut idle_connections = self.idle_connections.lock().await;
                    idle_connections.push(connection);
                }
            }
            
            trace!("🔥 Warmed {} connections for {}", connections_to_create, self.backend_address);
        }
        
        Ok(())
    }
    
    /// Scale up the pool
    pub async fn scale_up(&self, count: usize) -> Result<()> {
        for _ in 0..count {
            if let Ok(connection) = self.connection_factory.create_connection().await {
                let mut idle_connections = self.idle_connections.lock().await;
                idle_connections.push(connection);
            }
        }
        
        Ok(())
    }
    
    /// Scale down the pool
    pub async fn scale_down(&self, count: usize) -> Result<()> {
        let mut idle_connections = self.idle_connections.lock().await;
        
        for _ in 0..count.min(idle_connections.len()) {
            if let Some(connection) = idle_connections.pop() {
                self.discard_connection(connection).await?;
            }
        }
        
        Ok(())
    }
    
    /// Close all connections in the pool
    pub async fn close_all_connections(&self) -> Result<()> {
        // Close all idle connections
        {
            let mut idle_connections = self.idle_connections.lock().await;
            while let Some(connection) = idle_connections.pop() {
                self.discard_connection(connection).await?;
            }
        }
        
        // Close all active connections
        {
            let mut active_connections = self.active_connections.write().await;
            for (_, connection) in active_connections.drain() {
                self.discard_connection(connection).await?;
            }
        }
        
        info!("🔒 Closed all connections for {}", self.backend_address);
        Ok(())
    }
    
    /// Get current active connection count
    pub async fn get_active_count(&self) -> usize {
        self.active_connections.read().await.len()
    }
    
    /// Get current idle connection count
    pub async fn get_idle_count(&self) -> usize {
        self.idle_connections.lock().await.len()
    }
    
    /// Get pool statistics
    pub async fn get_stats(&self) -> PoolStats {
        let mut stats = self.stats.read().await.clone();
        
        // Update current counts
        stats.active_count = self.get_active_count().await;
        stats.idle_count = self.get_idle_count().await;
        stats.utilization = stats.active_count as f64 / self.config.max_connections as f64;
        
        stats
    }
}

/// Connection factory for creating new connections
pub struct ConnectionFactory {
    /// Backend address to connect to
    backend_address: SocketAddr,

    /// Connection configuration
    config: ConnectionPoolConfig,
}

impl ConnectionFactory {
    pub fn new(backend_address: SocketAddr, config: ConnectionPoolConfig) -> Self {
        Self {
            backend_address,
            config,
        }
    }

    /// Create a new connection to the backend
    pub async fn create_connection(&self) -> Result<PooledConnection> {
        let connection_id = Uuid::new_v4().to_string();
        let start_time = Instant::now();

        // Attempt to establish TCP connection with timeout
        let stream = tokio::time::timeout(
            self.config.connection_timeout,
            TcpStream::connect(self.backend_address)
        ).await
        .map_err(|_| LoadBalancerError::Timeout {
            message: "Connection timeout".to_string(),
            operation: "tcp_connect".to_string(),
            duration: start_time.elapsed(),
            timeout_limit: self.config.connection_timeout,
        })?
        .map_err(|e| LoadBalancerError::Network {
            message: format!("Failed to connect to backend: {}", e),
            address: Some(self.backend_address),
            error_code: Some(e.kind().to_string()),
            retry_after: Some(self.config.retry_delay),
        })?;

        // Configure TCP socket options
        if let Err(e) = self.configure_socket(&stream) {
            warn!("Failed to configure socket options: {}", e);
        }

        let connection = PooledConnection {
            id: connection_id.clone(),
            stream: Arc::new(Mutex::new(Some(stream))),
            created_at: Instant::now(),
            last_used: Instant::now(),
            state: ConnectionState::Ready,
            use_count: 0,
            backend_address: self.backend_address,
            health_status: ConnectionHealth::Unknown,
        };

        debug!("🔗 Created connection {} to {} in {:?}",
               connection_id, self.backend_address, start_time.elapsed());

        Ok(connection)
    }

    /// Configure socket options for optimal performance
    fn configure_socket(&self, stream: &TcpStream) -> Result<()> {
        use std::os::unix::io::AsRawFd;
        use libc::{setsockopt, SOL_SOCKET, SO_KEEPALIVE, SO_REUSEADDR, IPPROTO_TCP, TCP_NODELAY};

        let fd = stream.as_raw_fd();

        // Enable TCP keepalive
        let keepalive = 1i32;
        unsafe {
            setsockopt(
                fd,
                SOL_SOCKET,
                SO_KEEPALIVE,
                &keepalive as *const _ as *const libc::c_void,
                std::mem::size_of::<i32>() as libc::socklen_t,
            );
        }

        // Enable address reuse
        let reuse = 1i32;
        unsafe {
            setsockopt(
                fd,
                SOL_SOCKET,
                SO_REUSEADDR,
                &reuse as *const _ as *const libc::c_void,
                std::mem::size_of::<i32>() as libc::socklen_t,
            );
        }

        // Disable Nagle's algorithm for lower latency
        let nodelay = 1i32;
        unsafe {
            setsockopt(
                fd,
                IPPROTO_TCP,
                TCP_NODELAY,
                &nodelay as *const _ as *const libc::c_void,
                std::mem::size_of::<i32>() as libc::socklen_t,
            );
        }

        Ok(())
    }
}

/// Pool health monitor
pub struct PoolHealthMonitor {
    config: ConnectionPoolConfig,
}

impl PoolHealthMonitor {
    pub fn new(config: ConnectionPoolConfig) -> Self {
        Self { config }
    }

    pub async fn start(&self, pools: Arc<RwLock<HashMap<SocketAddr, Arc<ConnectionPool>>>>) -> Result<()> {
        if !self.config.enable_health_check {
            return Ok(());
        }

        let config = self.config.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(config.health_check_interval);

            info!("🏥 Starting connection pool health monitoring");

            loop {
                interval.tick().await;

                let pools_guard = pools.read().await;
                for (addr, pool) in pools_guard.iter() {
                    if let Err(e) = Self::check_pool_health(pool).await {
                        warn!("Health check failed for pool {}: {}", addr, e);
                    }
                }
            }
        });

        Ok(())
    }

    async fn check_pool_health(pool: &Arc<ConnectionPool>) -> Result<()> {
        // Check idle connections health
        let mut idle_connections = pool.idle_connections.lock().await;
        let mut unhealthy_connections = Vec::new();

        for (index, connection) in idle_connections.iter_mut().enumerate() {
            if !Self::is_connection_healthy(connection).await {
                unhealthy_connections.push(index);
            }
        }

        // Remove unhealthy connections (in reverse order to maintain indices)
        for &index in unhealthy_connections.iter().rev() {
            if let Some(connection) = idle_connections.get(index) {
                debug!("🏥 Removing unhealthy connection: {}", connection.id);
            }
            idle_connections.remove(index);
        }

        Ok(())
    }

    async fn is_connection_healthy(connection: &PooledConnection) -> bool {
        // Simple health check - in production, you might send a ping or small request
        if let Ok(stream_guard) = connection.stream.try_lock() {
            if stream_guard.is_some() {
                // Connection exists and is not in use
                return true;
            }
        }
        false
    }
}

impl Default for PoolStats {
    fn default() -> Self {
        Self {
            total_created: 0,
            total_destroyed: 0,
            active_count: 0,
            idle_count: 0,
            total_requests: 0,
            total_hits: 0,
            total_misses: 0,
            avg_lifetime: Duration::from_secs(0),
            avg_use_count: 0.0,
            utilization: 0.0,
            last_updated: Instant::now(),
        }
    }
}

impl Default for ConnectionPoolConfig {
    fn default() -> Self {
        Self {
            min_connections: 5,
            max_connections: 100,
            connection_timeout: Duration::from_secs(10),
            idle_timeout: Duration::from_secs(300), // 5 minutes
            max_lifetime: Duration::from_secs(3600), // 1 hour
            validation_interval: Duration::from_secs(30),
            enable_warming: true,
            warming_threshold: 0.2, // 20%
            enable_auto_scaling: true,
            scaling_interval: Duration::from_secs(60),
            target_utilization: 0.7, // 70%
            enable_health_check: true,
            health_check_interval: Duration::from_secs(30),
            max_retries: 3,
            retry_delay: Duration::from_secs(1),
        }
    }
}
```

## Integration Example

Create `examples/connection_pooling_example.rs`:

```rust
//! Example demonstrating advanced connection pooling with monitoring

use rusty_balancer::pool::{ConnectionPoolManager, ConnectionPoolConfig};
use std::net::SocketAddr;
use std::time::Duration;
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("rusty_balancer=info")
        .init();

    info!("🏊 Starting Connection Pooling Example");

    // Create connection pool configuration
    let config = ConnectionPoolConfig {
        min_connections: 3,
        max_connections: 20,
        connection_timeout: Duration::from_secs(5),
        idle_timeout: Duration::from_secs(120),
        max_lifetime: Duration::from_secs(600),
        enable_warming: true,
        warming_threshold: 0.3,
        enable_auto_scaling: true,
        scaling_interval: Duration::from_secs(30),
        target_utilization: 0.8,
        enable_health_check: true,
        health_check_interval: Duration::from_secs(15),
        ..Default::default()
    };

    // Create connection pool manager
    let pool_manager = ConnectionPoolManager::new(config);
    pool_manager.start().await?;

    // Add backend servers
    let backends = vec![
        "127.0.0.1:8081".parse::<SocketAddr>()?,
        "127.0.0.1:8082".parse::<SocketAddr>()?,
        "127.0.0.1:8083".parse::<SocketAddr>()?,
    ];

    for backend in &backends {
        pool_manager.add_backend(*backend).await?;
        info!("Added backend: {}", backend);
    }

    // Simulate connection usage patterns
    info!("🔄 Simulating connection usage patterns");

    // Pattern 1: Normal load
    info!("📊 Pattern 1: Normal load");
    for i in 0..10 {
        let backend = backends[i % backends.len()];

        match pool_manager.get_connection(backend).await {
            Ok(connection) => {
                info!("Got connection {} for {}", connection.id, backend);

                // Simulate work
                tokio::time::sleep(Duration::from_millis(100)).await;

                // Return connection to pool
                pool_manager.return_connection(connection).await?;
            }
            Err(e) => error!("Failed to get connection: {}", e),
        }

        tokio::time::sleep(Duration::from_millis(50)).await;
    }

    // Show pool statistics after normal load
    let stats = pool_manager.get_pool_stats().await;
    info!("📈 Pool Statistics after normal load:");
    for (addr, stat) in &stats {
        info!("  {}: Active: {}, Idle: {}, Utilization: {:.2}%, Hits: {}, Misses: {}",
              addr, stat.active_count, stat.idle_count, stat.utilization * 100.0,
              stat.total_hits, stat.total_misses);
    }

    // Pattern 2: Burst load
    info!("📊 Pattern 2: Burst load");
    let mut connections = Vec::new();

    // Get many connections quickly
    for i in 0..15 {
        let backend = backends[i % backends.len()];

        match pool_manager.get_connection(backend).await {
            Ok(connection) => {
                info!("Burst connection {} for {}", connection.id, backend);
                connections.push(connection);
            }
            Err(e) => error!("Failed to get burst connection: {}", e),
        }
    }

    // Hold connections for a while to simulate high load
    tokio::time::sleep(Duration::from_secs(2)).await;

    // Return all connections
    for connection in connections {
        pool_manager.return_connection(connection).await?;
    }

    // Show pool statistics after burst load
    let stats = pool_manager.get_pool_stats().await;
    info!("📈 Pool Statistics after burst load:");
    for (addr, stat) in &stats {
        info!("  {}: Active: {}, Idle: {}, Utilization: {:.2}%, Total Created: {}",
              addr, stat.active_count, stat.idle_count, stat.utilization * 100.0,
              stat.total_created);
    }

    // Pattern 3: Connection reuse test
    info!("📊 Pattern 3: Connection reuse test");
    let backend = backends[0];

    for i in 0..5 {
        match pool_manager.get_connection(backend).await {
            Ok(connection) => {
                info!("Reuse test {}: Connection {} (use count: {})",
                      i + 1, connection.id, connection.use_count);

                // Quick work simulation
                tokio::time::sleep(Duration::from_millis(10)).await;

                pool_manager.return_connection(connection).await?;
            }
            Err(e) => error!("Failed to get connection for reuse test: {}", e),
        }
    }

    // Wait for auto-scaling and warming to potentially occur
    info!("⏳ Waiting for auto-scaling and warming...");
    tokio::time::sleep(Duration::from_secs(5)).await;

    // Final statistics
    let stats = pool_manager.get_pool_stats().await;
    info!("📊 Final Pool Statistics:");
    for (addr, stat) in &stats {
        info!("  Backend: {}", addr);
        info!("    Active: {}, Idle: {}", stat.active_count, stat.idle_count);
        info!("    Utilization: {:.2}%", stat.utilization * 100.0);
        info!("    Total Requests: {}", stat.total_requests);
        info!("    Cache Hit Rate: {:.2}%",
              if stat.total_requests > 0 {
                  (stat.total_hits as f64 / stat.total_requests as f64) * 100.0
              } else {
                  0.0
              });
        info!("    Connections Created: {}, Destroyed: {}",
              stat.total_created, stat.total_destroyed);
        info!("    Average Use Count: {:.2}", stat.avg_use_count);
    }

    // Test backend removal
    info!("🗑️ Testing backend removal");
    let backend_to_remove = backends[2];
    pool_manager.remove_backend(backend_to_remove).await?;
    info!("Removed backend: {}", backend_to_remove);

    // Verify backend was removed
    let stats = pool_manager.get_pool_stats().await;
    if !stats.contains_key(&backend_to_remove) {
        info!("✅ Backend successfully removed from pool manager");
    } else {
        error!("❌ Backend still present in pool manager");
    }

    info!("✅ Connection pooling example completed");

    Ok(())
}
```

## Key Design Decisions Explained

### 1. Per-Backend Connection Pools
**Decision**: Maintain separate connection pools for each backend server

**Rationale**:
- **Isolation**: Failures in one backend don't affect connections to others
- **Optimization**: Tune pool settings based on individual backend characteristics
- **Monitoring**: Track performance metrics per backend for better insights
- **Scaling**: Scale pools independently based on backend capacity and load

### 2. Connection Lifecycle Management
**Decision**: Comprehensive connection state tracking and validation

**Rationale**:
- **Reliability**: Detect and replace broken connections before they cause errors
- **Performance**: Maintain optimal pool size based on current load patterns
- **Resource Management**: Clean up idle and expired connections automatically
- **Health**: Validate connections proactively to prevent request failures

### 3. Auto-Scaling and Connection Warming
**Decision**: Dynamic pool sizing based on utilization and proactive connection creation

**Rationale**:
- **Performance**: Reduce connection establishment latency during traffic spikes
- **Efficiency**: Scale down during low traffic to conserve resources
- **Responsiveness**: Maintain optimal pool size for current load patterns
- **User Experience**: Minimize connection delays for end users

## Performance Considerations

1. **Connection Reuse**: Maximize connection reuse to amortize establishment costs
2. **Pool Sizing**: Balance between resource usage and connection availability
3. **Validation Overhead**: Minimize health check impact on performance
4. **Memory Management**: Efficient storage and cleanup of connection metadata

## Security Considerations

1. **Connection Limits**: Prevent resource exhaustion through proper limits
2. **Timeout Management**: Avoid hanging connections and resource leaks
3. **Health Validation**: Detect compromised or hijacked connections
4. **Access Control**: Secure connection pool management interfaces

## Navigation
- [Previous: Rate Limiting](15-rate-limiting.md)
- [Next: Circuit Breaking](17-circuit-breaking.md)
